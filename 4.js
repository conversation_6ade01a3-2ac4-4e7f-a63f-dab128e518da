(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{1e3:function(t,e,r){"use strict";var i=r(794),n=r(812),s=r(797),a=r(844),o=r(927),u=TypeError;t.exports=function(t,e){var r=arguments.length<2?o(t):e;if(n(r))return s(i(r,t));throw new u(a(t)+" is not iterable")}},1001:function(t,e,r){"use strict";var i=r(794),n=r(797),s=r(823);t.exports=function(t,e,r){var a,o;n(t);try{if(!(a=s(t,"return"))){if("throw"===e)throw r;return r}a=i(a,t)}catch(t){o=!0,a=t}if("throw"===e)throw r;if(o)throw a;return n(a),r}},1002:function(t,e,r){"use strict";var i=r(792)("iterator"),n=!1;try{var s=0,a={next:function(){return{done:!!s++}},return:function(){n=!0}};a[i]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!n)return!1}catch(t){return!1}var r=!1;try{var s={};s[i]=function(){return{next:function(){return{done:r=!0}}}},t(s)}catch(t){}return r}},1003:function(t,e,r){"use strict";var i=r(796),n=r(808),s=r(831).CONSTRUCTOR,a=r(830),o=r(818),u=r(788),c=r(819),h=a&&a.prototype;if(i({target:"Promise",proto:!0,forced:s,real:!0},{catch:function(t){return this.then(void 0,t)}}),!n&&u(a)){var l=o("Promise").prototype.catch;h.catch!==l&&c(h,"catch",l,{unsafe:!0})}},1004:function(t,e,r){"use strict";var i=r(796),n=r(794),s=r(812),a=r(832),o=r(880),u=r(926);i({target:"Promise",stat:!0,forced:r(928)},{race:function(t){var e=this,r=a.f(e),i=r.reject,c=o((function(){var a=s(e.resolve);u(t,(function(t){n(a,e,t).then(r.resolve,i)}))}));return c.error&&i(c.value),r.promise}})},1005:function(t,e,r){"use strict";var i=r(796),n=r(832);i({target:"Promise",stat:!0,forced:r(831).CONSTRUCTOR},{reject:function(t){var e=n.f(this);return(0,e.reject)(t),e.promise}})},1006:function(t,e,r){"use strict";var i=r(796),n=r(818),s=r(808),a=r(830),o=r(831).CONSTRUCTOR,u=r(1007),c=n("Promise"),h=s&&!o;i({target:"Promise",stat:!0,forced:s||o},{resolve:function(t){return u(h&&this===c?a:this,t)}})},1007:function(t,e,r){"use strict";var i=r(797),n=r(803),s=r(832);t.exports=function(t,e){if(i(t),n(e)&&e.constructor===t)return e;var r=s.f(t);return(0,r.resolve)(e),r.promise}},1008:function(t,e){function r(t,e,r,i,n,s,a){try{var o=t[s](a),u=o.value}catch(t){return void r(t)}o.done?e(u):Promise.resolve(u).then(i,n)}t.exports=function(t){return function(){var e=this,i=arguments;return new Promise((function(n,s){var a=t.apply(e,i);function o(t){r(a,n,s,o,u,"next",t)}function u(t){r(a,n,s,o,u,"throw",t)}o(void 0)}))}},t.exports.__esModule=!0,t.exports.default=t.exports},1009:function(t,e,r){"use strict";var i=r(794),n=r(881),s=r(797),a=r(817),o=r(824),u=r(809),c=r(802),h=r(823),l=r(883),f=r(884);n("match",(function(t,e,r){return[function(e){var r=c(this),n=a(e)?void 0:h(e,t);return n?i(n,e,r):new RegExp(e)[t](u(r))},function(t){var i=s(this),n=u(t),a=r(e,i,n);if(a.done)return a.value;if(!i.global)return f(i,n);var c=i.unicode;i.lastIndex=0;for(var h,g=[],p=0;null!==(h=f(i,n));){var d=u(h[0]);g[p]=d,""===d&&(i.lastIndex=l(n,o(i.lastIndex),c)),p++}return 0===p?null:g}]}))},1010:function(t,e,r){"use strict";var i=r(796),n=r(882);i({target:"RegExp",proto:!0,forced:/./.exec!==n},{exec:n})},1011:function(t,e,r){"use strict";var i=r(801),n=r(913),s=r(814),a=r(797),o=r(827),u=r(1012);e.f=i&&!n?Object.defineProperties:function(t,e){a(t);for(var r,i=o(e),n=u(e),c=n.length,h=0;c>h;)s.f(t,r=n[h++],i[r]);return t}},1012:function(t,e,r){"use strict";var i=r(915),n=r(877);t.exports=Object.keys||function(t){return i(t,n)}},1013:function(t,e,r){"use strict";var i=r(786),n=r(784).RegExp;t.exports=i((function(){var t=n(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)}))},1014:function(t,e,r){"use strict";var i=r(786),n=r(784).RegExp;t.exports=i((function(){var t=n("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},1015:function(t,e,r){"use strict";var i=r(787),n=r(849),s=r(809),a=r(802),o=i("".charAt),u=i("".charCodeAt),c=i("".slice),h=function(t){return function(e,r){var i,h,l=s(a(e)),f=n(r),g=l.length;return f<0||f>=g?t?"":void 0:(i=u(l,f))<55296||i>56319||f+1===g||(h=u(l,f+1))<56320||h>57343?t?o(l,f):i:t?c(l,f,f+2):h-56320+(i-55296<<10)+65536}};t.exports={codeAt:h(!1),charAt:h(!0)}},1016:function(t,e,r){"use strict";var i=r(922),n=r(794),s=r(787),a=r(881),o=r(786),u=r(797),c=r(788),h=r(817),l=r(849),f=r(824),g=r(809),p=r(802),d=r(883),v=r(823),y=r(1017),m=r(884),x=r(792)("replace"),b=Math.max,S=Math.min,w=s([].concat),P=s([].push),A=s("".indexOf),C=s("".slice),E="$0"==="a".replace(/./,"$0"),T=!!/./[x]&&""===/./[x]("a","$0");a("replace",(function(t,e,r){var s=T?"$":"$0";return[function(t,r){var i=p(this),s=h(t)?void 0:v(t,x);return s?n(s,t,i,r):n(e,g(i),t,r)},function(t,n){var a=u(this),o=g(t);if("string"==typeof n&&-1===A(n,s)&&-1===A(n,"$<")){var h=r(e,a,o,n);if(h.done)return h.value}var p=c(n);p||(n=g(n));var v,x=a.global;x&&(v=a.unicode,a.lastIndex=0);for(var E,T=[];null!==(E=m(a,o))&&(P(T,E),x);){""===g(E[0])&&(a.lastIndex=d(o,f(a.lastIndex),v))}for(var O,M="",V=0,k=0;k<T.length;k++){for(var N,R=g((E=T[k])[0]),I=b(S(l(E.index),o.length),0),L=[],D=1;D<E.length;D++)P(L,void 0===(O=E[D])?O:String(O));var j=E.groups;if(p){var B=w([R],L,I,o);void 0!==j&&P(B,j),N=g(i(n,void 0,B))}else N=y(R,o,I,L,j,n);I>=V&&(M+=C(o,V,I)+N,V=I+R.length)}return M+C(o,V)}]}),!!o((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!E||T)},1017:function(t,e,r){"use strict";var i=r(787),n=r(845),s=Math.floor,a=i("".charAt),o=i("".replace),u=i("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,h=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,r,i,l,f){var g=r+t.length,p=i.length,d=h;return void 0!==l&&(l=n(l),d=c),o(f,d,(function(n,o){var c;switch(a(o,0)){case"$":return"$";case"&":return t;case"`":return u(e,0,r);case"'":return u(e,g);case"<":c=l[u(o,1,-1)];break;default:var h=+o;if(0===h)return n;if(h>p){var f=s(h/10);return 0===f?n:f<=p?void 0===i[f-1]?a(o,1):i[f-1]+a(o,1):n}c=i[h-1]}return void 0===c?"":c}))}},1018:function(t,e,r){"use strict";var i,n=r(796),s=r(852),a=r(841).f,o=r(824),u=r(809),c=r(885),h=r(802),l=r(886),f=r(808),g=s("".slice),p=Math.min,d=l("startsWith");n({target:"String",proto:!0,forced:!!(f||d||(i=a(String.prototype,"startsWith"),!i||i.writable))&&!d},{startsWith:function(t){var e=u(h(this));c(t);var r=o(p(arguments.length>1?arguments[1]:void 0,e.length)),i=u(t);return g(e,r,r+i.length)===i}})},1019:function(t,e,r){"use strict";var i=r(803),n=r(816),s=r(792)("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[s])?!!e:"RegExp"===n(t))}},1020:function(t,e,r){"use strict";var i=r(792),n=r(853),s=r(814).f,a=i("unscopables"),o=Array.prototype;void 0===o[a]&&s(o,a,{configurable:!0,value:n(null)}),t.exports=function(t){o[a][t]=!0}},1021:function(t,e,r){"use strict";var i=r(796),n=r(794),s=r(808),a=r(847),o=r(788),u=r(1022),c=r(933),h=r(919),l=r(851),f=r(829),g=r(819),p=r(792),d=r(833),v=r(932),y=a.PROPER,m=a.CONFIGURABLE,x=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,S=p("iterator"),w=function(){return this};t.exports=function(t,e,r,a,p,v,P){u(r,e,a);var A,C,E,T=function(t){if(t===p&&N)return N;if(!b&&t&&t in V)return V[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},O=e+" Iterator",M=!1,V=t.prototype,k=V[S]||V["@@iterator"]||p&&V[p],N=!b&&k||T(p),R="Array"===e&&V.entries||k;if(R&&(A=c(R.call(new t)))!==Object.prototype&&A.next&&(s||c(A)===x||(h?h(A,x):o(A[S])||g(A,S,w)),l(A,O,!0,!0),s&&(d[O]=w)),y&&"values"===p&&k&&"values"!==k.name&&(!s&&m?f(V,"name","values"):(M=!0,N=function(){return n(k,this)})),p)if(C={values:T("values"),keys:v?N:T("keys"),entries:T("entries")},P)for(E in C)(b||M||!(E in V))&&g(V,E,C[E]);else i({target:e,proto:!0,forced:b||M},C);return s&&!P||V[S]===N||g(V,S,N,{name:p}),d[e]=N,C}},1022:function(t,e,r){"use strict";var i=r(932).IteratorPrototype,n=r(853),s=r(868),a=r(851),o=r(833),u=function(){return this};t.exports=function(t,e,r,c){var h=e+" Iterator";return t.prototype=n(i,{next:s(+!c,r)}),a(t,h,!1,!0),o[h]=u,t}},1023:function(t,e,r){"use strict";var i=r(786);t.exports=!i((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},1024:function(t,e,r){"use strict";t.exports=function(t,e){return{value:t,done:e}}},1025:function(t,e,r){"use strict";var i=r(784),n=r(1026),s=r(1027),a=r(931),o=r(829),u=r(851),c=r(792)("iterator"),h=a.values,l=function(t,e){if(t){if(t[c]!==h)try{o(t,c,h)}catch(e){t[c]=h}if(u(t,e,!0),n[e])for(var r in a)if(t[r]!==a[r])try{o(t,r,a[r])}catch(e){t[r]=a[r]}}};for(var f in n)l(i[f]&&i[f].prototype,f);l(s,"DOMTokenList")},1026:function(t,e,r){"use strict";t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},1027:function(t,e,r){"use strict";var i=r(846)("span").classList,n=i&&i.constructor&&i.constructor.prototype;t.exports=n===Object.prototype?void 0:n},1028:function(t,e,r){var i=r(1029);t.exports=function(t,e,r){return(e=i(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports.default=t.exports},1029:function(t,e,r){var i=r(50).default,n=r(1030);t.exports=function(t){var e=n(t,"string");return"symbol"==i(e)?e:e+""},t.exports.__esModule=!0,t.exports.default=t.exports},1030:function(t,e,r){var i=r(50).default;t.exports=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},1031:function(t,e,r){"use strict";var i=r(796),n=r(1032).left,s=r(934),a=r(869);i({target:"Array",proto:!0,forced:!r(850)&&a>79&&a<83||!s("reduce")},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},1032:function(t,e,r){"use strict";var i=r(812),n=r(845),s=r(906),a=r(876),o=TypeError,u="Reduce of empty array with no initial value",c=function(t){return function(e,r,c,h){var l=n(e),f=s(l),g=a(l);if(i(r),0===g&&c<2)throw new o(u);var p=t?g-1:0,d=t?-1:1;if(c<2)for(;;){if(p in f){h=f[p],p+=d;break}if(p+=d,t?p<0:g<=p)throw new o(u)}for(;t?p>=0:g>p;p+=d)p in f&&(h=r(h,f[p],p,l));return h}};t.exports={left:c(!1),right:c(!0)}},1033:function(t,e,r){"use strict";var i,n=r(796),s=r(852),a=r(841).f,o=r(824),u=r(809),c=r(885),h=r(802),l=r(886),f=r(808),g=s("".slice),p=Math.min,d=l("endsWith");n({target:"String",proto:!0,forced:!!(f||d||(i=a(String.prototype,"endsWith"),!i||i.writable))&&!d},{endsWith:function(t){var e=u(h(this));c(t);var r=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===r?i:p(o(r),i),s=u(t);return g(e,n-s.length,n)===s}})},1034:function(t,e,r){"use strict";var i=r(794),n=r(787),s=r(881),a=r(797),o=r(817),u=r(802),c=r(920),h=r(883),l=r(824),f=r(809),g=r(823),p=r(884),d=r(930),v=r(786),y=d.UNSUPPORTED_Y,m=Math.min,x=n([].push),b=n("".slice),S=!v((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),w="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;s("split",(function(t,e,r){var n="0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:i(e,this,t,r)}:e;return[function(e,r){var s=u(this),a=o(e)?void 0:g(e,t);return a?i(a,e,s,r):i(n,f(s),e,r)},function(t,i){var s=a(this),o=f(t);if(!w){var u=r(n,s,o,i,n!==e);if(u.done)return u.value}var g=c(s,RegExp),d=s.unicode,v=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(y?"g":"y"),S=new g(y?"^(?:"+s.source+")":s,v),P=void 0===i?4294967295:i>>>0;if(0===P)return[];if(0===o.length)return null===p(S,o)?[o]:[];for(var A=0,C=0,E=[];C<o.length;){S.lastIndex=y?0:C;var T,O=p(S,y?b(o,C):o);if(null===O||(T=m(l(S.lastIndex+(y?C:0)),o.length))===A)C=h(o,C,d);else{if(x(E,b(o,A,C)),E.length===P)return E;for(var M=1;M<=O.length-1;M++)if(x(E,O[M]),E.length===P)return E;C=A=T}}return x(E,b(o,A)),E}]}),w||!S,y)},1036:function(t,e,r){"use strict";var i=r(796),n=r(1037).trim;i({target:"String",proto:!0,forced:r(1038)("trim")},{trim:function(){return n(this)}})},1037:function(t,e,r){"use strict";var i=r(787),n=r(802),s=r(809),a=r(936),o=i("".replace),u=RegExp("^["+a+"]+"),c=RegExp("(^|[^"+a+"])["+a+"]+$"),h=function(t){return function(e){var r=s(n(e));return 1&t&&(r=o(r,u,"")),2&t&&(r=o(r,c,"$1")),r}};t.exports={start:h(1),end:h(2),trim:h(3)}},1038:function(t,e,r){"use strict";var i=r(847).PROPER,n=r(786),s=r(936);t.exports=function(t){return n((function(){return!!s[t]()||"​᠎"!=="​᠎"[t]()||i&&s[t].name!==t}))}},1039:function(t,e,r){"use strict";var i=r(796),n=r(852),s=r(916).indexOf,a=r(934),o=n([].indexOf),u=!!o&&1/o([1],1,-0)<0;i({target:"Array",proto:!0,forced:u||!a("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return u?o(this,t,e)||0:s(this,t,e)}})},1040:function(t,e,r){"use strict";var i=r(796),n=r(787),s=r(885),a=r(802),o=r(809),u=r(886),c=n("".indexOf);i({target:"String",proto:!0,forced:!u("includes")},{includes:function(t){return!!~c(o(a(this)),o(s(t)),arguments.length>1?arguments[1]:void 0)}})},1041:function(t,e,r){"use strict";var i=r(796),n=r(787),s=r(1042),a=n([].reverse),o=[1,2];i({target:"Array",proto:!0,forced:String(o)===String(o.reverse())},{reverse:function(){return s(this)&&(this.length=this.length),a(this)}})},1042:function(t,e,r){"use strict";var i=r(816);t.exports=Array.isArray||function(t){return"Array"===i(t)}},1043:function(t,e,r){"use strict";var i=r(847).PROPER,n=r(819),s=r(797),a=r(809),o=r(786),u=r(1044),c=RegExp.prototype,h=c.toString,l=o((function(){return"/a/b"!==h.call({source:"a",flags:"b"})})),f=i&&"toString"!==h.name;(l||f)&&n(c,"toString",(function(){var t=s(this);return"/"+a(t.source)+"/"+a(u(t))}),{unsafe:!0})},1044:function(t,e,r){"use strict";var i=r(794),n=r(804),s=r(843),a=r(929),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||n(t,"flags")||!s(o,t)?e:i(a,t)}},781:function(t,e,r){"use strict";r.r(e),function(t){r.d(e,"AElement",(function(){return Ut})),r.d(e,"AnimateColorElement",(function(){return Dt})),r.d(e,"AnimateElement",(function(){return Lt})),r.d(e,"AnimateTransformElement",(function(){return jt})),r.d(e,"BoundingBox",(function(){return ft})),r.d(e,"CB1",(function(){return L})),r.d(e,"CB2",(function(){return D})),r.d(e,"CB3",(function(){return j})),r.d(e,"CB4",(function(){return B})),r.d(e,"Canvg",(function(){return xe})),r.d(e,"CircleElement",(function(){return wt})),r.d(e,"ClipPathElement",(function(){return se})),r.d(e,"DefsElement",(function(){return Mt})),r.d(e,"DescElement",(function(){return fe})),r.d(e,"Document",(function(){return ve})),r.d(e,"Element",(function(){return at})),r.d(e,"EllipseElement",(function(){return Pt})),r.d(e,"FeColorMatrixElement",(function(){return re})),r.d(e,"FeCompositeElement",(function(){return ce})),r.d(e,"FeDropShadowElement",(function(){return oe})),r.d(e,"FeGaussianBlurElement",(function(){return he})),r.d(e,"FeMorphologyElement",(function(){return ue})),r.d(e,"FilterElement",(function(){return ae})),r.d(e,"Font",(function(){return lt})),r.d(e,"FontElement",(function(){return Bt})),r.d(e,"FontFaceElement",(function(){return zt})),r.d(e,"GElement",(function(){return Vt})),r.d(e,"GlyphElement",(function(){return vt})),r.d(e,"GradientElement",(function(){return kt})),r.d(e,"ImageElement",(function(){return Ht})),r.d(e,"LineElement",(function(){return At})),r.d(e,"LinearGradientElement",(function(){return Nt})),r.d(e,"MarkerElement",(function(){return Ot})),r.d(e,"MaskElement",(function(){return ie})),r.d(e,"Matrix",(function(){return et})),r.d(e,"MissingGlyphElement",(function(){return _t})),r.d(e,"Mouse",(function(){return Y})),r.d(e,"PSEUDO_ZERO",(function(){return k})),r.d(e,"Parser",(function(){return Z})),r.d(e,"PathElement",(function(){return dt})),r.d(e,"PathParser",(function(){return gt})),r.d(e,"PatternElement",(function(){return Tt})),r.d(e,"Point",(function(){return W})),r.d(e,"PolygonElement",(function(){return Et})),r.d(e,"PolylineElement",(function(){return Ct})),r.d(e,"Property",(function(){return U})),r.d(e,"QB1",(function(){return z})),r.d(e,"QB2",(function(){return _})),r.d(e,"QB3",(function(){return F})),r.d(e,"RadialGradientElement",(function(){return Rt})),r.d(e,"RectElement",(function(){return St})),r.d(e,"RenderedElement",(function(){return pt})),r.d(e,"Rotate",(function(){return K})),r.d(e,"SVGElement",(function(){return bt})),r.d(e,"SVGFontLoader",(function(){return qt})),r.d(e,"Scale",(function(){return tt})),r.d(e,"Screen",(function(){return $})),r.d(e,"Skew",(function(){return rt})),r.d(e,"SkewX",(function(){return it})),r.d(e,"SkewY",(function(){return nt})),r.d(e,"StopElement",(function(){return It})),r.d(e,"StyleElement",(function(){return Qt})),r.d(e,"SymbolElement",(function(){return $t})),r.d(e,"TRefElement",(function(){return Ft})),r.d(e,"TSpanElement",(function(){return mt})),r.d(e,"TextElement",(function(){return yt})),r.d(e,"TextPathElement",(function(){return Yt})),r.d(e,"TitleElement",(function(){return le})),r.d(e,"Transform",(function(){return st})),r.d(e,"Translate",(function(){return J})),r.d(e,"UnknownElement",(function(){return ot})),r.d(e,"UseElement",(function(){return Zt})),r.d(e,"ViewPort",(function(){return X})),r.d(e,"compressSpaces",(function(){return p})),r.d(e,"default",(function(){return xe})),r.d(e,"getSelectorSpecificity",(function(){return V})),r.d(e,"normalizeAttributeName",(function(){return x})),r.d(e,"normalizeColor",(function(){return S})),r.d(e,"parseExternalUrl",(function(){return b})),r.d(e,"presets",(function(){return g})),r.d(e,"toNumbers",(function(){return y})),r.d(e,"trimLeft",(function(){return d})),r.d(e,"trimRight",(function(){return v})),r.d(e,"vectorMagnitude",(function(){return N})),r.d(e,"vectorsAngle",(function(){return I})),r.d(e,"vectorsRatio",(function(){return R}));r(970);var i=r(1008),n=r.n(i),s=(r(1009),r(1016),r(1018),r(931),r(1025),r(1028)),a=r.n(s),o=(r(1031),r(1033),r(1034),r(935)),u=r.n(o),c=(r(1036),r(937)),h=r.n(c),l=(r(1039),r(1040),r(1041),r(938)),f=(r(1043),r(939));var g=Object.freeze({__proto__:null,offscreen:function(){var{DOMParser:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:t,createCanvas:(t,e)=>new OffscreenCanvas(t,e),createImage:t=>n()((function*(){var e=yield fetch(t),r=yield e.blob();return yield createImageBitmap(r)}))()};return"undefined"==typeof DOMParser&&void 0!==t||Reflect.deleteProperty(e,"DOMParser"),e},node:function(t){var{DOMParser:e,canvas:r,fetch:i}=t;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:i,createCanvas:r.createCanvas,createImage:r.loadImage}}});function p(t){return t.replace(/(?!\u3000)\s+/gm," ")}function d(t){return t.replace(/^[\n \t]+/,"")}function v(t){return t.replace(/[\n \t]+$/,"")}function y(t){return((t||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[]).map(parseFloat)}var m=/^[A-Z-]+$/;function x(t){return m.test(t)?t.toLowerCase():t}function b(t){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(t)||[];return e[2]||e[3]||e[4]}function S(t){if(!t.startsWith("rgb"))return t;var e=3;return t.replace(/\d+(\.\d+)?/g,(t,r)=>e--&&r?String(Math.round(parseFloat(t))):t)}var w=/(\[[^\]]+\])/g,P=/(#[^\s+>~.[:]+)/g,A=/(\.[^\s+>~.[:]+)/g,C=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,E=/(:[\w-]+\([^)]*\))/gi,T=/(:[^\s+>~.[:]+)/g,O=/([^\s+>~.[:]+)/g;function M(t,e){var r=e.exec(t);return r?[t.replace(e," "),r.length]:[t,0]}function V(t){var e=[0,0,0],r=t.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),i=0;return[r,i]=M(r,w),e[1]+=i,[r,i]=M(r,P),e[0]+=i,[r,i]=M(r,A),e[1]+=i,[r,i]=M(r,C),e[2]+=i,[r,i]=M(r,E),e[1]+=i,[r,i]=M(r,T),e[1]+=i,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,i]=M(r,O),e[2]+=i,e.join("")}var k=1e-8;function N(t){return Math.sqrt(Math.pow(t[0],2)+Math.pow(t[1],2))}function R(t,e){return(t[0]*e[0]+t[1]*e[1])/(N(t)*N(e))}function I(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(R(t,e))}function L(t){return t*t*t}function D(t){return 3*t*t*(1-t)}function j(t){return 3*t*(1-t)*(1-t)}function B(t){return(1-t)*(1-t)*(1-t)}function z(t){return t*t}function _(t){return 2*t*(1-t)}function F(t){return(1-t)*(1-t)}class U{constructor(t,e,r){this.document=t,this.name=e,this.value=r,this.isNormalizedColor=!1}static empty(t){return new U(t,"EMPTY","")}split(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",{document:e,name:r}=this;return p(this.getString()).trim().split(t).map(t=>new U(e,r,t))}hasValue(t){var{value:e}=this;return null!==e&&""!==e&&(t||0!==e)&&void 0!==e}isString(t){var{value:e}=this,r="string"==typeof e;return r&&t?t.test(e):r}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var t=this.getString();switch(!0){case t.endsWith("px"):case/^[0-9]+$/.test(t):return!0;default:return!1}}setValue(t){return this.value=t,this}getValue(t){return void 0===t||this.hasValue()?this.value:t}getNumber(t){if(!this.hasValue())return void 0===t?0:parseFloat(t);var{value:e}=this,r=parseFloat(e);return this.isString(/%$/)&&(r/=100),r}getString(t){return void 0===t||this.hasValue()?void 0===this.value?"":String(this.value):String(t)}getColor(t){var e=this.getString(t);return this.isNormalizedColor||(this.isNormalizedColor=!0,e=S(e),this.value=e),e}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasValue())return 0;var[r,i]="boolean"==typeof t?[void 0,t]:[t],{viewPort:n}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(n.computeSize("x"),n.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(n.computeSize("x"),n.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*n.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*n.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return 15*this.getNumber();case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case this.isString(/%$/)&&i:return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*n.computeSize(r);default:var s=this.getNumber();return e&&s<1?s*n.computeSize(r):s}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():1e3*this.getNumber():0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var t=this.getString(),e=/#([^)'"]+)/.exec(t);return e&&(e=e[1]),e||(e=t),this.document.definitions[e]}getFillStyleDefinition(t,e){var r=this.getDefinition();if(!r)return null;if("function"==typeof r.createGradient)return r.createGradient(this.document.ctx,t,e);if("function"==typeof r.createPattern){if(r.getHrefAttribute().hasValue()){var i=r.getAttribute("patternTransform");r=r.getHrefAttribute().getDefinition(),i.hasValue()&&r.getAttribute("patternTransform",!0).setValue(i.value)}return r.createPattern(this.document.ctx,t,e)}return null}getTextBaseline(){return this.hasValue()?U.textBaselineMapping[this.getString()]:null}addOpacity(t){for(var e=this.getColor(),r=e.length,i=0,n=0;n<r&&(","===e[n]&&i++,3!==i);n++);if(t.hasValue()&&this.isString()&&3!==i){var s=new h.a(e);s.ok&&(s.alpha=t.getNumber(),e=s.toRGBA())}return new U(this.document,this.name,e)}}U.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"};class X{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(t,e){this.viewPorts.push({width:t,height:e})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:t}=this;return t[t.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(t){return"number"==typeof t?t:"x"===t?this.width:"y"===t?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}}class W{constructor(t,e){this.x=t,this.y=e}static parse(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,[r=e,i=e]=y(t);return new W(r,i)}static parseScale(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,[r=e,i=r]=y(t);return new W(r,i)}static parsePath(t){for(var e=y(t),r=e.length,i=[],n=0;n<r;n+=2)i.push(new W(e[n],e[n+1]));return i}angleTo(t){return Math.atan2(t.y-this.y,t.x-this.x)}applyTransform(t){var{x:e,y:r}=this,i=e*t[0]+r*t[2]+t[4],n=e*t[1]+r*t[3]+t[5];this.x=i,this.y=n}}class Y{constructor(t){this.screen=t,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:t,onClick:e,onMouseMove:r}=this,i=t.ctx.canvas;i.onclick=e,i.onmousemove=r,this.working=!0}}stop(){if(this.working){var t=this.screen.ctx.canvas;this.working=!1,t.onclick=null,t.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:t,events:e,eventElements:r}=this,{style:i}=t.ctx.canvas;i&&(i.cursor=""),e.forEach((t,e)=>{for(var{run:i}=t,n=r[e];n;)i(n),n=n.parent}),this.events=[],this.eventElements=[]}}checkPath(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInPath&&e.isPointInPath(s,a)&&(i[n]=t)})}}checkBoundingBox(t,e){if(this.working&&e){var{events:r,eventElements:i}=this;r.forEach((r,n)=>{var{x:s,y:a}=r;!i[n]&&e.isPointInBox(s,a)&&(i[n]=t)})}}mapXY(t,e){for(var{window:r,ctx:i}=this.screen,n=new W(t,e),s=i.canvas;s;)n.x-=s.offsetLeft,n.y-=s.offsetTop,s=s.offsetParent;return r.scrollX&&(n.x+=r.scrollX),r.scrollY&&(n.y+=r.scrollY),n}onClick(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onclick",x:e,y:r,run(t){t.onClick&&t.onClick()}})}onMouseMove(t){var{x:e,y:r}=this.mapXY(t.clientX,t.clientY);this.events.push({type:"onmousemove",x:e,y:r,run(t){t.onMouseMove&&t.onMouseMove()}})}}var G="undefined"!=typeof window?window:null,H="undefined"!=typeof fetch?fetch.bind(void 0):null;class ${constructor(t){var{fetch:e=H,window:r=G}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.ctx=t,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new X,this.mouse=new Y(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=r,this.fetch=e}wait(t){this.waits.push(t)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var t=this.waits.every(t=>t());return t&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=t,t}setDefaults(t){t.strokeStyle="rgba(0,0,0,0)",t.lineCap="butt",t.lineJoin="miter",t.miterLimit=4}setViewBox(t){var{document:e,ctx:r,aspectRatio:i,width:n,desiredWidth:s,height:a,desiredHeight:o,minX:u=0,minY:c=0,refX:h,refY:l,clip:f=!1,clipX:g=0,clipY:d=0}=t,v=p(i).replace(/^defer\s/,""),[y,m]=v.split(" "),x=y||"xMidYMid",b=m||"meet",S=n/s,w=a/o,P=Math.min(S,w),A=Math.max(S,w),C=s,E=o;"meet"===b&&(C*=P,E*=P),"slice"===b&&(C*=A,E*=A);var T=new U(e,"refX",h),O=new U(e,"refY",l),M=T.hasValue()&&O.hasValue();if(M&&r.translate(-P*T.getPixels("x"),-P*O.getPixels("y")),f){var V=P*g,k=P*d;r.beginPath(),r.moveTo(V,k),r.lineTo(n,k),r.lineTo(n,a),r.lineTo(V,a),r.closePath(),r.clip()}if(!M){var N="meet"===b&&P===w,R="slice"===b&&A===w,I="meet"===b&&P===S,L="slice"===b&&A===S;x.startsWith("xMid")&&(N||R)&&r.translate(n/2-C/2,0),x.endsWith("YMid")&&(I||L)&&r.translate(0,a/2-E/2),x.startsWith("xMax")&&(N||R)&&r.translate(n-C,0),x.endsWith("YMax")&&(I||L)&&r.translate(0,a-E)}switch(!0){case"none"===x:r.scale(S,w);break;case"meet"===b:r.scale(P,P);break;case"slice"===b:r.scale(A,A)}r.translate(-u,-c)}start(t){var{enableRedraw:e=!1,ignoreMouse:r=!1,ignoreAnimation:i=!1,ignoreDimensions:n=!1,ignoreClear:s=!1,forceRedraw:a,scaleWidth:o,scaleHeight:c,offsetX:h,offsetY:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{FRAMERATE:f,mouse:g}=this,p=1e3/f;if(this.frameDuration=p,this.readyPromise=new Promise(t=>{this.resolveReady=t}),this.isReady()&&this.render(t,n,s,o,c,h,l),e){var d=Date.now(),v=d,y=0,m=()=>{d=Date.now(),(y=d-v)>=p&&(v=d-y%p,this.shouldUpdate(i,a)&&(this.render(t,n,s,o,c,h,l),g.runEvents())),this.intervalId=u()(m)};r||g.start(),this.intervalId=u()(m)}}stop(){this.intervalId&&(u.a.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(t,e){if(!t){var{frameDuration:r}=this;if(this.animations.reduce((t,e)=>e.update(r)||t,!1))return!0}return!("function"!=typeof e||!e())||(!(this.isReadyLock||!this.isReady())||!!this.mouse.hasEvents())}render(t,e,r,i,n,s,a){var{CLIENT_WIDTH:o,CLIENT_HEIGHT:u,viewPort:c,ctx:h,isFirstRender:l}=this,f=h.canvas;c.clear(),f.width&&f.height?c.setCurrent(f.width,f.height):c.setCurrent(o,u);var g=t.getStyle("width"),p=t.getStyle("height");!e&&(l||"number"!=typeof i&&"number"!=typeof n)&&(g.hasValue()&&(f.width=g.getPixels("x"),f.style&&(f.style.width="".concat(f.width,"px"))),p.hasValue()&&(f.height=p.getPixels("y"),f.style&&(f.style.height="".concat(f.height,"px"))));var d=f.clientWidth||f.width,v=f.clientHeight||f.height;if(e&&g.hasValue()&&p.hasValue()&&(d=g.getPixels("x"),v=p.getPixels("y")),c.setCurrent(d,v),"number"==typeof s&&t.getAttribute("x",!0).setValue(s),"number"==typeof a&&t.getAttribute("y",!0).setValue(a),"number"==typeof i||"number"==typeof n){var m=y(t.getAttribute("viewBox").getString()),x=0,b=0;if("number"==typeof i){var S=t.getStyle("width");S.hasValue()?x=S.getPixels("x")/i:isNaN(m[2])||(x=m[2]/i)}if("number"==typeof n){var w=t.getStyle("height");w.hasValue()?b=w.getPixels("y")/n:isNaN(m[3])||(b=m[3]/n)}x||(x=b),b||(b=x),t.getAttribute("width",!0).setValue(i),t.getAttribute("height",!0).setValue(n);var P=t.getStyle("transform",!0,!0);P.setValue("".concat(P.getString()," scale(").concat(1/x,", ").concat(1/b,")"))}r||h.clearRect(0,0,d,v),t.render(h),l&&(this.isFirstRender=!1)}}$.defaultWindow=G,$.defaultFetch=H;var{defaultFetch:q}=$,Q="undefined"!=typeof DOMParser?DOMParser:null;class Z{constructor(){var{fetch:t=q,DOMParser:e=Q}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fetch=t,this.DOMParser=e}parse(t){var e=this;return n()((function*(){return t.startsWith("<")?e.parseFromString(t):e.load(t)}))()}parseFromString(t){var e=new this.DOMParser;try{return this.checkDocument(e.parseFromString(t,"image/svg+xml"))}catch(r){return this.checkDocument(e.parseFromString(t,"text/xml"))}}checkDocument(t){var e=t.getElementsByTagName("parsererror")[0];if(e)throw new Error(e.textContent);return t}load(t){var e=this;return n()((function*(){var r=yield e.fetch(t),i=yield r.text();return e.parseFromString(i)}))()}}class J{constructor(t,e){this.type="translate",this.point=null,this.point=W.parse(e)}apply(t){var{x:e,y:r}=this.point;t.translate(e||0,r||0)}unapply(t){var{x:e,y:r}=this.point;t.translate(-1*e||0,-1*r||0)}applyToPoint(t){var{x:e,y:r}=this.point;t.applyTransform([1,0,0,1,e||0,r||0])}}class K{constructor(t,e,r){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var i=y(e);this.angle=new U(t,"angle",i[0]),this.originX=r[0],this.originY=r[1],this.cx=i[1]||0,this.cy=i[2]||0}apply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(s.getRadians()),t.translate(-a,-o)}unapply(t){var{cx:e,cy:r,originX:i,originY:n,angle:s}=this,a=e+i.getPixels("x"),o=r+n.getPixels("y");t.translate(a,o),t.rotate(-1*s.getRadians()),t.translate(-a,-o)}applyToPoint(t){var{cx:e,cy:r,angle:i}=this,n=i.getRadians();t.applyTransform([1,0,0,1,e||0,r||0]),t.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),t.applyTransform([1,0,0,1,-e||0,-r||0])}}class tt{constructor(t,e,r){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var i=W.parseScale(e);0!==i.x&&0!==i.y||(i.x=k,i.y=k),this.scale=i,this.originX=r[0],this.originY=r[1]}apply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(e,r||e),t.translate(-s,-a)}unapply(t){var{scale:{x:e,y:r},originX:i,originY:n}=this,s=i.getPixels("x"),a=n.getPixels("y");t.translate(s,a),t.scale(1/e,1/r||e),t.translate(-s,-a)}applyToPoint(t){var{x:e,y:r}=this.scale;t.applyTransform([e||0,0,0,r||0,0,0])}}class et{constructor(t,e,r){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=y(e),this.originX=r[0],this.originY=r[1]}apply(t){var{originX:e,originY:r,matrix:i}=this,n=e.getPixels("x"),s=r.getPixels("y");t.translate(n,s),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),t.translate(-n,-s)}unapply(t){var{originX:e,originY:r,matrix:i}=this,n=i[0],s=i[2],a=i[4],o=i[1],u=i[3],c=i[5],h=1/(n*(1*u-0*c)-s*(1*o-0*c)+a*(0*o-0*u)),l=e.getPixels("x"),f=r.getPixels("y");t.translate(l,f),t.transform(h*(1*u-0*c),h*(0*c-1*o),h*(0*a-1*s),h*(1*n-0*a),h*(s*c-a*u),h*(a*o-n*c)),t.translate(-l,-f)}applyToPoint(t){t.applyTransform(this.matrix)}}class rt extends et{constructor(t,e,r){super(t,e,r),this.type="skew",this.angle=null,this.angle=new U(t,"angle",e)}}class it extends rt{constructor(t,e,r){super(t,e,r),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}}class nt extends rt{constructor(t,e,r){super(t,e,r),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}}class st{constructor(t,e,r){this.document=t,this.transforms=[],function(t){return p(t).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}(e).forEach(t=>{if("none"!==t){var[e,i]=function(t){var[e,r]=t.split("(");return[e.trim(),r.trim().replace(")","")]}(t),n=st.transformTypes[e];void 0!==n&&this.transforms.push(new n(this.document,i,r))}})}static fromElement(t,e){var r=e.getStyle("transform",!1,!0),[i,n=i]=e.getStyle("transform-origin",!1,!0).split(),s=[i,n];return r.hasValue()?new st(t,r.getString(),s):null}apply(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].apply(t)}unapply(t){for(var{transforms:e}=this,r=e.length-1;r>=0;r--)e[r].unapply(t)}applyToPoint(t){for(var{transforms:e}=this,r=e.length,i=0;i<r;i++)e[i].applyToPoint(t)}}st.transformTypes={translate:J,rotate:K,scale:tt,matrix:et,skewX:it,skewY:nt};class at{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this.document=t,this.node=e,this.captureTextNodes=r,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],e&&1===e.nodeType){if(Array.from(e.attributes).forEach(e=>{var r=x(e.nodeName);this.attributes[r]=new U(t,r,e.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue())this.getAttribute("style").getString().split(";").map(t=>t.trim()).forEach(e=>{if(e){var[r,i]=e.split(":").map(t=>t.trim());this.styles[r]=new U(t,r,i)}});var{definitions:i}=t,n=this.getAttribute("id");n.hasValue()&&(i[n.getString()]||(i[n.getString()]=this)),Array.from(e.childNodes).forEach(e=>{if(1===e.nodeType)this.addChild(e);else if(r&&(3===e.nodeType||4===e.nodeType)){var i=t.createTextNode(e);i.getText().length>0&&this.addChild(i)}})}}getAttribute(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.attributes[t];if(!r&&e){var i=new U(this.document,t,"");return this.attributes[t]=i,i}return r||U.empty(this.document)}getHrefAttribute(){for(var t in this.attributes)if("href"===t||t.endsWith(":href"))return this.attributes[t];return U.empty(this.document)}getStyle(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.styles[t];if(i)return i;var n=this.getAttribute(t);if(null!=n&&n.hasValue())return this.styles[t]=n,n;if(!r){var{parent:s}=this;if(s){var a=s.getStyle(t);if(null!=a&&a.hasValue())return a}}if(e){var o=new U(this.document,t,"");return this.styles[t]=o,o}return i||U.empty(this.document)}render(t){if("none"!==this.getStyle("display").getString()&&"hidden"!==this.getStyle("visibility").getString()){if(t.save(),this.getStyle("mask").hasValue()){var e=this.getStyle("mask").getDefinition();e&&(this.applyEffects(t),e.apply(t,this))}else if("none"!==this.getStyle("filter").getValue("none")){var r=this.getStyle("filter").getDefinition();r&&(this.applyEffects(t),r.apply(t,this))}else this.setContext(t),this.renderChildren(t),this.clearContext(t);t.restore()}}setContext(t){}applyEffects(t){var e=st.fromElement(this.document,this);e&&e.apply(t);var r=this.getStyle("clip-path",!1,!0);if(r.hasValue()){var i=r.getDefinition();i&&i.apply(t)}}clearContext(t){}renderChildren(t){this.children.forEach(e=>{e.render(t)})}addChild(t){var e=t instanceof at?t:this.document.createElement(t);e.parent=this,at.ignoreChildTypes.includes(e.type)||this.children.push(e)}matchesSelector(t){var e,{node:r}=this;if("function"==typeof r.matches)return r.matches(t);var i=null===(e=r.getAttribute)||void 0===e?void 0:e.call(r,"class");return!(!i||""===i)&&i.split(" ").some(e=>".".concat(e)===t)}addStylesFromStyleDefinition(){var{styles:t,stylesSpecificity:e}=this.document;for(var r in t)if(!r.startsWith("@")&&this.matchesSelector(r)){var i=t[r],n=e[r];if(i)for(var s in i){var a=this.stylesSpecificity[s];void 0===a&&(a="000"),n>=a&&(this.styles[s]=i[s],this.stylesSpecificity[s]=n)}}}removeStyles(t,e){return e.reduce((e,r)=>{var i=t.getStyle(r);if(!i.hasValue())return e;var n=i.getString();return i.setValue(""),[...e,[r,n]]},[])}restoreStyles(t,e){e.forEach(e=>{var[r,i]=e;t.getStyle(r,!0).setValue(i)})}isFirstChild(){var t;return 0===(null===(t=this.parent)||void 0===t?void 0:t.children.indexOf(this))}}at.ignoreChildTypes=["title"];class ot extends at{constructor(t,e,r){super(t,e,r)}}function ut(t){var e=t.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function ct(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function ht(t){if(!t)return"";var e=t.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}class lt{constructor(t,e,r,i,n,s){var a=s?"string"==typeof s?lt.parse(s):s:{};this.fontFamily=n||a.fontFamily,this.fontSize=i||a.fontSize,this.fontStyle=t||a.fontStyle,this.fontWeight=r||a.fontWeight,this.fontVariant=e||a.fontVariant}static parse(){var t=arguments.length>1?arguments[1]:void 0,e="",r="",i="",n="",s="",a=p(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").trim().split(" "),o={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return a.forEach(t=>{switch(!0){case!o.fontStyle&&lt.styles.includes(t):"inherit"!==t&&(e=t),o.fontStyle=!0;break;case!o.fontVariant&&lt.variants.includes(t):"inherit"!==t&&(r=t),o.fontStyle=!0,o.fontVariant=!0;break;case!o.fontWeight&&lt.weights.includes(t):"inherit"!==t&&(i=t),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0;break;case!o.fontSize:"inherit"!==t&&([n]=t.split("/")),o.fontStyle=!0,o.fontVariant=!0,o.fontWeight=!0,o.fontSize=!0;break;default:"inherit"!==t&&(s+=t)}}),new lt(e,r,i,n,s,t)}toString(){return[ct(this.fontStyle),this.fontVariant,ht(this.fontWeight),this.fontSize,(e=this.fontFamily,void 0===t?e:e.trim().split(",").map(ut).join(","))].join(" ").trim();var e}}lt.styles="normal|italic|oblique|inherit",lt.variants="normal|small-caps|inherit",lt.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit";class ft{constructor(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Number.NaN,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.NaN,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.NaN,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Number.NaN;this.x1=t,this.y1=e,this.x2=r,this.y2=i,this.addPoint(t,e),this.addPoint(r,i)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(t,e){void 0!==t&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=t,this.x2=t),t<this.x1&&(this.x1=t),t>this.x2&&(this.x2=t)),void 0!==e&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=e,this.y2=e),e<this.y1&&(this.y1=e),e>this.y2&&(this.y2=e))}addX(t){this.addPoint(t,null)}addY(t){this.addPoint(null,t)}addBoundingBox(t){if(t){var{x1:e,y1:r,x2:i,y2:n}=t;this.addPoint(e,r),this.addPoint(i,n)}}sumCubic(t,e,r,i,n){return Math.pow(1-t,3)*e+3*Math.pow(1-t,2)*t*r+3*(1-t)*Math.pow(t,2)*i+Math.pow(t,3)*n}bezierCurveAdd(t,e,r,i,n){var s=6*e-12*r+6*i,a=-3*e+9*r-9*i+3*n,o=3*r-3*e;if(0!==a){var u=Math.pow(s,2)-4*o*a;if(!(u<0)){var c=(-s+Math.sqrt(u))/(2*a);0<c&&c<1&&(t?this.addX(this.sumCubic(c,e,r,i,n)):this.addY(this.sumCubic(c,e,r,i,n)));var h=(-s-Math.sqrt(u))/(2*a);0<h&&h<1&&(t?this.addX(this.sumCubic(h,e,r,i,n)):this.addY(this.sumCubic(h,e,r,i,n)))}}else{if(0===s)return;var l=-o/s;0<l&&l<1&&(t?this.addX(this.sumCubic(l,e,r,i,n)):this.addY(this.sumCubic(l,e,r,i,n)))}}addBezierCurve(t,e,r,i,n,s,a,o){this.addPoint(t,e),this.addPoint(a,o),this.bezierCurveAdd(!0,t,r,n,a),this.bezierCurveAdd(!1,e,i,s,o)}addQuadraticCurve(t,e,r,i,n,s){var a=t+2/3*(r-t),o=e+2/3*(i-e),u=a+1/3*(n-t),c=o+1/3*(s-e);this.addBezierCurve(t,e,a,u,o,c,n,s)}isPointInBox(t,e){var{x1:r,y1:i,x2:n,y2:s}=this;return r<=t&&t<=n&&i<=e&&e<=s}}class gt extends l.a{constructor(t){super(t.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new W(0,0),this.control=new W(0,0),this.current=new W(0,0),this.points=[],this.angles=[]}isEnd(){var{i:t,commands:e}=this;return t>=e.length-1}next(){var t=this.commands[++this.i];return this.previousCommand=this.command,this.command=t,t}getPoint(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"x",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"y",r=new W(this.command[t],this.command[e]);return this.makeAbsolute(r)}getAsControlPoint(t,e){var r=this.getPoint(t,e);return this.control=r,r}getAsCurrentPoint(t,e){var r=this.getPoint(t,e);return this.current=r,r}getReflectedControlPoint(){var t=this.previousCommand.type;if(t!==l.a.CURVE_TO&&t!==l.a.SMOOTH_CURVE_TO&&t!==l.a.QUAD_TO&&t!==l.a.SMOOTH_QUAD_TO)return this.current;var{current:{x:e,y:r},control:{x:i,y:n}}=this;return new W(2*e-i,2*r-n)}makeAbsolute(t){if(this.command.relative){var{x:e,y:r}=this.current;t.x+=e,t.y+=r}return t}addMarker(t,e,r){var{points:i,angles:n}=this;r&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=i[i.length-1].angleTo(r)),this.addMarkerAngle(t,e?e.angleTo(t):null)}addMarkerAngle(t,e){this.points.push(t),this.angles.push(e)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:t}=this,e=t.length,r=0;r<e;r++)if(!t[r])for(var i=r+1;i<e;i++)if(t[i]){t[r]=t[i];break}return t}}class pt extends at{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var t=1,e=this;e;){var r=e.getStyle("opacity",!1,!0);r.hasValue(!0)&&(t*=r.getNumber()),e=e.parent}return t}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e){var r=this.getStyle("fill"),i=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),s=this.getStyle("stroke-opacity");if(r.isUrlDefinition()){var a=r.getFillStyleDefinition(this,i);a&&(t.fillStyle=a)}else if(r.hasValue()){"currentColor"===r.getString()&&r.setValue(this.getStyle("color").getColor());var o=r.getColor();"inherit"!==o&&(t.fillStyle="none"===o?"rgba(0,0,0,0)":o)}if(i.hasValue()){var u=new U(this.document,"fill",t.fillStyle).addOpacity(i).getColor();t.fillStyle=u}if(n.isUrlDefinition()){var c=n.getFillStyleDefinition(this,s);c&&(t.strokeStyle=c)}else if(n.hasValue()){"currentColor"===n.getString()&&n.setValue(this.getStyle("color").getColor());var h=n.getString();"inherit"!==h&&(t.strokeStyle="none"===h?"rgba(0,0,0,0)":h)}if(s.hasValue()){var l=new U(this.document,"stroke",t.strokeStyle).addOpacity(s).getString();t.strokeStyle=l}var f=this.getStyle("stroke-width");if(f.hasValue()){var g=f.getPixels();t.lineWidth=g||k}var p=this.getStyle("stroke-linecap"),d=this.getStyle("stroke-linejoin"),v=this.getStyle("stroke-miterlimit"),m=this.getStyle("stroke-dasharray"),x=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(t.lineCap=p.getString()),d.hasValue()&&(t.lineJoin=d.getString()),v.hasValue()&&(t.miterLimit=v.getNumber()),m.hasValue()&&"none"!==m.getString()){var b=y(m.getString());void 0!==t.setLineDash?t.setLineDash(b):void 0!==t.webkitLineDash?t.webkitLineDash=b:void 0===t.mozDash||1===b.length&&0===b[0]||(t.mozDash=b);var S=x.getPixels();void 0!==t.lineDashOffset?t.lineDashOffset=S:void 0!==t.webkitLineDashOffset?t.webkitLineDashOffset=S:void 0!==t.mozDashOffset&&(t.mozDashOffset=S)}}if(this.modifiedEmSizeStack=!1,void 0!==t.font){var w=this.getStyle("font"),P=this.getStyle("font-style"),A=this.getStyle("font-variant"),C=this.getStyle("font-weight"),E=this.getStyle("font-size"),T=this.getStyle("font-family"),O=new lt(P.getString(),A.getString(),C.getString(),E.hasValue()?"".concat(E.getPixels(!0),"px"):"",T.getString(),lt.parse(w.getString(),t.font));P.setValue(O.fontStyle),A.setValue(O.fontVariant),C.setValue(O.fontWeight),E.setValue(O.fontSize),T.setValue(O.fontFamily),t.font=O.toString(),E.isPixels()&&(this.document.emSize=E.getPixels(),this.modifiedEmSizeStack=!0)}e||(this.applyEffects(t),t.globalAlpha=this.calculateOpacity())}clearContext(t){super.clearContext(t),this.modifiedEmSizeStack&&this.document.popEmSize()}}class dt extends pt{constructor(t,e,r){super(t,e,r),this.type="path",this.pathParser=null,this.pathParser=new gt(this.getAttribute("d").getString())}path(t){var{pathParser:e}=this,r=new ft;for(e.reset(),t&&t.beginPath();!e.isEnd();)switch(e.next().type){case gt.MOVE_TO:this.pathM(t,r);break;case gt.LINE_TO:this.pathL(t,r);break;case gt.HORIZ_LINE_TO:this.pathH(t,r);break;case gt.VERT_LINE_TO:this.pathV(t,r);break;case gt.CURVE_TO:this.pathC(t,r);break;case gt.SMOOTH_CURVE_TO:this.pathS(t,r);break;case gt.QUAD_TO:this.pathQ(t,r);break;case gt.SMOOTH_QUAD_TO:this.pathT(t,r);break;case gt.ARC:this.pathA(t,r);break;case gt.CLOSE_PATH:this.pathZ(t,r)}return r}getBoundingBox(t){return this.path()}getMarkers(){var{pathParser:t}=this,e=t.getMarkerPoints(),r=t.getMarkerAngles();return e.map((t,e)=>[t,r[e]])}renderChildren(t){this.path(t),this.document.screen.mouse.checkPath(this,t);var e=this.getStyle("fill-rule");""!==t.fillStyle&&("inherit"!==e.getString("inherit")?t.fill(e.getString()):t.fill()),""!==t.strokeStyle&&("non-scaling-stroke"===this.getAttribute("vector-effect").getString()?(t.save(),t.setTransform(1,0,0,1,0,0),t.stroke(),t.restore()):t.stroke());var r=this.getMarkers();if(r){var i=r.length-1,n=this.getStyle("marker-start"),s=this.getStyle("marker-mid"),a=this.getStyle("marker-end");if(n.isUrlDefinition()){var o=n.getDefinition(),[u,c]=r[0];o.render(t,u,c)}if(s.isUrlDefinition())for(var h=s.getDefinition(),l=1;l<i;l++){var[f,g]=r[l];h.render(t,f,g)}if(a.isUrlDefinition()){var p=a.getDefinition(),[d,v]=r[i];p.render(t,d,v)}}}static pathM(t){var e=t.getAsCurrentPoint();return t.start=t.current,{point:e}}pathM(t,e){var{pathParser:r}=this,{point:i}=dt.pathM(r),{x:n,y:s}=i;r.addMarker(i),e.addPoint(n,s),t&&t.moveTo(n,s)}static pathL(t){var{current:e}=t;return{current:e,point:t.getAsCurrentPoint()}}pathL(t,e){var{pathParser:r}=this,{current:i,point:n}=dt.pathL(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathH(t){var{current:e,command:r}=t,i=new W((r.relative?e.x:0)+r.x,e.y);return t.current=i,{current:e,point:i}}pathH(t,e){var{pathParser:r}=this,{current:i,point:n}=dt.pathH(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathV(t){var{current:e,command:r}=t,i=new W(e.x,(r.relative?e.y:0)+r.y);return t.current=i,{current:e,point:i}}pathV(t,e){var{pathParser:r}=this,{current:i,point:n}=dt.pathV(r),{x:s,y:a}=n;r.addMarker(n,i),e.addPoint(s,a),t&&t.lineTo(s,a)}static pathC(t){var{current:e}=t;return{current:e,point:t.getPoint("x1","y1"),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathC(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=dt.pathC(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathS(t){var{current:e}=t;return{current:e,point:t.getReflectedControlPoint(),controlPoint:t.getAsControlPoint("x2","y2"),currentPoint:t.getAsCurrentPoint()}}pathS(t,e){var{pathParser:r}=this,{current:i,point:n,controlPoint:s,currentPoint:a}=dt.pathS(r);r.addMarker(a,s,n),e.addBezierCurve(i.x,i.y,n.x,n.y,s.x,s.y,a.x,a.y),t&&t.bezierCurveTo(n.x,n.y,s.x,s.y,a.x,a.y)}static pathQ(t){var{current:e}=t;return{current:e,controlPoint:t.getAsControlPoint("x1","y1"),currentPoint:t.getAsCurrentPoint()}}pathQ(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=dt.pathQ(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathT(t){var{current:e}=t,r=t.getReflectedControlPoint();return t.control=r,{current:e,controlPoint:r,currentPoint:t.getAsCurrentPoint()}}pathT(t,e){var{pathParser:r}=this,{current:i,controlPoint:n,currentPoint:s}=dt.pathT(r);r.addMarker(s,n,n),e.addQuadraticCurve(i.x,i.y,n.x,n.y,s.x,s.y),t&&t.quadraticCurveTo(n.x,n.y,s.x,s.y)}static pathA(t){var{current:e,command:r}=t,{rX:i,rY:n,xRot:s,lArcFlag:a,sweepFlag:o}=r,u=s*(Math.PI/180),c=t.getAsCurrentPoint(),h=new W(Math.cos(u)*(e.x-c.x)/2+Math.sin(u)*(e.y-c.y)/2,-Math.sin(u)*(e.x-c.x)/2+Math.cos(u)*(e.y-c.y)/2),l=Math.pow(h.x,2)/Math.pow(i,2)+Math.pow(h.y,2)/Math.pow(n,2);l>1&&(i*=Math.sqrt(l),n*=Math.sqrt(l));var f=(a===o?-1:1)*Math.sqrt((Math.pow(i,2)*Math.pow(n,2)-Math.pow(i,2)*Math.pow(h.y,2)-Math.pow(n,2)*Math.pow(h.x,2))/(Math.pow(i,2)*Math.pow(h.y,2)+Math.pow(n,2)*Math.pow(h.x,2)));isNaN(f)&&(f=0);var g=new W(f*i*h.y/n,f*-n*h.x/i),p=new W((e.x+c.x)/2+Math.cos(u)*g.x-Math.sin(u)*g.y,(e.y+c.y)/2+Math.sin(u)*g.x+Math.cos(u)*g.y),d=I([1,0],[(h.x-g.x)/i,(h.y-g.y)/n]),v=[(h.x-g.x)/i,(h.y-g.y)/n],y=[(-h.x-g.x)/i,(-h.y-g.y)/n],m=I(v,y);return R(v,y)<=-1&&(m=Math.PI),R(v,y)>=1&&(m=0),{currentPoint:c,rX:i,rY:n,sweepFlag:o,xAxisRotation:u,centp:p,a1:d,ad:m}}pathA(t,e){var{pathParser:r}=this,{currentPoint:i,rX:n,rY:s,sweepFlag:a,xAxisRotation:o,centp:u,a1:c,ad:h}=dt.pathA(r),l=1-a?1:-1,f=c+l*(h/2),g=new W(u.x+n*Math.cos(f),u.y+s*Math.sin(f));if(r.addMarkerAngle(g,f-l*Math.PI/2),r.addMarkerAngle(i,f-l*Math.PI),e.addPoint(i.x,i.y),t&&!isNaN(c)&&!isNaN(h)){var p=n>s?n:s,d=n>s?1:n/s,v=n>s?s/n:1;t.translate(u.x,u.y),t.rotate(o),t.scale(d,v),t.arc(0,0,p,c,c+h,Boolean(1-a)),t.scale(1/d,1/v),t.rotate(-o),t.translate(-u.x,-u.y)}}static pathZ(t){t.current=t.start}pathZ(t,e){dt.pathZ(this.pathParser),t&&e.x1!==e.x2&&e.y1!==e.y2&&t.closePath()}}class vt extends dt{constructor(t,e,r){super(t,e,r),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}}class yt extends pt{constructor(t,e,r){super(t,e,new.target===yt||r),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super.setContext(t,e);var r=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();r&&(t.textBaseline=r)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(t){if("text"!==this.type)return this.getTElementBoundingBox(t);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t);var e=null;return this.children.forEach((r,i)=>{var n=this.getChildBoundingBox(t,this,this,i);e?e.addBoundingBox(n):e=n}),e}getFontSize(){var{document:t,parent:e}=this,r=lt.parse(t.ctx.font).fontSize;return e.getStyle("font-size").getNumber(r)}getTElementBoundingBox(t){var e=this.getFontSize();return new ft(this.x,this.y-e,this.x+this.measureText(t),this.y)}getGlyph(t,e,r){var i=e[r],n=null;if(t.isArabic){var s=e.length,a=e[r-1],o=e[r+1],u="isolated";if((0===r||" "===a)&&r<s-1&&" "!==o&&(u="terminal"),r>0&&" "!==a&&r<s-1&&" "!==o&&(u="medial"),r>0&&" "!==a&&(r===s-1||" "===o)&&(u="initial"),void 0!==t.glyphs[i]){var c=t.glyphs[i];n=c instanceof vt?c:c[u]}}else n=t.glyphs[i];return n||(n=t.missingGlyph),n}getText(){return""}getTextFromNode(t){var e=t||this.node,r=Array.from(e.parentNode.childNodes),i=r.indexOf(e),n=r.length-1,s=p(e.textContent||"");return 0===i&&(s=d(s)),i===n&&(s=v(s)),s}renderChildren(t){if("text"===this.type){this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(t),this.children.forEach((e,r)=>{this.renderChild(t,this,this,r)});var{mouse:e}=this.document.screen;e.isWorking()&&e.checkBoundingBox(this,this.getBoundingBox(t))}else this.renderTElementChildren(t)}renderTElementChildren(t){var{document:e,parent:r}=this,i=this.getText(),n=r.getStyle("font-family").getDefinition();if(n)for(var{unitsPerEm:s}=n.fontFace,a=lt.parse(e.ctx.font),o=r.getStyle("font-size").getNumber(a.fontSize),u=r.getStyle("font-style").getString(a.fontStyle),c=o/s,h=n.isRTL?i.split("").reverse().join(""):i,l=y(r.getAttribute("dx").getString()),f=h.length,g=0;g<f;g++){var p=this.getGlyph(n,h,g);t.translate(this.x,this.y),t.scale(c,-c);var d=t.lineWidth;t.lineWidth=t.lineWidth*s/o,"italic"===u&&t.transform(1,0,.4,1,0,0),p.render(t),"italic"===u&&t.transform(1,0,-.4,1,0,0),t.lineWidth=d,t.scale(1/c,-1/c),t.translate(-this.x,-this.y),this.x+=o*(p.horizAdvX||n.horizAdvX)/s,void 0===l[g]||isNaN(l[g])||(this.x+=l[g])}else{var{x:v,y:m}=this;t.fillStyle&&t.fillText(i,v,m),t.strokeStyle&&t.strokeText(i,v,m)}}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var t=this.leafTexts[this.textChunkStart],e=t.getStyle("text-anchor").getString("start"),r=0;r="start"===e?t.x-this.minX:"end"===e?t.x-this.maxX:t.x-(this.minX+this.maxX)/2;for(var i=this.textChunkStart;i<this.leafTexts.length;i++)this.leafTexts[i].x+=r;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(t){this.children.forEach((e,r)=>{this.adjustChildCoordinatesRecursiveCore(t,this,this,r)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(t,e,r,i){var n=r.children[i];n.children.length>0?n.children.forEach((r,i)=>{e.adjustChildCoordinatesRecursiveCore(t,e,n,i)}):this.adjustChildCoordinates(t,e,r,i)}adjustChildCoordinates(t,e,r,i){var n=r.children[i];if("function"!=typeof n.measureText)return n;t.save(),n.setContext(t,!0);var s=n.getAttribute("x"),a=n.getAttribute("y"),o=n.getAttribute("dx"),u=n.getAttribute("dy"),c=n.getStyle("font-family").getDefinition(),h=Boolean(c)&&c.isRTL;0===i&&(s.hasValue()||s.setValue(n.getInheritedAttribute("x")),a.hasValue()||a.setValue(n.getInheritedAttribute("y")),o.hasValue()||o.setValue(n.getInheritedAttribute("dx")),u.hasValue()||u.setValue(n.getInheritedAttribute("dy")));var l=n.measureText(t);return h&&(e.x-=l),s.hasValue()?(e.applyAnchoring(),n.x=s.getPixels("x"),o.hasValue()&&(n.x+=o.getPixels("x"))):(o.hasValue()&&(e.x+=o.getPixels("x")),n.x=e.x),e.x=n.x,h||(e.x+=l),a.hasValue()?(n.y=a.getPixels("y"),u.hasValue()&&(n.y+=u.getPixels("y"))):(u.hasValue()&&(e.y+=u.getPixels("y")),n.y=e.y),e.y=n.y,e.leafTexts.push(n),e.minX=Math.min(e.minX,n.x,n.x+l),e.maxX=Math.max(e.maxX,n.x,n.x+l),n.clearContext(t),t.restore(),n}getChildBoundingBox(t,e,r,i){var n=r.children[i];if("function"!=typeof n.getBoundingBox)return null;var s=n.getBoundingBox(t);return s?(n.children.forEach((r,i)=>{var a=e.getChildBoundingBox(t,e,n,i);s.addBoundingBox(a)}),s):null}renderChild(t,e,r,i){var n=r.children[i];n.render(t),n.children.forEach((r,i)=>{e.renderChild(t,e,n,i)})}measureText(t){var{measureCache:e}=this;if(~e)return e;var r=this.getText(),i=this.measureTargetText(t,r);return this.measureCache=i,i}measureTargetText(t,e){if(!e.length)return 0;var{parent:r}=this,i=r.getStyle("font-family").getDefinition();if(i){for(var n=this.getFontSize(),s=i.isRTL?e.split("").reverse().join(""):e,a=y(r.getAttribute("dx").getString()),o=s.length,u=0,c=0;c<o;c++){u+=(this.getGlyph(i,s,c).horizAdvX||i.horizAdvX)*n/i.fontFace.unitsPerEm,void 0===a[c]||isNaN(a[c])||(u+=a[c])}return u}if(!t.measureText)return 10*e.length;t.save(),this.setContext(t,!0);var{width:h}=t.measureText(e);return this.clearContext(t),t.restore(),h}getInheritedAttribute(t){for(var e=this;e instanceof yt&&e.isFirstChild();){var r=e.parent.getAttribute(t);if(r.hasValue(!0))return r.getValue("0");e=e.parent}return null}}class mt extends yt{constructor(t,e,r){super(t,e,new.target===mt||r),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}}class xt extends mt{constructor(){super(...arguments),this.type="textNode"}}class bt extends pt{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(t){var e,{document:r}=this,{screen:i,window:n}=r,s=t.canvas;if(i.setDefaults(t),s.style&&void 0!==t.font&&n&&void 0!==n.getComputedStyle){t.font=n.getComputedStyle(s).getPropertyValue("font");var a=new U(r,"fontSize",lt.parse(t.font).fontSize);a.hasValue()&&(r.rootEmSize=a.getPixels("y"),r.emSize=r.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:o,height:u}=i.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var c=this.getAttribute("refX"),h=this.getAttribute("refY"),l=this.getAttribute("viewBox"),f=l.hasValue()?y(l.getString()):null,g=!this.root&&"visible"!==this.getStyle("overflow").getValue("hidden"),p=0,d=0,v=0,m=0;f&&(p=f[0],d=f[1]),this.root||(o=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y"),"marker"===this.type&&(v=p,m=d,p=0,d=0)),i.viewPort.setCurrent(o,u),!this.node||this.parent&&"foreignObject"!==(null===(e=this.node.parentNode)||void 0===e?void 0:e.nodeName)||!this.getStyle("transform",!1,!0).hasValue()||this.getStyle("transform-origin",!1,!0).hasValue()||this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(t),t.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),f&&(o=f[2],u=f[3]),r.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:i.viewPort.width,desiredWidth:o,height:i.viewPort.height,desiredHeight:u,minX:p,minY:d,refX:c.getValue(),refY:h.getValue(),clip:g,clipX:v,clipY:m}),f&&(i.viewPort.removeCurrent(),i.viewPort.setCurrent(o,u))}clearContext(t){super.clearContext(t),this.document.screen.viewPort.removeCurrent()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=this.getAttribute("width",!0),n=this.getAttribute("height",!0),s=this.getAttribute("viewBox"),a=this.getAttribute("style"),o=i.getNumber(0),u=n.getNumber(0);if(r)if("string"==typeof r)this.getAttribute("preserveAspectRatio",!0).setValue(r);else{var c=this.getAttribute("preserveAspectRatio");c.hasValue()&&c.setValue(c.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(i.setValue(t),n.setValue(e),s.hasValue()||s.setValue("0 0 ".concat(o||t," ").concat(u||e)),a.hasValue()){var h=this.getStyle("width"),l=this.getStyle("height");h.hasValue()&&h.setValue("".concat(t,"px")),l.hasValue()&&l.setValue("".concat(e,"px"))}}}class St extends dt{constructor(){super(...arguments),this.type="rect"}path(t){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),i=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),s=this.getAttribute("rx"),a=this.getAttribute("ry"),o=s.getPixels("x"),u=a.getPixels("y");if(s.hasValue()&&!a.hasValue()&&(u=o),a.hasValue()&&!s.hasValue()&&(o=u),o=Math.min(o,i/2),u=Math.min(u,n/2),t){var c=(Math.sqrt(2)-1)/3*4;t.beginPath(),n>0&&i>0&&(t.moveTo(e+o,r),t.lineTo(e+i-o,r),t.bezierCurveTo(e+i-o+c*o,r,e+i,r+u-c*u,e+i,r+u),t.lineTo(e+i,r+n-u),t.bezierCurveTo(e+i,r+n-u+c*u,e+i-o+c*o,r+n,e+i-o,r+n),t.lineTo(e+o,r+n),t.bezierCurveTo(e+o-c*o,r+n,e,r+n-u+c*u,e,r+n-u),t.lineTo(e,r+u),t.bezierCurveTo(e,r+u-c*u,e+o-c*o,r,e+o,r),t.closePath())}return new ft(e,r,e+i,r+n)}getMarkers(){return null}}class wt extends dt{constructor(){super(...arguments),this.type="circle"}path(t){var e=this.getAttribute("cx").getPixels("x"),r=this.getAttribute("cy").getPixels("y"),i=this.getAttribute("r").getPixels();return t&&i>0&&(t.beginPath(),t.arc(e,r,i,0,2*Math.PI,!1),t.closePath()),new ft(e-i,r-i,e+i,r+i)}getMarkers(){return null}}class Pt extends dt{constructor(){super(...arguments),this.type="ellipse"}path(t){var e=(Math.sqrt(2)-1)/3*4,r=this.getAttribute("rx").getPixels("x"),i=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),s=this.getAttribute("cy").getPixels("y");return t&&r>0&&i>0&&(t.beginPath(),t.moveTo(n+r,s),t.bezierCurveTo(n+r,s+e*i,n+e*r,s+i,n,s+i),t.bezierCurveTo(n-e*r,s+i,n-r,s+e*i,n-r,s),t.bezierCurveTo(n-r,s-e*i,n-e*r,s-i,n,s-i),t.bezierCurveTo(n+e*r,s-i,n+r,s-e*i,n+r,s),t.closePath()),new ft(n-r,s-i,n+r,s+i)}getMarkers(){return null}}class At extends dt{constructor(){super(...arguments),this.type="line"}getPoints(){return[new W(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new W(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(t){var[{x:e,y:r},{x:i,y:n}]=this.getPoints();return t&&(t.beginPath(),t.moveTo(e,r),t.lineTo(i,n)),new ft(e,r,i,n)}getMarkers(){var[t,e]=this.getPoints(),r=t.angleTo(e);return[[t,r],[e,r]]}}class Ct extends dt{constructor(t,e,r){super(t,e,r),this.type="polyline",this.points=[],this.points=W.parsePath(this.getAttribute("points").getString())}path(t){var{points:e}=this,[{x:r,y:i}]=e,n=new ft(r,i);return t&&(t.beginPath(),t.moveTo(r,i)),e.forEach(e=>{var{x:r,y:i}=e;n.addPoint(r,i),t&&t.lineTo(r,i)}),n}getMarkers(){var{points:t}=this,e=t.length-1,r=[];return t.forEach((i,n)=>{n!==e&&r.push([i,i.angleTo(t[n+1])])}),r.length>0&&r.push([t[t.length-1],r[r.length-1][1]]),r}}class Et extends Ct{constructor(){super(...arguments),this.type="polygon"}path(t){var e=super.path(t),[{x:r,y:i}]=this.points;return t&&(t.lineTo(r,i),t.closePath()),e}}class Tt extends at{constructor(){super(...arguments),this.type="pattern"}createPattern(t,e,r){var i=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),s=new bt(this.document,null);s.attributes.viewBox=new U(this.document,"viewBox",this.getAttribute("viewBox").getValue()),s.attributes.width=new U(this.document,"width","".concat(i,"px")),s.attributes.height=new U(this.document,"height","".concat(n,"px")),s.attributes.transform=new U(this.document,"transform",this.getAttribute("patternTransform").getValue()),s.children=this.children;var a=this.document.createCanvas(i,n),o=a.getContext("2d"),u=this.getAttribute("x"),c=this.getAttribute("y");u.hasValue()&&c.hasValue()&&o.translate(u.getPixels("x",!0),c.getPixels("y",!0)),r.hasValue()?this.styles["fill-opacity"]=r:Reflect.deleteProperty(this.styles,"fill-opacity");for(var h=-1;h<=1;h++)for(var l=-1;l<=1;l++)o.save(),s.attributes.x=new U(this.document,"x",h*a.width),s.attributes.y=new U(this.document,"y",l*a.height),s.render(o),o.restore();return t.createPattern(a,"repeat")}}class Ot extends at{constructor(){super(...arguments),this.type="marker"}render(t,e,r){if(e){var{x:i,y:n}=e,s=this.getAttribute("orient").getString("auto"),a=this.getAttribute("markerUnits").getString("strokeWidth");t.translate(i,n),"auto"===s&&t.rotate(r),"strokeWidth"===a&&t.scale(t.lineWidth,t.lineWidth),t.save();var o=new bt(this.document,null);o.type=this.type,o.attributes.viewBox=new U(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.refX=new U(this.document,"refX",this.getAttribute("refX").getValue()),o.attributes.refY=new U(this.document,"refY",this.getAttribute("refY").getValue()),o.attributes.width=new U(this.document,"width",this.getAttribute("markerWidth").getValue()),o.attributes.height=new U(this.document,"height",this.getAttribute("markerHeight").getValue()),o.attributes.overflow=new U(this.document,"overflow",this.getAttribute("overflow").getValue()),o.attributes.fill=new U(this.document,"fill",this.getAttribute("fill").getColor("black")),o.attributes.stroke=new U(this.document,"stroke",this.getAttribute("stroke").getValue("none")),o.children=this.children,o.render(t),t.restore(),"strokeWidth"===a&&t.scale(1/t.lineWidth,1/t.lineWidth),"auto"===s&&t.rotate(-r),t.translate(-i,-n)}}}class Mt extends at{constructor(){super(...arguments),this.type="defs"}render(){}}class Vt extends pt{constructor(){super(...arguments),this.type="g"}getBoundingBox(t){var e=new ft;return this.children.forEach(r=>{e.addBoundingBox(r.getBoundingBox(t))}),e}}class kt extends at{constructor(t,e,r){super(t,e,r),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:i,children:n}=this;n.forEach(t=>{"stop"===t.type&&i.push(t)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(t,e,r){var i=this;this.getHrefAttribute().hasValue()&&(i=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(i));var{stops:n}=i,s=this.getGradient(t,e);if(!s)return this.addParentOpacity(r,n[n.length-1].color);if(n.forEach(t=>{s.addColorStop(t.offset,this.addParentOpacity(r,t.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:a}=this,{MAX_VIRTUAL_PIXELS:o,viewPort:u}=a.screen,[c]=u.viewPorts,h=new St(a,null);h.attributes.x=new U(a,"x",-o/3),h.attributes.y=new U(a,"y",-o/3),h.attributes.width=new U(a,"width",o),h.attributes.height=new U(a,"height",o);var l=new Vt(a,null);l.attributes.transform=new U(a,"transform",this.getAttribute("gradientTransform").getValue()),l.children=[h];var f=new bt(a,null);f.attributes.x=new U(a,"x",0),f.attributes.y=new U(a,"y",0),f.attributes.width=new U(a,"width",c.width),f.attributes.height=new U(a,"height",c.height),f.children=[l];var g=a.createCanvas(c.width,c.height),p=g.getContext("2d");return p.fillStyle=s,f.render(p),p.createPattern(g,"no-repeat")}return s}inheritStopContainer(t){this.attributesToInherit.forEach(e=>{!this.getAttribute(e).hasValue()&&t.getAttribute(e).hasValue()&&this.getAttribute(e,!0).setValue(t.getAttribute(e).getValue())})}addParentOpacity(t,e){return t.hasValue()?new U(this.document,"color",e).addOpacity(t).getColor():e}}class Nt extends kt{constructor(t,e,r){super(t,e,r),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=r?e.getBoundingBox(t):null;if(r&&!i)return null;this.getAttribute("x1").hasValue()||this.getAttribute("y1").hasValue()||this.getAttribute("x2").hasValue()||this.getAttribute("y2").hasValue()||(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=r?i.x+i.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),s=r?i.y+i.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),a=r?i.x+i.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),o=r?i.y+i.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===a&&s===o?null:t.createLinearGradient(n,s,a,o)}}class Rt extends kt{constructor(t,e,r){super(t,e,r),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(t,e){var r="objectBoundingBox"===this.getGradientUnits(),i=e.getBoundingBox(t);if(r&&!i)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=r?i.x+i.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),s=r?i.y+i.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),a=n,o=s;this.getAttribute("fx").hasValue()&&(a=r?i.x+i.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(o=r?i.y+i.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var u=r?(i.width+i.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),c=this.getAttribute("fr").getPixels();return t.createRadialGradient(a,o,c,n,s,u)}}class It extends at{constructor(t,e,r){super(t,e,r),this.type="stop";var i=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),s=this.getStyle("stop-color",!0);""===s.getString()&&s.setValue("#000"),n.hasValue()&&(s=s.addOpacity(n)),this.offset=i,this.color=s.getColor()}}class Lt extends at{constructor(t,e,r){super(t,e,r),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,t.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new U(t,"values",null);var i=this.getAttribute("values");i.hasValue()&&this.values.setValue(i.getString().split(";"))}getProperty(){var t=this.getAttribute("attributeType").getString(),e=this.getAttribute("attributeName").getString();return"CSS"===t?this.parent.getStyle(e,!0):this.parent.getAttribute(e,!0)}calcValue(){var{initialUnits:t}=this,{progress:e,from:r,to:i}=this.getProgress(),n=r.getNumber()+(i.getNumber()-r.getNumber())*e;return"%"===t&&(n*=100),"".concat(n).concat(t)}update(t){var{parent:e}=this,r=this.getProperty();if(this.initialValue||(this.initialValue=r.getString(),this.initialUnits=r.getUnits()),this.duration>this.maxDuration){var i=this.getAttribute("fill").getString("remove");if("indefinite"===this.getAttribute("repeatCount").getString()||"indefinite"===this.getAttribute("repeatDur").getString())this.duration=0;else if("freeze"!==i||this.frozen){if("remove"===i&&!this.removed)return this.removed=!0,r.setValue(e.animationFrozen?e.animationFrozenValue:this.initialValue),!0}else this.frozen=!0,e.animationFrozen=!0,e.animationFrozenValue=r.getString();return!1}this.duration+=t;var n=!1;if(this.begin<this.duration){var s=this.calcValue(),a=this.getAttribute("type");if(a.hasValue()){var o=a.getString();s="".concat(o,"(").concat(s,")")}r.setValue(s),n=!0}return n}getProgress(){var{document:t,values:e}=this,r={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(e.hasValue()){var i=r.progress*(e.getValue().length-1),n=Math.floor(i),s=Math.ceil(i);r.from=new U(t,"from",parseFloat(e.getValue()[n])),r.to=new U(t,"to",parseFloat(e.getValue()[s])),r.progress=(i-n)/(s-n)}else r.from=this.from,r.to=this.to;return r}}class Dt extends Lt{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=new h.a(e.getColor()),n=new h.a(r.getColor());if(i.ok&&n.ok){var s=i.r+(n.r-i.r)*t,a=i.g+(n.g-i.g)*t,o=i.b+(n.b-i.b)*t;return"rgb(".concat(Math.floor(s),", ").concat(Math.floor(a),", ").concat(Math.floor(o),")")}return this.getAttribute("from").getColor()}}class jt extends Lt{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:t,from:e,to:r}=this.getProgress(),i=y(e.getString()),n=y(r.getString());return i.map((e,r)=>e+(n[r]-e)*t).join(" ")}}class Bt extends at{constructor(t,e,r){super(t,e,r),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:i}=t,{children:n}=this;for(var s of n)switch(s.type){case"font-face":this.fontFace=s;var a=s.getStyle("font-family");a.hasValue()&&(i[a.getString()]=this);break;case"missing-glyph":this.missingGlyph=s;break;case"glyph":var o=s;o.arabicForm?(this.isRTL=!0,this.isArabic=!0,void 0===this.glyphs[o.unicode]&&(this.glyphs[o.unicode]={}),this.glyphs[o.unicode][o.arabicForm]=o):this.glyphs[o.unicode]=o}}render(){}}class zt extends at{constructor(t,e,r){super(t,e,r),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}}class _t extends dt{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}}class Ft extends yt{constructor(){super(...arguments),this.type="tref"}getText(){var t=this.getHrefAttribute().getDefinition();if(t){var e=t.children[0];if(e)return e.getText()}return""}}class Ut extends yt{constructor(t,e,r){super(t,e,r),this.type="a";var{childNodes:i}=e,n=i[0],s=i.length>0&&Array.from(i).every(t=>3===t.nodeType);this.hasText=s,this.text=s?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(t){if(this.hasText){super.renderChildren(t);var{document:e,x:r,y:i}=this,{mouse:n}=e.screen,s=new U(e,"fontSize",lt.parse(e.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new ft(r,i-s.getPixels("y"),r+this.measureText(t),i))}else if(this.children.length>0){var a=new Vt(this.document,null);a.children=this.children,a.parent=this,a.render(t)}}onClick(){var{window:t}=this.document;t&&t.open(this.getHrefAttribute().getString())}onMouseMove(){this.document.ctx.canvas.style.cursor="pointer"}}function Xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function Wt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xt(Object(r),!0).forEach((function(e){a()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}class Yt extends yt{constructor(t,e,r){super(t,e,r),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var i=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(i)}getText(){return this.text}path(t){var{dataArray:e}=this;t&&t.beginPath(),e.forEach(e=>{var{type:r,points:i}=e;switch(r){case gt.LINE_TO:t&&t.lineTo(i[0],i[1]);break;case gt.MOVE_TO:t&&t.moveTo(i[0],i[1]);break;case gt.CURVE_TO:t&&t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case gt.QUAD_TO:t&&t.quadraticCurveTo(i[0],i[1],i[2],i[3]);break;case gt.ARC:var[n,s,a,o,u,c,h,l]=i,f=a>o?a:o,g=a>o?1:a/o,p=a>o?o/a:1;t&&(t.translate(n,s),t.rotate(h),t.scale(g,p),t.arc(0,0,f,u,u+c,Boolean(1-l)),t.scale(1/g,1/p),t.rotate(-h),t.translate(-n,-s));break;case gt.CLOSE_PATH:t&&t.closePath()}})}renderChildren(t){this.setTextData(t),t.save();var e=this.parent.getStyle("text-decoration").getString(),r=this.getFontSize(),{glyphInfo:i}=this,n=t.fillStyle;"underline"===e&&t.beginPath(),i.forEach((i,n)=>{var{p0:s,p1:a,rotation:o,text:u}=i;t.save(),t.translate(s.x,s.y),t.rotate(o),t.fillStyle&&t.fillText(u,0,0),t.strokeStyle&&t.strokeText(u,0,0),t.restore(),"underline"===e&&(0===n&&t.moveTo(s.x,s.y+r/8),t.lineTo(a.x,a.y+r/5))}),"underline"===e&&(t.lineWidth=r/20,t.strokeStyle=n,t.stroke(),t.closePath()),t.restore()}getLetterSpacingAt(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return this.letterSpacingCache[t]||0}findSegmentToFitChar(t,e,r,i,n,s,a,o,u){var c=s,h=this.measureText(t,o);" "===o&&"justify"===e&&r<i&&(h+=(i-r)/n),u>-1&&(c+=this.getLetterSpacingAt(u));var l=this.textHeight/20,f=this.getEquidistantPointOnPath(c,l,0),g=this.getEquidistantPointOnPath(c+h,l,0),p={p0:f,p1:g},d=f&&g?Math.atan2(g.y-f.y,g.x-f.x):0;if(a){var v=Math.cos(Math.PI/2+d)*a,y=Math.cos(-d)*a;p.p0=Wt(Wt({},f),{},{x:f.x+v,y:f.y+y}),p.p1=Wt(Wt({},g),{},{x:g.x+v,y:g.y+y})}return{offset:c+=h,segment:p,rotation:d}}measureText(t,e){var{measuresCache:r}=this,i=e||this.getText();if(r.has(i))return r.get(i);var n=this.measureTargetText(t,i);return r.set(i,n),n}setTextData(t){if(!this.glyphInfo){var e=this.getText(),r=e.split(""),i=e.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(t=>t.getPixels("x")),s=this.parent.getAttribute("dy").getPixels("y"),a=this.parent.getStyle("text-anchor").getString("start"),o=this.getStyle("letter-spacing"),u=this.parent.getStyle("letter-spacing"),c=0;o.hasValue()&&"inherit"!==o.getValue()?o.hasValue()&&"initial"!==o.getValue()&&"unset"!==o.getValue()&&(c=o.getPixels()):c=u.getPixels();var h=[],l=e.length;this.letterSpacingCache=h;for(var f=0;f<l;f++)h.push(void 0!==n[f]?n[f]:c);var g=h.reduce((t,e,r)=>0===r?0:t+e||0,0),p=this.measureText(t),d=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var v=this.getPathLength(),y=this.getStyle("startOffset").getNumber(0)*v,m=0;"middle"!==a&&"center"!==a||(m=-d/2),"end"!==a&&"right"!==a||(m=-d),m+=y,r.forEach((e,n)=>{var{offset:o,segment:u,rotation:c}=this.findSegmentToFitChar(t,a,d,v,i,m,s,e,n);m=o,u.p0&&u.p1&&this.glyphInfo.push({text:r[n],p0:u.p0,p1:u.p1,rotation:c})})}}parsePathData(t){if(this.pathLength=-1,!t)return[];var e=[],{pathParser:r}=t;for(r.reset();!r.isEnd();){var{current:i}=r,n=i?i.x:0,s=i?i.y:0,a=r.next(),o=a.type,u=[];switch(a.type){case gt.MOVE_TO:this.pathM(r,u);break;case gt.LINE_TO:o=this.pathL(r,u);break;case gt.HORIZ_LINE_TO:o=this.pathH(r,u);break;case gt.VERT_LINE_TO:o=this.pathV(r,u);break;case gt.CURVE_TO:this.pathC(r,u);break;case gt.SMOOTH_CURVE_TO:o=this.pathS(r,u);break;case gt.QUAD_TO:this.pathQ(r,u);break;case gt.SMOOTH_QUAD_TO:o=this.pathT(r,u);break;case gt.ARC:u=this.pathA(r);break;case gt.CLOSE_PATH:dt.pathZ(r)}a.type!==gt.CLOSE_PATH?e.push({type:o,points:u,start:{x:n,y:s},pathLength:this.calcLength(n,s,o,u)}):e.push({type:gt.CLOSE_PATH,points:[],pathLength:0})}return e}pathM(t,e){var{x:r,y:i}=dt.pathM(t).point;e.push(r,i)}pathL(t,e){var{x:r,y:i}=dt.pathL(t).point;return e.push(r,i),gt.LINE_TO}pathH(t,e){var{x:r,y:i}=dt.pathH(t).point;return e.push(r,i),gt.LINE_TO}pathV(t,e){var{x:r,y:i}=dt.pathV(t).point;return e.push(r,i),gt.LINE_TO}pathC(t,e){var{point:r,controlPoint:i,currentPoint:n}=dt.pathC(t);e.push(r.x,r.y,i.x,i.y,n.x,n.y)}pathS(t,e){var{point:r,controlPoint:i,currentPoint:n}=dt.pathS(t);return e.push(r.x,r.y,i.x,i.y,n.x,n.y),gt.CURVE_TO}pathQ(t,e){var{controlPoint:r,currentPoint:i}=dt.pathQ(t);e.push(r.x,r.y,i.x,i.y)}pathT(t,e){var{controlPoint:r,currentPoint:i}=dt.pathT(t);return e.push(r.x,r.y,i.x,i.y),gt.QUAD_TO}pathA(t){var{rX:e,rY:r,sweepFlag:i,xAxisRotation:n,centp:s,a1:a,ad:o}=dt.pathA(t);return 0===i&&o>0&&(o-=2*Math.PI),1===i&&o<0&&(o+=2*Math.PI),[s.x,s.y,e,r,a,o,n,i]}calcLength(t,e,r,i){var n=0,s=null,a=null,o=0;switch(r){case gt.LINE_TO:return this.getLineLength(t,e,i[0],i[1]);case gt.CURVE_TO:for(n=0,s=this.getPointOnCubicBezier(0,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),o=.01;o<=1;o+=.01)a=this.getPointOnCubicBezier(o,t,e,i[0],i[1],i[2],i[3],i[4],i[5]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case gt.QUAD_TO:for(n=0,s=this.getPointOnQuadraticBezier(0,t,e,i[0],i[1],i[2],i[3]),o=.01;o<=1;o+=.01)a=this.getPointOnQuadraticBezier(o,t,e,i[0],i[1],i[2],i[3]),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return n;case gt.ARC:n=0;var u=i[4],c=i[5],h=i[4]+c,l=Math.PI/180;if(Math.abs(u-h)<l&&(l=Math.abs(u-h)),s=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],u,0),c<0)for(o=u-l;o>h;o-=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;else for(o=u+l;o<h;o+=l)a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],o,0),n+=this.getLineLength(s.x,s.y,a.x,a.y),s=a;return a=this.getPointOnEllipticalArc(i[0],i[1],i[2],i[3],h,0),n+=this.getLineLength(s.x,s.y,a.x,a.y)}return 0}getPointOnLine(t,e,r,i,n){var s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:r,o=(n-r)/(i-e+k),u=Math.sqrt(t*t/(1+o*o));i<e&&(u*=-1);var c=o*u,h=null;if(i===e)h={x:s,y:a+c};else if((a-r)/(s-e+k)===o)h={x:s+u,y:a+c};else{var l,f,g=this.getLineLength(e,r,i,n);if(g<k)return null;var p=(s-e)*(i-e)+(a-r)*(n-r);l=e+(p/=g*g)*(i-e),f=r+p*(n-r);var d=this.getLineLength(s,a,l,f),v=Math.sqrt(t*t-d*d);u=Math.sqrt(v*v/(1+o*o)),i<e&&(u*=-1),h={x:l+u,y:f+(c=o*u)}}return h}getPointOnPath(t){var e=this.getPathLength(),r=0,i=null;if(t<-5e-5||t-5e-5>e)return null;var{dataArray:n}=this;for(var s of n){if(!s||!(s.pathLength<5e-5||r+s.pathLength+5e-5<t)){var a=t-r,o=0;switch(s.type){case gt.LINE_TO:i=this.getPointOnLine(a,s.start.x,s.start.y,s.points[0],s.points[1],s.start.x,s.start.y);break;case gt.ARC:var u=s.points[4],c=s.points[5],h=s.points[4]+c;if(o=u+a/s.pathLength*c,c<0&&o<h||c>=0&&o>h)break;i=this.getPointOnEllipticalArc(s.points[0],s.points[1],s.points[2],s.points[3],o,s.points[6]);break;case gt.CURVE_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnCubicBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3],s.points[4],s.points[5]);break;case gt.QUAD_TO:(o=a/s.pathLength)>1&&(o=1),i=this.getPointOnQuadraticBezier(o,s.start.x,s.start.y,s.points[0],s.points[1],s.points[2],s.points[3])}if(i)return i;break}r+=s.pathLength}return null}getLineLength(t,e,r,i){return Math.sqrt((r-t)*(r-t)+(i-e)*(i-e))}getPathLength(){return-1===this.pathLength&&(this.pathLength=this.dataArray.reduce((t,e)=>e.pathLength>0?t+e.pathLength:t,0)),this.pathLength}getPointOnCubicBezier(t,e,r,i,n,s,a,o,u){return{x:o*L(t)+s*D(t)+i*j(t)+e*B(t),y:u*L(t)+a*D(t)+n*j(t)+r*B(t)}}getPointOnQuadraticBezier(t,e,r,i,n,s,a){return{x:s*z(t)+i*_(t)+e*F(t),y:a*z(t)+n*_(t)+r*F(t)}}getPointOnEllipticalArc(t,e,r,i,n,s){var a=Math.cos(s),o=Math.sin(s),u=r*Math.cos(n),c=i*Math.sin(n);return{x:t+(u*a-c*o),y:e+(u*o+c*a)}}buildEquidistantCache(t,e){var r=this.getPathLength(),i=e||.25,n=t||r/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==i){this.equidistantCache={step:n,precision:i,points:[]};for(var s=0,a=0;a<=r;a+=i){var o=this.getPointOnPath(a),u=this.getPointOnPath(a+i);o&&u&&((s+=this.getLineLength(o.x,o.y,u.x,u.y))>=n&&(this.equidistantCache.points.push({x:o.x,y:o.y,distance:a}),s-=n))}}}getEquidistantPointOnPath(t,e,r){if(this.buildEquidistantCache(e,r),t<0||t-this.getPathLength()>5e-5)return null;var i=Math.round(t/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[i]||null}}var Gt=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i;class Ht extends pt{constructor(t,e,r){super(t,e,r),this.type="image",this.loaded=!1;var i=this.getHrefAttribute().getString();if(i){var n=i.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(i);t.images.push(this),n?this.loadSvg(i):this.loadImage(i),this.isSvg=n}}loadImage(t){var e=this;return n()((function*(){try{var r=yield e.document.createImage(t);e.image=r}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0}))()}loadSvg(t){var e=this;return n()((function*(){var r=Gt.exec(t);if(r){var i=r[5];"base64"===r[4]?e.image=atob(i):e.image=decodeURIComponent(i)}else try{var n=yield e.document.fetch(t),s=yield n.text();e.image=s}catch(e){console.error('Error while loading image "'.concat(t,'":'),e)}e.loaded=!0}))()}renderChildren(t){var{document:e,image:r,loaded:i}=this,n=this.getAttribute("x").getPixels("x"),s=this.getAttribute("y").getPixels("y"),a=this.getStyle("width").getPixels("x"),o=this.getStyle("height").getPixels("y");if(i&&r&&a&&o){if(t.save(),t.translate(n,s),this.isSvg){var u=e.canvg.forkString(t,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:a,scaleHeight:o});u.document.documentElement.parent=this,u.render()}else{var c=this.image;e.setViewBox({ctx:t,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a,desiredWidth:c.width,height:o,desiredHeight:c.height}),this.loaded&&(void 0===c.complete||c.complete)&&t.drawImage(c,0,0)}t.restore()}}getBoundingBox(){var t=this.getAttribute("x").getPixels("x"),e=this.getAttribute("y").getPixels("y"),r=this.getStyle("width").getPixels("x"),i=this.getStyle("height").getPixels("y");return new ft(t,e,t+r,e+i)}}class $t extends pt{constructor(){super(...arguments),this.type="symbol"}render(t){}}class qt{constructor(t){this.document=t,this.loaded=!1,t.fonts.push(this)}load(t,e){var r=this;return n()((function*(){try{var{document:i}=r,n=(yield i.canvg.parser.load(e)).getElementsByTagName("font");Array.from(n).forEach(e=>{var r=i.createElement(e);i.definitions[t]=r})}catch(t){console.error('Error while loading font "'.concat(e,'":'),t)}r.loaded=!0}))()}}class Qt extends at{constructor(t,e,r){super(t,e,r),this.type="style",p(Array.from(e.childNodes).map(t=>t.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")).split("}").forEach(e=>{var r=e.trim();if(r){var i=r.split("{"),n=i[0].split(","),s=i[1].split(";");n.forEach(e=>{var r=e.trim();if(r){var i=t.styles[r]||{};if(s.forEach(e=>{var r=e.indexOf(":"),n=e.substr(0,r).trim(),s=e.substr(r+1,e.length-r).trim();n&&s&&(i[n]=new U(t,n,s))}),t.styles[r]=i,t.stylesSpecificity[r]=V(r),"@font-face"===r){var n=i["font-family"].getString().replace(/"|'/g,"");i.src.getString().split(",").forEach(e=>{if(e.indexOf('format("svg")')>0){var r=b(e);r&&new qt(t).load(n,r)}})}}})}})}}Qt.parseExternalUrl=b;class Zt extends pt{constructor(){super(...arguments),this.type="use"}setContext(t){super.setContext(t);var e=this.getAttribute("x"),r=this.getAttribute("y");e.hasValue()&&t.translate(e.getPixels("x"),0),r.hasValue()&&t.translate(0,r.getPixels("y"))}path(t){var{element:e}=this;e&&e.path(t)}renderChildren(t){var{document:e,element:r}=this;if(r){var i=r;if("symbol"===r.type&&((i=new bt(e,null)).attributes.viewBox=new U(e,"viewBox",r.getAttribute("viewBox").getString()),i.attributes.preserveAspectRatio=new U(e,"preserveAspectRatio",r.getAttribute("preserveAspectRatio").getString()),i.attributes.overflow=new U(e,"overflow",r.getAttribute("overflow").getString()),i.children=r.children,r.styles.opacity=new U(e,"opacity",this.calculateOpacity())),"svg"===i.type){var n=this.getStyle("width",!1,!0),s=this.getStyle("height",!1,!0);n.hasValue()&&(i.attributes.width=new U(e,"width",n.getString())),s.hasValue()&&(i.attributes.height=new U(e,"height",s.getString()))}var a=i.parent;i.parent=this,i.render(t),i.parent=a}}getBoundingBox(t){var{element:e}=this;return e?e.getBoundingBox(t):null}elementTransform(){var{document:t,element:e}=this;return st.fromElement(t,e)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}}function Jt(t,e,r,i,n,s){return t[r*i*4+4*e+s]}function Kt(t,e,r,i,n,s,a){t[r*i*4+4*e+s]=a}function te(t,e,r){return t[e]*r}function ee(t,e,r,i){return e+Math.cos(t)*r+Math.sin(t)*i}class re extends at{constructor(t,e,r){super(t,e,r),this.type="feColorMatrix";var i=y(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":var n=i[0];i=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break;case"hueRotate":var s=i[0]*Math.PI/180;i=[ee(s,.213,.787,-.213),ee(s,.715,-.715,-.715),ee(s,.072,-.072,.928),0,0,ee(s,.213,-.213,.143),ee(s,.715,.285,.14),ee(s,.072,-.072,-.283),0,0,ee(s,.213,-.213,-.787),ee(s,.715,-.715,.715),ee(s,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break;case"luminanceToAlpha":i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1]}this.matrix=i,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(t,e,r,i,n){for(var{includeOpacity:s,matrix:a}=this,o=t.getImageData(0,0,i,n),u=0;u<n;u++)for(var c=0;c<i;c++){var h=Jt(o.data,c,u,i,0,0),l=Jt(o.data,c,u,i,0,1),f=Jt(o.data,c,u,i,0,2),g=Jt(o.data,c,u,i,0,3),p=te(a,0,h)+te(a,1,l)+te(a,2,f)+te(a,3,g)+te(a,4,1),d=te(a,5,h)+te(a,6,l)+te(a,7,f)+te(a,8,g)+te(a,9,1),v=te(a,10,h)+te(a,11,l)+te(a,12,f)+te(a,13,g)+te(a,14,1),y=te(a,15,h)+te(a,16,l)+te(a,17,f)+te(a,18,g)+te(a,19,1);s&&(p=0,d=0,v=0,y*=g/255),Kt(o.data,c,u,i,0,0,p),Kt(o.data,c,u,i,0,1,d),Kt(o.data,c,u,i,0,2,v),Kt(o.data,c,u,i,0,3,y)}t.clearRect(0,0,i,n),t.putImageData(o,0,0)}}class ie extends at{constructor(){super(...arguments),this.type="mask"}apply(t,e){var{document:r}=this,i=this.getAttribute("x").getPixels("x"),n=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");if(!s&&!a){var o=new ft;this.children.forEach(e=>{o.addBoundingBox(e.getBoundingBox(t))}),i=Math.floor(o.x1),n=Math.floor(o.y1),s=Math.floor(o.width),a=Math.floor(o.height)}var u=this.removeStyles(e,ie.ignoreStyles),c=r.createCanvas(i+s,n+a),h=c.getContext("2d");r.screen.setDefaults(h),this.renderChildren(h),new re(r,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(h,0,0,i+s,n+a);var l=r.createCanvas(i+s,n+a),f=l.getContext("2d");r.screen.setDefaults(f),e.render(f),f.globalCompositeOperation="destination-in",f.fillStyle=h.createPattern(c,"no-repeat"),f.fillRect(0,0,i+s,n+a),t.fillStyle=f.createPattern(l,"no-repeat"),t.fillRect(0,0,i+s,n+a),this.restoreStyles(e,u)}render(t){}}ie.ignoreStyles=["mask","transform","clip-path"];var ne=()=>{};class se extends at{constructor(){super(...arguments),this.type="clipPath"}apply(t){var{document:e}=this,r=Reflect.getPrototypeOf(t),{beginPath:i,closePath:n}=t;r&&(r.beginPath=ne,r.closePath=ne),Reflect.apply(i,t,[]),this.children.forEach(i=>{if(void 0!==i.path){var s=void 0!==i.elementTransform?i.elementTransform():null;s||(s=st.fromElement(e,i)),s&&s.apply(t),i.path(t),r&&(r.closePath=n),s&&s.unapply(t)}}),Reflect.apply(n,t,[]),t.clip(),r&&(r.beginPath=i,r.closePath=n)}render(t){}}class ae extends at{constructor(){super(...arguments),this.type="filter"}apply(t,e){var{document:r,children:i}=this,n=e.getBoundingBox(t);if(n){var s=0,a=0;i.forEach(t=>{var e=t.extraFilterDistance||0;s=Math.max(s,e),a=Math.max(a,e)});var o=Math.floor(n.width),u=Math.floor(n.height),c=o+2*s,h=u+2*a;if(!(c<1||h<1)){var l=Math.floor(n.x),f=Math.floor(n.y),g=this.removeStyles(e,ae.ignoreStyles),p=r.createCanvas(c,h),d=p.getContext("2d");r.screen.setDefaults(d),d.translate(-l+s,-f+a),e.render(d),i.forEach(t=>{"function"==typeof t.apply&&t.apply(d,0,0,c,h)}),t.drawImage(p,0,0,c,h,l-s,f-a,c,h),this.restoreStyles(e,g)}}}render(t){}}ae.ignoreStyles=["filter","transform","clip-path"];class oe extends at{constructor(t,e,r){super(t,e,r),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(t,e,r,i,n){}}class ue extends at{constructor(){super(...arguments),this.type="feMorphology"}apply(t,e,r,i,n){}}class ce extends at{constructor(){super(...arguments),this.type="feComposite"}apply(t,e,r,i,n){}}class he extends at{constructor(t,e,r){super(t,e,r),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(t,e,r,i,n){var{document:s,blurRadius:a}=this,o=s.window?s.window.document.body:null,u=t.canvas;u.id=s.getUniqueId(),o&&(u.style.display="none",o.appendChild(u)),Object(f.a)(u,e,r,i,n,a),o&&o.removeChild(u)}}class le extends at{constructor(){super(...arguments),this.type="title"}}class fe extends at{constructor(){super(...arguments),this.type="desc"}}var ge={svg:bt,rect:St,circle:wt,ellipse:Pt,line:At,polyline:Ct,polygon:Et,path:dt,pattern:Tt,marker:Ot,defs:Mt,linearGradient:Nt,radialGradient:Rt,stop:It,animate:Lt,animateColor:Dt,animateTransform:jt,font:Bt,"font-face":zt,"missing-glyph":_t,glyph:vt,text:yt,tspan:mt,tref:Ft,a:Ut,textPath:Yt,image:Ht,g:Vt,symbol:$t,style:Qt,use:Zt,mask:ie,clipPath:se,filter:ae,feDropShadow:oe,feMorphology:ue,feComposite:ce,feColorMatrix:re,feGaussianBlur:he,title:le,desc:fe};function pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function de(){return(de=n()((function*(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((e,i)=>{r.onload=()=>{e(r)},r.onerror=(t,e,r,n,s)=>{i(s)},r.src=t})}))).apply(this,arguments)}class ve{constructor(t){var{rootEmSize:e=12,emSize:r=12,createCanvas:i=ve.createCanvas,createImage:n=ve.createImage,anonymousCrossOrigin:s}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.canvg=t,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=t.screen,this.rootEmSize=e,this.emSize=r,this.createCanvas=i,this.createImage=this.bindCreateImage(n,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(t,e){return"boolean"==typeof e?(r,i)=>t(r,"boolean"==typeof i?i:e):t}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:t}=this;return t[t.length-1]}set emSize(t){var{emSizeStack:e}=this;e.push(t)}popEmSize(){var{emSizeStack:t}=this;t.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(t=>t.loaded)}isFontsLoaded(){return this.fonts.every(t=>t.loaded)}createDocumentElement(t){var e=this.createElement(t.documentElement);return e.root=!0,e.addStylesFromStyleDefinition(),this.documentElement=e,e}createElement(t){var e=t.nodeName.replace(/^[^:]+:/,""),r=ve.elementTypes[e];return void 0!==r?new r(this,t):new ot(this,t)}createTextNode(t){return new xt(this,t)}setViewBox(t){this.screen.setViewBox(function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pe(Object(r),!0).forEach((function(e){a()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({document:this},t))}}function ye(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function me(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ye(Object(r),!0).forEach((function(e){a()(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}ve.createCanvas=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,r},ve.createImage=function(t){return de.apply(this,arguments)},ve.elementTypes=ge;class xe{constructor(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.parser=new Z(r),this.screen=new $(t,r),this.options=r;var i=new ve(this,r),n=i.createDocumentElement(e);this.document=i,this.documentElement=n}static from(t,e){var r=arguments;return n()((function*(){var i=r.length>2&&void 0!==r[2]?r[2]:{},n=new Z(i),s=yield n.parse(e);return new xe(t,s,i)}))()}static fromString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=new Z(r).parseFromString(e);return new xe(t,i,r)}fork(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return xe.from(t,e,me(me({},this.options),r))}forkString(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return xe.fromString(t,e,me(me({},this.options),r))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var t=arguments,e=this;return n()((function*(){var r=t.length>0&&void 0!==t[0]?t[0]:{};e.start(me({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},r)),yield e.ready(),e.stop()}))()}start(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{documentElement:e,screen:r,options:i}=this;r.start(e,me(me({enableRedraw:!0},i),t))}stop(){this.screen.stop()}resize(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.documentElement.resize(t,e,r)}}}.call(this,r(66))},784:function(t,e,r){"use strict";(function(e){var r=function(t){return t&&t.Math===Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof e&&e)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()}).call(this,r(49))},786:function(t,e,r){"use strict";t.exports=function(t){try{return!!t()}catch(t){return!0}}},787:function(t,e,r){"use strict";var i=r(842),n=Function.prototype,s=n.call,a=i&&n.bind.bind(s,s);t.exports=i?a:function(t){return function(){return s.apply(t,arguments)}}},788:function(t,e,r){"use strict";var i="object"==typeof document&&document.all;t.exports=void 0===i&&void 0!==i?function(t){return"function"==typeof t||t===i}:function(t){return"function"==typeof t}},792:function(t,e,r){"use strict";var i=r(784),n=r(870),s=r(804),a=r(911),o=r(910),u=r(909),c=i.Symbol,h=n("wks"),l=u?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return s(h,t)||(h[t]=o&&s(c,t)?c[t]:l("Symbol."+t)),h[t]}},794:function(t,e,r){"use strict";var i=r(842),n=Function.prototype.call;t.exports=i?n.bind(n):function(){return n.apply(n,arguments)}},796:function(t,e,r){"use strict";var i=r(784),n=r(841).f,s=r(829),a=r(819),o=r(872),u=r(976),c=r(917);t.exports=function(t,e){var r,h,l,f,g,p=t.target,d=t.global,v=t.stat;if(r=d?i:v?i[p]||o(p,{}):i[p]&&i[p].prototype)for(h in e){if(f=e[h],l=t.dontCallGetSet?(g=n(r,h))&&g.value:r[h],!c(d?h:p+(v?".":"#")+h,t.forced)&&void 0!==l){if(typeof f==typeof l)continue;u(f,l)}(t.sham||l&&l.sham)&&s(f,"sham",!0),a(r,h,f,t)}}},797:function(t,e,r){"use strict";var i=r(803),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not an object")}},801:function(t,e,r){"use strict";var i=r(786);t.exports=!i((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},802:function(t,e,r){"use strict";var i=r(817),n=TypeError;t.exports=function(t){if(i(t))throw new n("Can't call method on "+t);return t}},803:function(t,e,r){"use strict";var i=r(788);t.exports=function(t){return"object"==typeof t?null!==t:i(t)}},804:function(t,e,r){"use strict";var i=r(787),n=r(845),s=i({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return s(n(t),e)}},808:function(t,e,r){"use strict";t.exports=!1},809:function(t,e,r){"use strict";var i=r(878),n=String;t.exports=function(t){if("Symbol"===i(t))throw new TypeError("Cannot convert a Symbol value to a string");return n(t)}},812:function(t,e,r){"use strict";var i=r(788),n=r(844),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a function")}},814:function(t,e,r){"use strict";var i=r(801),n=r(912),s=r(913),a=r(797),o=r(907),u=TypeError,c=Object.defineProperty,h=Object.getOwnPropertyDescriptor;e.f=i?s?function(t,e,r){if(a(t),e=o(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&"writable"in r&&!r.writable){var i=h(t,e);i&&i.writable&&(t[e]=r.value,r={configurable:"configurable"in r?r.configurable:i.configurable,enumerable:"enumerable"in r?r.enumerable:i.enumerable,writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=o(e),a(r),n)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new u("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},816:function(t,e,r){"use strict";var i=r(787),n=i({}.toString),s=i("".slice);t.exports=function(t){return s(n(t),8,-1)}},817:function(t,e,r){"use strict";t.exports=function(t){return null==t}},818:function(t,e,r){"use strict";var i=r(784),n=r(788),s=function(t){return n(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?s(i[t]):i[t]&&i[t][e]}},819:function(t,e,r){"use strict";var i=r(788),n=r(814),s=r(914),a=r(872);t.exports=function(t,e,r,o){o||(o={});var u=o.enumerable,c=void 0!==o.name?o.name:e;if(i(r)&&s(r,c,o),o.global)u?t[e]=r:a(e,r);else{try{o.unsafe?t[e]&&(u=!0):delete t[e]}catch(t){}u?t[e]=r:n.f(t,e,{value:r,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},823:function(t,e,r){"use strict";var i=r(812),n=r(817);t.exports=function(t,e){var r=t[e];return n(r)?void 0:i(r)}},824:function(t,e,r){"use strict";var i=r(849),n=Math.min;t.exports=function(t){var e=i(t);return e>0?n(e,9007199254740991):0}},827:function(t,e,r){"use strict";var i=r(906),n=r(802);t.exports=function(t){return i(n(t))}},828:function(t,e,r){"use strict";var i=r(784).navigator,n=i&&i.userAgent;t.exports=n?String(n):""},829:function(t,e,r){"use strict";var i=r(801),n=r(814),s=r(868);t.exports=i?function(t,e,r){return n.f(t,e,s(1,r))}:function(t,e,r){return t[e]=r,t}},830:function(t,e,r){"use strict";var i=r(784);t.exports=i.Promise},831:function(t,e,r){"use strict";var i=r(784),n=r(830),s=r(788),a=r(917),o=r(873),u=r(792),c=r(918),h=r(808),l=r(869),f=n&&n.prototype,g=u("species"),p=!1,d=s(i.PromiseRejectionEvent),v=a("Promise",(function(){var t=o(n),e=t!==String(n);if(!e&&66===l)return!0;if(h&&(!f.catch||!f.finally))return!0;if(!l||l<51||!/native code/.test(t)){var r=new n((function(t){t(1)})),i=function(t){t((function(){}),(function(){}))};if((r.constructor={})[g]=i,!(p=r.then((function(){}))instanceof i))return!0}return!(e||"BROWSER"!==c&&"DENO"!==c||d)}));t.exports={CONSTRUCTOR:v,REJECTION_EVENT:d,SUBCLASSING:p}},832:function(t,e,r){"use strict";var i=r(812),n=TypeError,s=function(t){var e,r;this.promise=new t((function(t,i){if(void 0!==e||void 0!==r)throw new n("Bad Promise constructor");e=t,r=i})),this.resolve=i(e),this.reject=i(r)};t.exports.f=function(t){return new s(t)}},833:function(t,e,r){"use strict";t.exports={}},841:function(t,e,r){"use strict";var i=r(801),n=r(794),s=r(972),a=r(868),o=r(827),u=r(907),c=r(804),h=r(912),l=Object.getOwnPropertyDescriptor;e.f=i?l:function(t,e){if(t=o(t),e=u(e),h)try{return l(t,e)}catch(t){}if(c(t,e))return a(!n(s.f,t,e),t[e])}},842:function(t,e,r){"use strict";var i=r(786);t.exports=!i((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},843:function(t,e,r){"use strict";var i=r(787);t.exports=i({}.isPrototypeOf)},844:function(t,e,r){"use strict";var i=String;t.exports=function(t){try{return i(t)}catch(t){return"Object"}}},845:function(t,e,r){"use strict";var i=r(802),n=Object;t.exports=function(t){return n(i(t))}},846:function(t,e,r){"use strict";var i=r(784),n=r(803),s=i.document,a=n(s)&&n(s.createElement);t.exports=function(t){return a?s.createElement(t):{}}},847:function(t,e,r){"use strict";var i=r(801),n=r(804),s=Function.prototype,a=i&&Object.getOwnPropertyDescriptor,o=n(s,"name"),u=o&&"something"===function(){}.name,c=o&&(!i||i&&a(s,"name").configurable);t.exports={EXISTS:o,PROPER:u,CONFIGURABLE:c}},848:function(t,e,r){"use strict";var i,n,s,a=r(975),o=r(784),u=r(803),c=r(829),h=r(804),l=r(871),f=r(874),g=r(875),p=o.TypeError,d=o.WeakMap;if(a||l.state){var v=l.state||(l.state=new d);v.get=v.get,v.has=v.has,v.set=v.set,i=function(t,e){if(v.has(t))throw new p("Object already initialized");return e.facade=t,v.set(t,e),e},n=function(t){return v.get(t)||{}},s=function(t){return v.has(t)}}else{var y=f("state");g[y]=!0,i=function(t,e){if(h(t,y))throw new p("Object already initialized");return e.facade=t,c(t,y,e),e},n=function(t){return h(t,y)?t[y]:{}},s=function(t){return h(t,y)}}t.exports={set:i,get:n,has:s,enforce:function(t){return s(t)?n(t):i(t,{})},getterFor:function(t){return function(e){var r;if(!u(e)||(r=n(e)).type!==t)throw new p("Incompatible receiver, "+t+" required");return r}}}},849:function(t,e,r){"use strict";var i=r(980);t.exports=function(t){var e=+t;return e!=e||0===e?0:i(e)}},850:function(t,e,r){"use strict";var i=r(918);t.exports="NODE"===i},851:function(t,e,r){"use strict";var i=r(814).f,n=r(804),s=r(792)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!n(t,s)&&i(t,s,{configurable:!0,value:e})}},852:function(t,e,r){"use strict";var i=r(816),n=r(787);t.exports=function(t){if("Function"===i(t))return n(t)}},853:function(t,e,r){"use strict";var i,n=r(797),s=r(1011),a=r(877),o=r(875),u=r(923),c=r(846),h=r(874),l=h("IE_PROTO"),f=function(){},g=function(t){return"<script>"+t+"<\/script>"},p=function(t){t.write(g("")),t.close();var e=t.parentWindow.Object;return t=null,e},d=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}var t,e;d="undefined"!=typeof document?document.domain&&i?p(i):((e=c("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(g("document.F=Object")),t.close(),t.F):p(i);for(var r=a.length;r--;)delete d.prototype[a[r]];return d()};o[l]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(f.prototype=n(t),r=new f,f.prototype=null,r[l]=t):r=d(),void 0===e?r:s.f(r,e)}},868:function(t,e,r){"use strict";t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},869:function(t,e,r){"use strict";var i,n,s=r(784),a=r(828),o=s.process,u=s.Deno,c=o&&o.versions||u&&u.version,h=c&&c.v8;h&&(n=(i=h.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1])),!n&&a&&(!(i=a.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=a.match(/Chrome\/(\d+)/))&&(n=+i[1]),t.exports=n},870:function(t,e,r){"use strict";var i=r(871);t.exports=function(t,e){return i[t]||(i[t]=e||{})}},871:function(t,e,r){"use strict";var i=r(808),n=r(784),s=r(872),a=t.exports=n["__core-js_shared__"]||s("__core-js_shared__",{});(a.versions||(a.versions=[])).push({version:"3.39.0",mode:i?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.39.0/LICENSE",source:"https://github.com/zloirock/core-js"})},872:function(t,e,r){"use strict";var i=r(784),n=Object.defineProperty;t.exports=function(t,e){try{n(i,t,{value:e,configurable:!0,writable:!0})}catch(r){i[t]=e}return e}},873:function(t,e,r){"use strict";var i=r(787),n=r(788),s=r(871),a=i(Function.toString);n(s.inspectSource)||(s.inspectSource=function(t){return a(t)}),t.exports=s.inspectSource},874:function(t,e,r){"use strict";var i=r(870),n=r(911),s=i("keys");t.exports=function(t){return s[t]||(s[t]=n(t))}},875:function(t,e,r){"use strict";t.exports={}},876:function(t,e,r){"use strict";var i=r(824);t.exports=function(t){return i(t.length)}},877:function(t,e,r){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},878:function(t,e,r){"use strict";var i=r(990),n=r(788),s=r(816),a=r(792)("toStringTag"),o=Object,u="Arguments"===s(function(){return arguments}());t.exports=i?s:function(t){var e,r,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?r:u?s(e):"Object"===(i=s(e))&&n(e.callee)?"Arguments":i}},879:function(t,e,r){"use strict";var i=r(852),n=r(812),s=r(842),a=i(i.bind);t.exports=function(t,e){return n(t),void 0===e?t:s?a(t,e):function(){return t.apply(e,arguments)}}},880:function(t,e,r){"use strict";t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},881:function(t,e,r){"use strict";r(1010);var i=r(794),n=r(819),s=r(882),a=r(786),o=r(792),u=r(829),c=o("species"),h=RegExp.prototype;t.exports=function(t,e,r,l){var f=o(t),g=!a((function(){var e={};return e[f]=function(){return 7},7!==""[t](e)})),p=g&&!a((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[c]=function(){return r},r.flags="",r[f]=/./[f]),r.exec=function(){return e=!0,null},r[f](""),!e}));if(!g||!p||r){var d=/./[f],v=e(f,""[t],(function(t,e,r,n,a){var o=e.exec;return o===s||o===h.exec?g&&!a?{done:!0,value:i(d,e,r,n)}:{done:!0,value:i(t,r,e,n)}:{done:!1}}));n(String.prototype,t,v[0]),n(h,f,v[1])}l&&u(h[f],"sham",!0)}},882:function(t,e,r){"use strict";var i,n,s=r(794),a=r(787),o=r(809),u=r(929),c=r(930),h=r(870),l=r(853),f=r(848).get,g=r(1013),p=r(1014),d=h("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,y=v,m=a("".charAt),x=a("".indexOf),b=a("".replace),S=a("".slice),w=(n=/b*/g,s(v,i=/a/,"a"),s(v,n,"a"),0!==i.lastIndex||0!==n.lastIndex),P=c.BROKEN_CARET,A=void 0!==/()??/.exec("")[1];(w||A||P||g||p)&&(y=function(t){var e,r,i,n,a,c,h,g=this,p=f(g),C=o(t),E=p.raw;if(E)return E.lastIndex=g.lastIndex,e=s(y,E,C),g.lastIndex=E.lastIndex,e;var T=p.groups,O=P&&g.sticky,M=s(u,g),V=g.source,k=0,N=C;if(O&&(M=b(M,"y",""),-1===x(M,"g")&&(M+="g"),N=S(C,g.lastIndex),g.lastIndex>0&&(!g.multiline||g.multiline&&"\n"!==m(C,g.lastIndex-1))&&(V="(?: "+V+")",N=" "+N,k++),r=new RegExp("^(?:"+V+")",M)),A&&(r=new RegExp("^"+V+"$(?!\\s)",M)),w&&(i=g.lastIndex),n=s(v,O?r:g,N),O?n?(n.input=S(n.input,k),n[0]=S(n[0],k),n.index=g.lastIndex,g.lastIndex+=n[0].length):g.lastIndex=0:w&&n&&(g.lastIndex=g.global?n.index+n[0].length:i),A&&n&&n.length>1&&s(d,n[0],r,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(n[a]=void 0)})),n&&T)for(n.groups=c=l(null),a=0;a<T.length;a++)c[(h=T[a])[0]]=n[h[1]];return n}),t.exports=y},883:function(t,e,r){"use strict";var i=r(1015).charAt;t.exports=function(t,e,r){return e+(r?i(t,e).length:1)}},884:function(t,e,r){"use strict";var i=r(794),n=r(797),s=r(788),a=r(816),o=r(882),u=TypeError;t.exports=function(t,e){var r=t.exec;if(s(r)){var c=i(r,t,e);return null!==c&&n(c),c}if("RegExp"===a(t))return i(o,t,e);throw new u("RegExp#exec called on incompatible receiver")}},885:function(t,e,r){"use strict";var i=r(1019),n=TypeError;t.exports=function(t){if(i(t))throw new n("The method doesn't accept regular expressions");return t}},886:function(t,e,r){"use strict";var i=r(792)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[i]=!1,"/./"[t](e)}catch(t){}}return!1}},906:function(t,e,r){"use strict";var i=r(787),n=r(786),s=r(816),a=Object,o=i("".split);t.exports=n((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===s(t)?o(t,""):a(t)}:a},907:function(t,e,r){"use strict";var i=r(973),n=r(908);t.exports=function(t){var e=i(t,"string");return n(e)?e:e+""}},908:function(t,e,r){"use strict";var i=r(818),n=r(788),s=r(843),a=r(909),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=i("Symbol");return n(e)&&s(e.prototype,o(t))}},909:function(t,e,r){"use strict";var i=r(910);t.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},910:function(t,e,r){"use strict";var i=r(869),n=r(786),s=r(784).String;t.exports=!!Object.getOwnPropertySymbols&&!n((function(){var t=Symbol("symbol detection");return!s(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41}))},911:function(t,e,r){"use strict";var i=r(787),n=0,s=Math.random(),a=i(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++n+s,36)}},912:function(t,e,r){"use strict";var i=r(801),n=r(786),s=r(846);t.exports=!i&&!n((function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},913:function(t,e,r){"use strict";var i=r(801),n=r(786);t.exports=i&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},914:function(t,e,r){"use strict";var i=r(787),n=r(786),s=r(788),a=r(804),o=r(801),u=r(847).CONFIGURABLE,c=r(873),h=r(848),l=h.enforce,f=h.get,g=String,p=Object.defineProperty,d=i("".slice),v=i("".replace),y=i([].join),m=o&&!n((function(){return 8!==p((function(){}),"length",{value:8}).length})),x=String(String).split("String"),b=t.exports=function(t,e,r){"Symbol("===d(g(e),0,7)&&(e="["+v(g(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||u&&t.name!==e)&&(o?p(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&p(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?o&&p(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var i=l(t);return a(i,"source")||(i.source=y(x,"string"==typeof e?e:"")),t};Function.prototype.toString=b((function(){return s(this)&&f(this).source||c(this)}),"toString")},915:function(t,e,r){"use strict";var i=r(787),n=r(804),s=r(827),a=r(916).indexOf,o=r(875),u=i([].push);t.exports=function(t,e){var r,i=s(t),c=0,h=[];for(r in i)!n(o,r)&&n(i,r)&&u(h,r);for(;e.length>c;)n(i,r=e[c++])&&(~a(h,r)||u(h,r));return h}},916:function(t,e,r){"use strict";var i=r(827),n=r(979),s=r(876),a=function(t){return function(e,r,a){var o=i(e),u=s(o);if(0===u)return!t&&-1;var c,h=n(a,u);if(t&&r!=r){for(;u>h;)if((c=o[h++])!=c)return!0}else for(;u>h;h++)if((t||h in o)&&o[h]===r)return t||h||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},917:function(t,e,r){"use strict";var i=r(786),n=r(788),s=/#|\.prototype\./,a=function(t,e){var r=u[o(t)];return r===h||r!==c&&(n(e)?i(e):!!e)},o=a.normalize=function(t){return String(t).replace(s,".").toLowerCase()},u=a.data={},c=a.NATIVE="N",h=a.POLYFILL="P";t.exports=a},918:function(t,e,r){"use strict";var i=r(784),n=r(828),s=r(816),a=function(t){return n.slice(0,t.length)===t};t.exports=a("Bun/")?"BUN":a("Cloudflare-Workers")?"CLOUDFLARE":a("Deno/")?"DENO":a("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"},919:function(t,e,r){"use strict";var i=r(982),n=r(803),s=r(802),a=r(983);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=i(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,i){return s(r),a(i),n(r)?(e?t(r,i):r.__proto__=i,r):r}}():void 0)},920:function(t,e,r){"use strict";var i=r(797),n=r(988),s=r(817),a=r(792)("species");t.exports=function(t,e){var r,o=i(t).constructor;return void 0===o||s(r=i(o)[a])?e:n(r)}},921:function(t,e,r){"use strict";var i,n,s,a,o=r(784),u=r(922),c=r(879),h=r(788),l=r(804),f=r(786),g=r(923),p=r(991),d=r(846),v=r(992),y=r(924),m=r(850),x=o.setImmediate,b=o.clearImmediate,S=o.process,w=o.Dispatch,P=o.Function,A=o.MessageChannel,C=o.String,E=0,T={};f((function(){i=o.location}));var O=function(t){if(l(T,t)){var e=T[t];delete T[t],e()}},M=function(t){return function(){O(t)}},V=function(t){O(t.data)},k=function(t){o.postMessage(C(t),i.protocol+"//"+i.host)};x&&b||(x=function(t){v(arguments.length,1);var e=h(t)?t:P(t),r=p(arguments,1);return T[++E]=function(){u(e,void 0,r)},n(E),E},b=function(t){delete T[t]},m?n=function(t){S.nextTick(M(t))}:w&&w.now?n=function(t){w.now(M(t))}:A&&!y?(a=(s=new A).port2,s.port1.onmessage=V,n=c(a.postMessage,a)):o.addEventListener&&h(o.postMessage)&&!o.importScripts&&i&&"file:"!==i.protocol&&!f(k)?(n=k,o.addEventListener("message",V,!1)):n="onreadystatechange"in d("script")?function(t){g.appendChild(d("script")).onreadystatechange=function(){g.removeChild(this),O(t)}}:function(t){setTimeout(M(t),0)}),t.exports={set:x,clear:b}},922:function(t,e,r){"use strict";var i=r(842),n=Function.prototype,s=n.apply,a=n.call;t.exports="object"==typeof Reflect&&Reflect.apply||(i?a.bind(s):function(){return a.apply(s,arguments)})},923:function(t,e,r){"use strict";var i=r(818);t.exports=i("document","documentElement")},924:function(t,e,r){"use strict";var i=r(828);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},925:function(t,e,r){"use strict";var i=function(){this.head=null,this.tail=null};i.prototype={add:function(t){var e={item:t,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var t=this.head;if(t)return null===(this.head=t.next)&&(this.tail=null),t.item}},t.exports=i},926:function(t,e,r){"use strict";var i=r(879),n=r(794),s=r(797),a=r(844),o=r(999),u=r(876),c=r(843),h=r(1e3),l=r(927),f=r(1001),g=TypeError,p=function(t,e){this.stopped=t,this.result=e},d=p.prototype;t.exports=function(t,e,r){var v,y,m,x,b,S,w,P=r&&r.that,A=!(!r||!r.AS_ENTRIES),C=!(!r||!r.IS_RECORD),E=!(!r||!r.IS_ITERATOR),T=!(!r||!r.INTERRUPTED),O=i(e,P),M=function(t){return v&&f(v,"normal",t),new p(!0,t)},V=function(t){return A?(s(t),T?O(t[0],t[1],M):O(t[0],t[1])):T?O(t,M):O(t)};if(C)v=t.iterator;else if(E)v=t;else{if(!(y=l(t)))throw new g(a(t)+" is not iterable");if(o(y)){for(m=0,x=u(t);x>m;m++)if((b=V(t[m]))&&c(d,b))return b;return new p(!1)}v=h(t,y)}for(S=C?t.next:v.next;!(w=n(S,v)).done;){try{b=V(w.value)}catch(t){f(v,"throw",t)}if("object"==typeof b&&b&&c(d,b))return b}return new p(!1)}},927:function(t,e,r){"use strict";var i=r(878),n=r(823),s=r(817),a=r(833),o=r(792)("iterator");t.exports=function(t){if(!s(t))return n(t,o)||n(t,"@@iterator")||a[i(t)]}},928:function(t,e,r){"use strict";var i=r(830),n=r(1002),s=r(831).CONSTRUCTOR;t.exports=s||!n((function(t){i.all(t).then(void 0,(function(){}))}))},929:function(t,e,r){"use strict";var i=r(797);t.exports=function(){var t=i(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},930:function(t,e,r){"use strict";var i=r(786),n=r(784).RegExp,s=i((function(){var t=n("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),a=s||i((function(){return!n("a","y").sticky})),o=s||i((function(){var t=n("^r","gy");return t.lastIndex=2,null!==t.exec("str")}));t.exports={BROKEN_CARET:o,MISSED_STICKY:a,UNSUPPORTED_Y:s}},931:function(t,e,r){"use strict";var i=r(827),n=r(1020),s=r(833),a=r(848),o=r(814).f,u=r(1021),c=r(1024),h=r(808),l=r(801),f=a.set,g=a.getterFor("Array Iterator");t.exports=u(Array,"Array",(function(t,e){f(this,{type:"Array Iterator",target:i(t),index:0,kind:e})}),(function(){var t=g(this),e=t.target,r=t.index++;if(!e||r>=e.length)return t.target=null,c(void 0,!0);switch(t.kind){case"keys":return c(r,!1);case"values":return c(e[r],!1)}return c([r,e[r]],!1)}),"values");var p=s.Arguments=s.Array;if(n("keys"),n("values"),n("entries"),!h&&l&&"values"!==p.name)try{o(p,"name",{value:"values"})}catch(t){}},932:function(t,e,r){"use strict";var i,n,s,a=r(786),o=r(788),u=r(803),c=r(853),h=r(933),l=r(819),f=r(792),g=r(808),p=f("iterator"),d=!1;[].keys&&("next"in(s=[].keys())?(n=h(h(s)))!==Object.prototype&&(i=n):d=!0),!u(i)||a((function(){var t={};return i[p].call(t)!==t}))?i={}:g&&(i=c(i)),o(i[p])||l(i,p,(function(){return this})),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:d}},933:function(t,e,r){"use strict";var i=r(804),n=r(788),s=r(845),a=r(874),o=r(1023),u=a("IE_PROTO"),c=Object,h=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=s(t);if(i(e,u))return e[u];var r=e.constructor;return n(r)&&e instanceof r?r.prototype:e instanceof c?h:null}},934:function(t,e,r){"use strict";var i=r(786);t.exports=function(t,e){var r=[][t];return!!r&&i((function(){r.call(null,e||function(){return 1},1)}))}},936:function(t,e,r){"use strict";t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},970:function(t,e,r){"use strict";r(971),r(998),r(1003),r(1004),r(1005),r(1006)},971:function(t,e,r){"use strict";var i,n,s,a=r(796),o=r(808),u=r(850),c=r(784),h=r(794),l=r(819),f=r(919),g=r(851),p=r(985),d=r(812),v=r(788),y=r(803),m=r(987),x=r(920),b=r(921).set,S=r(993),w=r(997),P=r(880),A=r(925),C=r(848),E=r(830),T=r(831),O=r(832),M=T.CONSTRUCTOR,V=T.REJECTION_EVENT,k=T.SUBCLASSING,N=C.getterFor("Promise"),R=C.set,I=E&&E.prototype,L=E,D=I,j=c.TypeError,B=c.document,z=c.process,_=O.f,F=_,U=!!(B&&B.createEvent&&c.dispatchEvent),X=function(t){var e;return!(!y(t)||!v(e=t.then))&&e},W=function(t,e){var r,i,n,s=e.value,a=1===e.state,o=a?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{o?(a||(2===e.rejection&&q(e),e.rejection=1),!0===o?r=s:(l&&l.enter(),r=o(s),l&&(l.exit(),n=!0)),r===t.promise?c(new j("Promise-chain cycle")):(i=X(r))?h(i,r,u,c):u(r)):c(s)}catch(t){l&&!n&&l.exit(),c(t)}},Y=function(t,e){t.notified||(t.notified=!0,S((function(){for(var r,i=t.reactions;r=i.get();)W(r,t);t.notified=!1,e&&!t.rejection&&H(t)})))},G=function(t,e,r){var i,n;U?((i=B.createEvent("Event")).promise=e,i.reason=r,i.initEvent(t,!1,!0),c.dispatchEvent(i)):i={promise:e,reason:r},!V&&(n=c["on"+t])?n(i):"unhandledrejection"===t&&w("Unhandled promise rejection",r)},H=function(t){h(b,c,(function(){var e,r=t.facade,i=t.value;if($(t)&&(e=P((function(){u?z.emit("unhandledRejection",i,r):G("unhandledrejection",r,i)})),t.rejection=u||$(t)?2:1,e.error))throw e.value}))},$=function(t){return 1!==t.rejection&&!t.parent},q=function(t){h(b,c,(function(){var e=t.facade;u?z.emit("rejectionHandled",e):G("rejectionhandled",e,t.value)}))},Q=function(t,e,r){return function(i){t(e,i,r)}},Z=function(t,e,r){t.done||(t.done=!0,r&&(t=r),t.value=e,t.state=2,Y(t,!0))},J=function(t,e,r){if(!t.done){t.done=!0,r&&(t=r);try{if(t.facade===e)throw new j("Promise can't be resolved itself");var i=X(e);i?S((function(){var r={done:!1};try{h(i,e,Q(J,r,t),Q(Z,r,t))}catch(e){Z(r,e,t)}})):(t.value=e,t.state=1,Y(t,!1))}catch(e){Z({done:!1},e,t)}}};if(M&&(D=(L=function(t){m(this,D),d(t),h(i,this);var e=N(this);try{t(Q(J,e),Q(Z,e))}catch(t){Z(e,t)}}).prototype,(i=function(t){R(this,{type:"Promise",done:!1,notified:!1,parent:!1,reactions:new A,rejection:!1,state:0,value:null})}).prototype=l(D,"then",(function(t,e){var r=N(this),i=_(x(this,L));return r.parent=!0,i.ok=!v(t)||t,i.fail=v(e)&&e,i.domain=u?z.domain:void 0,0===r.state?r.reactions.add(i):S((function(){W(i,r)})),i.promise})),n=function(){var t=new i,e=N(t);this.promise=t,this.resolve=Q(J,e),this.reject=Q(Z,e)},O.f=_=function(t){return t===L||void 0===t?new n(t):F(t)},!o&&v(E)&&I!==Object.prototype)){s=I.then,k||l(I,"then",(function(t,e){var r=this;return new L((function(t,e){h(s,r,t,e)})).then(t,e)}),{unsafe:!0});try{delete I.constructor}catch(t){}f&&f(I,D)}a({global:!0,constructor:!0,wrap:!0,forced:M},{Promise:L}),g(L,"Promise",!1,!0),p("Promise")},972:function(t,e,r){"use strict";var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!i.call({1:2},1);e.f=s?function(t){var e=n(this,t);return!!e&&e.enumerable}:i},973:function(t,e,r){"use strict";var i=r(794),n=r(803),s=r(908),a=r(823),o=r(974),u=r(792),c=TypeError,h=u("toPrimitive");t.exports=function(t,e){if(!n(t)||s(t))return t;var r,u=a(t,h);if(u){if(void 0===e&&(e="default"),r=i(u,t,e),!n(r)||s(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},974:function(t,e,r){"use strict";var i=r(794),n=r(788),s=r(803),a=TypeError;t.exports=function(t,e){var r,o;if("string"===e&&n(r=t.toString)&&!s(o=i(r,t)))return o;if(n(r=t.valueOf)&&!s(o=i(r,t)))return o;if("string"!==e&&n(r=t.toString)&&!s(o=i(r,t)))return o;throw new a("Can't convert object to primitive value")}},975:function(t,e,r){"use strict";var i=r(784),n=r(788),s=i.WeakMap;t.exports=n(s)&&/native code/.test(String(s))},976:function(t,e,r){"use strict";var i=r(804),n=r(977),s=r(841),a=r(814);t.exports=function(t,e,r){for(var o=n(e),u=a.f,c=s.f,h=0;h<o.length;h++){var l=o[h];i(t,l)||r&&i(r,l)||u(t,l,c(e,l))}}},977:function(t,e,r){"use strict";var i=r(818),n=r(787),s=r(978),a=r(981),o=r(797),u=n([].concat);t.exports=i("Reflect","ownKeys")||function(t){var e=s.f(o(t)),r=a.f;return r?u(e,r(t)):e}},978:function(t,e,r){"use strict";var i=r(915),n=r(877).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,n)}},979:function(t,e,r){"use strict";var i=r(849),n=Math.max,s=Math.min;t.exports=function(t,e){var r=i(t);return r<0?n(r+e,0):s(r,e)}},980:function(t,e,r){"use strict";var i=Math.ceil,n=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?n:i)(e)}},981:function(t,e,r){"use strict";e.f=Object.getOwnPropertySymbols},982:function(t,e,r){"use strict";var i=r(787),n=r(812);t.exports=function(t,e,r){try{return i(n(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},983:function(t,e,r){"use strict";var i=r(984),n=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw new s("Can't set "+n(t)+" as a prototype")}},984:function(t,e,r){"use strict";var i=r(803);t.exports=function(t){return i(t)||null===t}},985:function(t,e,r){"use strict";var i=r(818),n=r(986),s=r(792),a=r(801),o=s("species");t.exports=function(t){var e=i(t);a&&e&&!e[o]&&n(e,o,{configurable:!0,get:function(){return this}})}},986:function(t,e,r){"use strict";var i=r(914),n=r(814);t.exports=function(t,e,r){return r.get&&i(r.get,e,{getter:!0}),r.set&&i(r.set,e,{setter:!0}),n.f(t,e,r)}},987:function(t,e,r){"use strict";var i=r(843),n=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw new n("Incorrect invocation")}},988:function(t,e,r){"use strict";var i=r(989),n=r(844),s=TypeError;t.exports=function(t){if(i(t))return t;throw new s(n(t)+" is not a constructor")}},989:function(t,e,r){"use strict";var i=r(787),n=r(786),s=r(788),a=r(878),o=r(818),u=r(873),c=function(){},h=o("Reflect","construct"),l=/^\s*(?:class|function)\b/,f=i(l.exec),g=!l.test(c),p=function(t){if(!s(t))return!1;try{return h(c,[],t),!0}catch(t){return!1}},d=function(t){if(!s(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return g||!!f(l,u(t))}catch(t){return!0}};d.sham=!0,t.exports=!h||n((function(){var t;return p(p.call)||!p(Object)||!p((function(){t=!0}))||t}))?d:p},990:function(t,e,r){"use strict";var i={};i[r(792)("toStringTag")]="z",t.exports="[object z]"===String(i)},991:function(t,e,r){"use strict";var i=r(787);t.exports=i([].slice)},992:function(t,e,r){"use strict";var i=TypeError;t.exports=function(t,e){if(t<e)throw new i("Not enough arguments");return t}},993:function(t,e,r){"use strict";var i,n,s,a,o,u=r(784),c=r(994),h=r(879),l=r(921).set,f=r(925),g=r(924),p=r(995),d=r(996),v=r(850),y=u.MutationObserver||u.WebKitMutationObserver,m=u.document,x=u.process,b=u.Promise,S=c("queueMicrotask");if(!S){var w=new f,P=function(){var t,e;for(v&&(t=x.domain)&&t.exit();e=w.get();)try{e()}catch(t){throw w.head&&i(),t}t&&t.enter()};g||v||d||!y||!m?!p&&b&&b.resolve?((a=b.resolve(void 0)).constructor=b,o=h(a.then,a),i=function(){o(P)}):v?i=function(){x.nextTick(P)}:(l=h(l,u),i=function(){l(P)}):(n=!0,s=m.createTextNode(""),new y(P).observe(s,{characterData:!0}),i=function(){s.data=n=!n}),S=function(t){w.head||i(),w.add(t)}}t.exports=S},994:function(t,e,r){"use strict";var i=r(784),n=r(801),s=Object.getOwnPropertyDescriptor;t.exports=function(t){if(!n)return i[t];var e=s(i,t);return e&&e.value}},995:function(t,e,r){"use strict";var i=r(828);t.exports=/ipad|iphone|ipod/i.test(i)&&"undefined"!=typeof Pebble},996:function(t,e,r){"use strict";var i=r(828);t.exports=/web0s(?!.*chrome)/i.test(i)},997:function(t,e,r){"use strict";t.exports=function(t,e){try{1===arguments.length?console.error(t):console.error(t,e)}catch(t){}}},998:function(t,e,r){"use strict";var i=r(796),n=r(794),s=r(812),a=r(832),o=r(880),u=r(926);i({target:"Promise",stat:!0,forced:r(928)},{all:function(t){var e=this,r=a.f(e),i=r.resolve,c=r.reject,h=o((function(){var r=s(e.resolve),a=[],o=0,h=1;u(t,(function(t){var s=o++,u=!1;h++,n(r,e,t).then((function(t){u||(u=!0,a[s]=t,--h||i(a))}),c)})),--h||i(a)}));return h.error&&c(h.value),r.promise}})},999:function(t,e,r){"use strict";var i=r(792),n=r(833),s=i("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(n.Array===t||a[s]===t)}}}]);