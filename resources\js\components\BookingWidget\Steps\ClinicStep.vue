<template>
  <div class="kivi-booking-step" id="step-clinic">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader"></div>
    </div>

    <template v-else>
      <!-- Search input for filtering clinics -->
      <div v-if="clinics.length > 4" class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="clinic-search"
          placeholder="Search clinics..."
          v-model="searchTerm"
        >
      </div>

      <!-- Clinic cards grid view -->
      <div class="kivi-grid" id="clinic-list">
        <div
          v-for="clinic in filteredClinics"
          :key="clinic.id"
          class="kivi-card clinic-card"
          :class="{ 'selected': selectedClinicId === clinic.id }"
          @click="selectClinic(clinic)"
        >
          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ clinic.name }}</h3>
              <div v-if="clinic.address" class="kivi-card-subtitle">{{ clinic.address }}</div>
            </div>
          </div>
          <div v-if="clinic.description" class="kivi-card-body">
            {{ clinic.description }}
          </div>
          <div v-if="clinic.specializations && clinic.specializations.length > 0" class="kivi-card-footer">
            <span v-for="(specialty, index) in clinic.specializations" :key="index" class="kivi-badge kivi-badge-purple">
              {{ specialty }}
            </span>
          </div>
        </div>
      </div>

      <div v-if="filteredClinics.length === 0" class="kivi-empty-state">
        <p>No clinics found. Please try again later.</p>
      </div>

      <!-- Fallback for HTML rendering if needed -->
      <div v-if="!clinics.length && clinicsHtml" ref="clinicsHtmlContainer" v-html="clinicsHtml" class="clinic-html-container"></div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'ClinicStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      clinics: [],
      clinicsHtml: '',
      searchTerm: '',
      selectedClinicId: null,
      isLoading: false,
      fetchInProgress: false, // Flag to prevent multiple simultaneous API calls
      clinicsCache: null, // Cache for clinic data
      clinicSlugFromUrl: null // Store the clinic slug from URL
    };
  },
  computed: {
    filteredClinics() {
      if (!this.searchTerm) {
        return this.clinics;
      }

      const term = this.searchTerm.toLowerCase();
      return this.clinics.filter(clinic =>
        clinic.name.toLowerCase().includes(term) ||
        (clinic.address && clinic.address.toLowerCase().includes(term))
      );
    }
  },
  created() {
    this.fetchClinicsWithCache();

    // If we already have a selected clinic in the booking data, set it
    if (this.bookingData.clinic) {
      this.selectedClinicId = this.bookingData.clinic.id;
    }

    // Check if we have a clinic name in the URL
    this.checkForClinicInUrl();
  },
  mounted() {
    // Add event listener to handle clinic selection after the HTML content is rendered
    this.$nextTick(() => {
      this.attachClinicSelectionEventListeners();
    });
  },
  methods: {
    // New method that uses caching to prevent redundant API calls
    async fetchClinicsWithCache() {
      // If a fetch is already in progress, don't start another one
      if (this.fetchInProgress) {
        console.log('Fetch already in progress, skipping redundant call');
        return;
      }

      this.fetchInProgress = true;
      this.isLoading = true;

      try {
        // Check if we have cached data
        if (this.clinicsCache) {
          console.log('Using cached clinic data');
          this.clinics = [...this.clinicsCache];

          // If we only have one clinic, preselect it
          if (this.clinics.length === 1) {
            this.selectClinic(this.clinics[0]);
          }
        } else {
          // No cached data, fetch from API
          await this.fetchClinics();
        }
      } catch (error) {
        console.error('Error in fetchClinicsWithCache:', error);
      } finally {
        this.isLoading = false;
        this.fetchInProgress = false;
      }
    },

    async fetchClinics() {
      try {
        // Use the new JSON endpoint for our Vue widget
        const response = await apiCall.get('get_clinic_details_json', {
          params: {
            doctor_id: 0,
            searchKey: '',
            preselected_clinic: 0
          }
        });

        if (response.data.status) {
          // Log raw data for debugging
          console.log("Raw clinics data response:", response.data);

          // Process the JSON data from our new endpoint
          this.clinics = response.data.data.map(clinic => ({
            id: clinic.id,
            name: clinic.name,
            address: clinic.address || '',
            description: clinic.description || null,
            specializations: clinic.specialties || []
          }));

          // Cache the processed clinics
          this.clinicsCache = [...this.clinics];

          console.log("Processed clinics:", this.clinics);

          // If we only have one clinic, preselect it
          if (this.clinics.length === 1) {
            this.selectClinic(this.clinics[0]);
          }
          // If we have a clinic slug from the URL, try to match it
          else if (this.clinicSlugFromUrl) {
            this.matchClinicFromSlug(this.clinicSlugFromUrl);
          }

          // If there are multiple clinics, render them using our Vue template
          // instead of injected HTML
        }
      } catch (error) {
        console.error('Error fetching clinics:', error);
      }
    },

    attachClinicSelectionEventListeners() {
      if (!this.$refs.clinicsHtmlContainer) {
        console.warn('Clinic HTML container ref not found');
        return;
      }

      // Find all clinic radio inputs in the rendered HTML
      const clinicInputs = this.$refs.clinicsHtmlContainer.querySelectorAll('input[name="selected_clinic"]');

      console.log('Found clinic inputs:', clinicInputs.length);

      // Attach click event listener to each radio input
      clinicInputs.forEach(input => {
        input.addEventListener('change', (event) => {
          if (event.target.checked) {
            const clinicId = event.target.value;
            const clinicRow = event.target.closest('.kivicare-clinic-selection');

            if (clinicRow) {
              // Try to find clinic name in various possible elements
              const nameElement = clinicRow.querySelector('.kivicare-clinic-name') ||
                                   clinicRow.querySelector('.clinic-name') ||
                                   clinicRow.querySelector('h3');

              const addressElement = clinicRow.querySelector('.kivicare-clinic-address') ||
                                      clinicRow.querySelector('.clinic-address') ||
                                      clinicRow.querySelector('p');

              const clinicName = nameElement ? nameElement.textContent.trim() : 'Selected Clinic';
              const clinicAddress = addressElement ? addressElement.textContent.trim() : '';

              console.log('Clinic selected:', { id: clinicId, name: clinicName, address: clinicAddress });

              // Call the selection method with the extracted clinic info
              this.handleClinicSelection({
                id: clinicId,
                name: clinicName,
                address: clinicAddress
              });
            }
          }
        });
      });

      // If there's only one clinic, preselect it
      if (clinicInputs.length === 1) {
        clinicInputs[0].checked = true;
        clinicInputs[0].dispatchEvent(new Event('change'));
      }
    },

    handleClinicSelection(clinic) {
      this.selectClinic(clinic);

      // Removed auto-advance to next step - user must click Next button
    },

    selectClinic(clinic) {
      this.selectedClinicId = clinic.id;

      // Update parent component with new selection
      this.$emit('update:booking-data', {
        ...this.bookingData,
        clinic: {
          id: clinic.id,
          name: clinic.name,
          address: clinic.address || ''
        },
        // Clear subsequent selections when changing clinic
        category: null,
        services: []
      });

      // Removed auto-advance to next step - user must click Next button
    },

    checkForClinicInUrl() {
      // Get the full URL (including hash)
      const fullUrl = window.location.href;

      // Extract the path part (before any hash or query string)
      const urlWithoutHash = fullUrl.split('#')[0].split('?')[0];

      // Extract path segments
      const pathSegments = urlWithoutHash.replace(/https?:\/\/[^\/]+/, '').split('/').filter(segment => segment);

      let clinicSlug = null;

      // Check the last segment first
      if (pathSegments.length > 0) {
        const lastSegment = pathSegments[pathSegments.length - 1];

        // If the last segment is not a known endpoint, it might be a clinic name
        if (lastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(lastSegment)) {
          clinicSlug = lastSegment;
        }
        // If we didn't find a clinic name in the last segment and there are at least 2 segments,
        // check the second-to-last segment
        else if (pathSegments.length > 1) {
          const secondToLastSegment = pathSegments[pathSegments.length - 2];
          if (secondToLastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(secondToLastSegment)) {
            clinicSlug = secondToLastSegment;
          }
        }
      }

      // If we found a potential clinic slug, store it for later use
      if (clinicSlug) {
        this.clinicSlugFromUrl = clinicSlug;

        // If clinics are already loaded, try to match the slug
        if (this.clinics.length > 0) {
          this.matchClinicFromSlug(clinicSlug);
        }
      }
    },

    matchClinicFromSlug(slug) {
      // Convert slug to a normalized form for matching
      const normalizedSlug = slug.toLowerCase().replace(/-/g, ' ');



      // Try different matching strategies
      let matchedClinic = null;

      // 1. Try exact match with slug
      matchedClinic = this.clinics.find(clinic =>
        clinic.name.toLowerCase().replace(/\s+/g, '-') === slug.toLowerCase()
      );



      // 2. Try exact match with name
      if (!matchedClinic) {
        matchedClinic = this.clinics.find(clinic =>
          clinic.name.toLowerCase() === normalizedSlug
        );


      }

      // 3. Try partial match
      if (!matchedClinic) {
        matchedClinic = this.clinics.find(clinic =>
          clinic.name.toLowerCase().includes(normalizedSlug) ||
          normalizedSlug.includes(clinic.name.toLowerCase())
        );


      }

      // 4. Try matching individual words
      if (!matchedClinic) {
        const words = normalizedSlug.split(' ').filter(word => word.length > 3);
        if (words.length > 0) {
          matchedClinic = this.clinics.find(clinic => {
            const clinicNameLower = clinic.name.toLowerCase();
            return words.some(word => clinicNameLower.includes(word));
          });


        }
      }

      // 5. Additional fallback: try to match with shorter words
      if (!matchedClinic) {
        const shortWords = normalizedSlug.split(' ').filter(word => word.length > 1);
        if (shortWords.length > 0) {
          matchedClinic = this.clinics.find(clinic => {
            const clinicNameLower = clinic.name.toLowerCase();
            return shortWords.some(word => clinicNameLower.includes(word));
          });


        }
      }

      // 6. Try matching by first word of the slug
      if (!matchedClinic && normalizedSlug.includes(' ')) {
        const firstWord = normalizedSlug.split(' ')[0];
        if (firstWord.length > 2) {
          matchedClinic = this.clinics.find(clinic =>
            clinic.name.toLowerCase().includes(firstWord)
          );
        }
      }

      // 7. Last resort: try to match any part of the slug with any part of the clinic name
      if (!matchedClinic) {
        // Split the slug into parts and try to match any part
        const slugParts = slug.toLowerCase().split('-');

        for (const part of slugParts) {
          if (part.length > 2) {
            const possibleMatch = this.clinics.find(clinic =>
              clinic.name.toLowerCase().includes(part)
            );

            if (possibleMatch) {
              matchedClinic = possibleMatch;
              break;
            }
          }
        }
      }

      // 8. Check if we have a specific clinic ID in the URL
      if (!matchedClinic) {
        const urlParams = new URLSearchParams(window.location.search);
        const clinicIdParam = urlParams.get('clinic_id');

        if (clinicIdParam) {
          // Try to find the clinic by ID
          matchedClinic = this.clinics.find(clinic => clinic.id === clinicIdParam);


        }
      }

      // 9. Try a fuzzy match approach - find the clinic with the most matching characters
      if (!matchedClinic && this.clinics.length > 0) {
        // Calculate a similarity score for each clinic
        const clinicsWithScores = this.clinics.map(clinic => {
          const clinicNameLower = clinic.name.toLowerCase();
          let score = 0;

          // Check how many characters from the slug appear in the clinic name
          for (const char of normalizedSlug) {
            if (clinicNameLower.includes(char)) {
              score++;
            }
          }

          // Check how many characters from the clinic name appear in the slug
          for (const char of clinicNameLower) {
            if (normalizedSlug.includes(char)) {
              score++;
            }
          }

          return { clinic, score };
        });

        // Sort by score (highest first)
        clinicsWithScores.sort((a, b) => b.score - a.score);

        // Use the clinic with the highest score
        if (clinicsWithScores.length > 0 && clinicsWithScores[0].score > 0) {
          matchedClinic = clinicsWithScores[0].clinic;

        }
      }

      // If we found a match, select it and go to the next step
      if (matchedClinic) {

        // Force a delay to ensure the clinic selection is processed
        setTimeout(() => {
          // Select the clinic
          this.selectClinic(matchedClinic);

          // Force update the parent component with the selected clinic
          this.$emit('update:booking-data', {
            ...this.bookingData,
            clinic: {
              id: matchedClinic.id,
              name: matchedClinic.name,
              address: matchedClinic.address || ''
            },
            // Clear subsequent selections when changing clinic
            category: null,
            services: []
          });

          // Emit an event to skip to the next step
          this.$nextTick(() => {
            this.$emit('next-step');

            // Dispatch a global event that the clinic was selected from URL
            this.$root.$emit('clinic-selected-from-url', {
              clinicId: matchedClinic.id,
              clinicName: matchedClinic.name
            });
          });
        }, 200); // Increased delay to ensure everything is processed
      }
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kivi-card:hover {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-body {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgba(29, 78, 216, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
}

.kivi-loader {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2rem;
  height: 2rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-empty-state {
  text-align: center;
  padding: 2rem 0;
  color: var(--gray);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }
}

/* Styles for the injected HTML content */
.clinic-html-container {
  padding: 0.5rem;
}

.clinic-html-container :deep(.kivicare-clinic-selection) {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clinic-html-container :deep(.kivicare-clinic-selection:hover) {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
}

/* Style radio buttons */
.clinic-html-container :deep(input[type="radio"]) {
  accent-color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Style clinic details */
.clinic-html-container :deep(.kivicare-clinic-name),
.clinic-html-container :deep(.clinic-name),
.clinic-html-container :deep(h3) {
  font-weight: 600;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  color: var(--black);
}

.clinic-html-container :deep(.kivicare-clinic-address),
.clinic-html-container :deep(.clinic-address),
.clinic-html-container :deep(p) {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 0.5rem;
}

/* Ensure that any section with active/selected state is properly highlighted */
.clinic-html-container :deep(.selected),
.clinic-html-container :deep(.active) {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
}
</style>