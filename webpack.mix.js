const mix = require('laravel-mix');

// Skip tailwindcss processing to avoid compatibility issues
// Don't process Sass files in production to avoid errors
if (process.env.NODE_ENV === 'production') {
    // In production, only process JS files
    mix.js('resources/js/app.js', 'assets/js/app.min.js');
    mix.js('resources/js/front-app.js', 'assets/js/front-app.min.js');
} else {
    // In development, process both JS and SCSS but skip tailwind
    mix.js('resources/js/app.js', 'assets/js/app.min.js')
        .sass('resources/sass/app.scss', 'assets/css/app.min.css');
    
    mix.js('resources/js/front-app.js', 'assets/js/front-app.min.js')
        .sass('resources/sass/front-app.scss', 'assets/css/front-app.min.css');
    
    // Add PostCSS processing without tailwindcss
    mix.options({
        postCss: [
            require('autoprefixer')
        ]
    });
}

// jQuery autoload for both applications
mix.autoload({
    jquery: ['$', 'window.jQuery', 'jQuery', 'jquery']
});

// Add custom webpack configuration
mix.webpackConfig({
    resolve: {
        alias: {
            '@': __dirname + '/resources/js'
        }
    },
    // Increase memory limit for Node
    performance: {
        hints: false
    },
    // Disable source maps in production
    devtool: process.env.NODE_ENV === 'production' ? false : 'source-map'
});