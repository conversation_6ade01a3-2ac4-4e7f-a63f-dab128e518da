<template>
  <div class="max-w-4xl mx-auto bg-white rounded-lg p-6">
    <form id="serviceForm" @submit.prevent :novalidate="true" enctype="multipart/form-data">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Service Category -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.service.service_category }}
            <span class="text-red-500">*</span>
          </label>
          <vue-multiselect v-model="service.type" :options="types" :searchable="true" :close-on-select="true"
            :show-labels="false" placeholder="Select service category" label="label" track-by="id"
            @tag="addNewServiceCategory" :taggable="true" :loading="categoryMultiselectLoader"
            :disabled="!isServiceEdit" class="multiselect-primary" />
          <p class="text-sm text-purple-600">
            {{ formTranslation.service.note_category }}
          </p>
          <p v-if="submitted && !$v.service.type.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.service.service_category_required }}
          </p>
        </div>

        <!-- Service Name -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.service.service_name }}
            <span class="text-red-500">*</span>
          </label>
          <input id="name" v-model="service.name" :class="[
            'w-full rounded-md border border-gray-300 px-3 py-2 bg-white',
            { 'border-red-300': submitted && $v.service.name.$error },
          ]" :placeholder="formTranslation.service.service_name_plh" required type="text" />
          <p v-if="submitted && !$v.service.name.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.service.service_name_required }}
          </p>
        </div>

        <!-- Service Charges -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.service.charges }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm">{{ currencyPrefix }}</span>
            </div>
            <input id="price" v-model="service.price" type="number" min="0" max="100000000000" step="0.01"
              :placeholder="formTranslation.service.charges_plh" :class="[
                'w-full rounded-md border border-gray-300 pl-7 pr-12 py-2 bg-white',
                { 'border-red-300': submitted && $v.service.price.$error },
              ]" oninput="validity.valid||(value='')" />
            <div v-if="currencyPostfix" class="absolute inset-y-0 right-0 flex items-center pr-3">
              <span class="text-gray-500 sm:text-sm">{{
                currencyPostfix
              }}</span>
            </div>
          </div>
          <p v-if="submitted && !$v.service.price.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.service.service_charges_required }}
          </p>
        </div>

        <!-- Telemedicine Service -->
        <div v-if="userData.addOns.telemed || userData.addOns.googlemeet" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.service.is_telemed_service }}
            <span class="text-red-500">*</span>
          </label>
          <vue-multiselect v-model="service.telemed_service" :options="['yes', 'no']" :searchable="false"
            :close-on-select="true" :show-labels="false" :disabled="getUserRole() == 'doctor' &&
              !userData.telemedConfigOn &&
              userData.is_enable_doctor_zoom_telemed == 'off'
              " @input="telemedEnableChange" placeholder="Select telemedicine option" class="multiselect-primary" />
          <p v-if="submitted && !$v.service.telemed_service.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.service.telemed_service_required }}
          </p>
        </div>

        <!-- Clinic Selection -->
        <div v-if="
          userData.addOns.kiviPro &&
          (getUserRole() == 'administrator' || getUserRole() == 'doctor')
        " class="space-y-2">
          <div class="flex justify-between items-center">
            <label class="block text-sm font-medium text-gray-700">
              {{ formTranslation.clinic.clinic }}
              <span class="text-red-500">*</span>
            </label>
            <label v-if="isServiceEdit" class="flex items-center space-x-2">
              <input type="checkbox" v-model="clinicSelectAll" class="rounded border-gray-300 text-blue-600" />
              <span class="text-sm text-gray-600">Select All</span>
            </label>
          </div>
          <vue-multiselect v-model="service.clinic_id" :options="clinic" :multiple="isServiceEdit"
            :close-on-select="!isServiceEdit" :clear-on-select="false" :preserve-search="true"
            placeholder="Select clinic" label="label" track-by="id" :loading="clinicMultiselectLoader"
            @input="clinicChange" class="multiselect-primary" />
          <p v-if="submitted && !$v.service.clinic_id.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.common.clinic_is_required }}
          </p>
        </div>

        <!-- Doctor Selection -->
        <div v-if="getUserRole() !== 'doctor' && !hideDoctor" class="space-y-2">
          <div class="flex justify-between items-center">
            <label class="block text-sm font-medium text-gray-700">
              {{ formTranslation.common.doctor }}
              <span class="text-red-500">*</span>
            </label>
            <label v-if="isServiceEdit" class="flex items-center space-x-2">
              <input type="checkbox" v-model="doctorSelectAll" class="rounded border-gray-300 text-blue-600" />
              <span class="text-sm text-gray-600">Select All</span>
            </label>
          </div>
          <vue-multiselect v-model="service.doctor_id" :options="doctors" :multiple="isServiceEdit"
            :close-on-select="!isServiceEdit" :clear-on-select="false" :preserve-search="true"
            placeholder="Select doctor" label="label" track-by="id" :loading="doctorMultiselectLoader"
            class="multiselect-primary" />
          <p v-if="submitted && !$v.service.doctor_id.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.appointments.doc_required }}
          </p>
        </div>

        <!-- Duration -->
        <div v-if="userData.addOns.kiviPro" class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.patient_encounter.duration }}
          </label>
          <vue-multiselect v-model="service.duration" :options="time_slots" :searchable="false" :close-on-select="true"
            :show-labels="false" :custom-label="(opt) => opt.label" placeholder="Select duration" label="label"
            track-by="value" @input="(duration) => (service.duration = duration)" class="multiselect-primary" />
          <p class="text-sm text-gray-500 mt-1">
            Select 0min to create a service that doesn't block doctor availability.
          </p>
        </div>

        <!-- Status -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.status }}
            <span class="text-red-500">*</span>
          </label>
          <vue-multiselect v-model="service.status" :options="[
            { id: 1, label: formTranslation.common.active },
            { id: 0, label: formTranslation.common.inactive },
          ]" :searchable="false" :close-on-select="true" :show-labels="false" placeholder="Select status"
            label="label" track-by="id" class="multiselect-primary" />
          <p v-if="submitted && !$v.service.status.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.appointments.status_required }}
          </p>
        </div>

        <!-- Multiservice - Hidden and set to 'no' by default -->
        <!-- <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.include_in_multiservice }}
            <span class="text-red-500">*</span>
          </label>
          <vue-multiselect v-model="service.multiservice" :options="multiServiceOptions" :searchable="false"
            :close-on-select="true" :show-labels="false" placeholder="Select service type" label="label" track-by="id"
            class="multiselect-primary" />
          <p v-if="submitted && !$v.service.multiservice.required" class="text-sm text-red-500 mt-1">
            {{ formTranslation.appointments.status_required }}
          </p>
        </div> -->

        <!-- Profile Image -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">
            {{ formTranslation.common.profile_image }}
          </label>
          <div class="flex items-center gap-4">
            <div class="relative w-20 h-20 rounded-md overflow-hidden bg-gray-100 border border-gray-200">
              <div v-if="service.image || profileImage" class="w-full h-full bg-cover bg-center" :style="'background-image: url(' +
                (service.image ? service.image : profileImage) +
                ');'
                " />
              <div v-else class="w-full h-full flex items-center justify-center">
                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <button type="button" @click="uploadProfile"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
              <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
              </svg>
              {{ formTranslation.common.change_image }}
            </button>
          </div>
        </div>
      </div>

    </form>

    <!-- Service-Specific Availability Section (Outside the form) -->
    <div v-if="serviceId !== -1 && serviceId !== undefined" class="mt-8 pt-6 border-t border-gray-200">
      <ServiceSessionsTab :selectedServiceData="selectedServiceData" />
    </div>

    <!-- Footer buttons (Outside the form) -->
    <div class="flex justify-end pt-6 border-t border-gray-200 mt-8">
      <div class="flex gap-3">
        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          @click="closeForm">
          {{ formTranslation.common.cancel }}
        </button>
        <button type="button" @click="handleSubmit" :disabled="loading"
          class="px-8 py-2 bg-black text-white rounded-md hover:bg-black disabled:opacity-50">
          <span v-if="loading" class="inline-block animate-spin mr-2">↻</span>
          {{
            loading
              ? formTranslation.common.loading
              : formTranslation.service.save_btn
          }}
        </button>
      </div>
    </div>

  </div>
</template>

<script>
import VueMultiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";
import {
  maxValue,
  minValue,
  required,
  requiredIf,
} from "vuelidate/lib/validators";
import { post, get } from "../../config/request";
import { validateForm } from "../../config/helper";
import ServiceSessionsTab from "../../components/service/ServiceSessionsTab.vue";

export default {
  components: {
    VueMultiselect,
    ServiceSessionsTab,
  },

  props: {
    selectedServiceData: {
      type: Object,
      default: () => ({}),
    },
    serviceId: [Number, String],
    props_doctor_id: {
      type: [Number, String],
      default: -1,
    },
  },

  data: () => ({
    curency: {},
    oldDoctorList: [],
    serviceSelectAll: [],
    doctorSelectAll: false,
    clinicSelectAll: false,
    types: [],
    service: {},
    loading: false,
    submitted: false,
    doctors: [],
    clinic: [],
    clinic_id: 0,
    doctor_id: 0,
    hideDoctor: false,
    profileImage: "",
    categoryMultiselectLoader: true,
    doctorMultiselectLoader: true,
    time_slots: [],
    multiServiceOptions: [
      { id: "no", label: "Single" },
      { id: "yes", label: "Multiple" },
    ],
    clinicMultiselectLoader: false,
    selected_clinic_id: [],
    service_clinic_id: 0,
  }),

  validations: {
    service: {
      type: { required },
      name: { required },
      clinic_id: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.kiviPro == true &&
            (this.getUserRole() == "administrator" ||
              this.getUserRole() == "doctor") &&
            this.clinicField !== false
          );
        }),
      },
      doctor_id: {
        required: requiredIf(function () {
          return this.getUserRole() !== "doctor";
        }),
      },
      price: {
        required,
        minValue: minValue(0),
        maxValue: maxValue(1000000000000000000),
      },
      status: { required },
      // multiservice is no longer required since it's hidden and always set to "no"
      // multiservice: { required },
      telemed_service: {
        required: requiredIf(function () {
          return (
            this.userData.addOns.telemed || this.userData.addOns.googlemeet
          );
        }),
      },
    },
  },

  async mounted() {
    await this.init();

    if (this.props_doctor_id !== undefined && this.props_doctor_id !== -1) {
      this.hideDoctor = true;
      // Find doctor in the list or create a new object
      const doctor = this.doctors.find(
        (d) => d.id === this.props_doctor_id
      ) || {
        id: this.props_doctor_id,
        label: await this.getDoctorName(this.props_doctor_id),
      };
      this.service.doctor_id = doctor;
      this.clinicList({
        data_type: "clinics",
        doctor_id: this.props_doctor_id,
      });
    } else {
      this.clinicList({ data_type: "clinic_list" });
    }

    this.profileImage =
      window.request_data.kiviCarePluginURL + "assets/images/kc-demo-img.png";
  },

  methods: {
    init() {
      this.service = this.defaultServiceData();
      this.getTimeSlots();
      this.getServiceType();
      this.setupClinicAndDoctor();
      this.setupMultiServiceOptions();

      if (this.serviceId != -1) {
        this.get_service(this.serviceId);
      }
    },

    setupClinicAndDoctor() {
      if (this.$store.state.userDataModule.clinic) {
        this.clinic_id = this.$store.state.userDataModule.clinic.id;
        if (this.getUserRole() !== "doctor") {
          if (!this.hideDoctor) {
            this.getDoctorDropdown().then(() => {
              // Set doctor after dropdown is loaded
              if (this.props_doctor_id !== -1) {
                const selectedDoctor = this.doctors.find(
                  (d) => d.id === this.props_doctor_id
                );
                if (selectedDoctor) {
                  this.service.doctor_id = selectedDoctor;
                }
              }
            });
          }
        } else {
          const currentUser = this.$store.state.userDataModule.user;
          this.service.doctor_id = {
            id: currentUser.ID,
            label: currentUser.display_name,
          };
        }
      } else {
        this.$store
          .dispatch("userDataModule/fetchUserData", {})
          .then(() => this.setupClinicAndDoctor());
      }
    },

    setupMultiServiceOptions() {
      this.multiServiceOptions = this.multiServiceOptions.map((item) => ({
        ...item,
        label: this.formTranslation.common[item.id],
      }));
    },

    getTimeSlots() {
      this.time_slots = [
        { value: 0, label: "0min (No block)" },
        { value: 5, label: "5min" },
        { value: 10, label: "10min" },
        { value: 15, label: "15min" },
        { value: 20, label: "20min" },
        { value: 25, label: "25min" },
        { value: 30, label: "30min" },
        { value: 35, label: "35min" },
        { value: 40, label: "40min" },
        { value: 45, label: "45min" },
        { value: 50, label: "50min" },
        { value: 55, label: "55min" },
        { value: 60, label: "1hr" },
        { value: 75, label: "1hr 15min" },
        { value: 90, label: "1hr 30min" },
        { value: 105, label: "1hr 45min" },
        { value: 120, label: "2hr" },
        { value: 150, label: "2hr 30min" },
      ];
    },

    defaultServiceData() {
      return {
        type: "",
        name: "",
        doctor_id: "",
        service_id: "",
        price: "",
        status: { id: 1, label: this.formTranslation.common.active },
        multiservice: { id: "no", label: this.formTranslation.common.no },
        telemed_service: "no",
        duration: "",
        clinic_id: "",
      };
    },

    closeForm() {
      if (this.hideDoctor) {
        this.$emit("closeServiceModal");
        this.hideDoctor = false;
      }
      this.$emit("closeForm");
      if (this.$route.params.id !== undefined) {
        this.$router.push({ name: "service" });
      }
    },

    clinicChange(selectedOption) {
      if (this.getUserRole() !== "doctor") {
        // Handle clinic selection and update doctors list
        this.service.doctor_id = "";
        this.doctorSelectAll = false;
        this.doctorMultiselectLoader = true;

        const clinic_id = Array.isArray(selectedOption)
          ? selectedOption.map((c) => c.id).join(",")
          : selectedOption.id;

        get("get_static_data", {
          data_type: "get_users_by_clinic",
          clinic_id,
          telemed_service: this.service.telemed_service,
          type: "doctor",
        })
          .then((response) => {
            this.doctorMultiselectLoader = false;
            if (response.data.status) {
              this.doctors = response.data.data;
            }
          })
          .catch((error) => {
            this.doctorMultiselectLoader = false;
            console.error(error);
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      }
    },

    uploadProfile() {
      const custom_uploader = kivicareCustomImageUploader(this.formTranslation);

      custom_uploader.on("select", () => {
        const attachment = custom_uploader
          .state()
          .get("selection")
          .first()
          .toJSON();
        this.profileImage = attachment.url;
        this.service.profile_image = attachment.id;
        this.service.image = this.profileImage;
      });

      custom_uploader.open();
    },

    // Replace the existing handleSubmit method with this updated version
    handleSubmit: function () {
      this.loading = true;
      this.submitted = true;

      // stop here if form is invalid
      this.$v.$touch();
      if (this.$v.service.$invalid) {
        this.loading = false;
        return;
      }

      if (validateForm("serviceForm")) {
        // Format the service data to match the required payload structure
        let formattedService = {
          id: this.service.id || "",
          service_id: this.service.id || "",
          name: this.service.name,
          price: parseFloat(this.service.price) || 0,
          route_name: "service_save",
          image: this.service.image || "",
          _ajax_nonce: window.request_data._ajax_nonce || "", // Add the nonce from your request data

          // Maintain object structure for these fields
          type: this.service.type || { id: "", label: "" },
          status: this.service.status || {
            id: 1,
            label: this.formTranslation.common.active,
          },
          // Always set multiservice to "no" regardless of what's in the form
          multiservice: {
            id: "no",
            label: this.formTranslation.common.no,
          },

          // Handle clinic_id as object
          clinic_id: this.service.clinic_id || { id: "", label: "" },

          // Handle doctor_id as object
          doctor_id: this.service.doctor_id || { id: "", label: "" },

          // Simple string fields
          telemed_service: this.service.telemed_service || "no",
        };

        // Handle duration
        if (
          typeof this.service.duration === "object" &&
          this.service.duration.HH !== undefined
        ) {
          // If using vue2-timepicker format
          const hours = parseInt(this.service.duration.HH) || 0;
          const minutes = parseInt(this.service.duration.mm) || 0;
          formattedService.duration = (hours * 60 + minutes).toString();
        } else {
          // If direct duration value
          formattedService.duration = this.service.duration || "0";
        }

        // Special handling for props_doctor_id if provided
        if (
          this.props_doctor_id !== undefined &&
          this.props_doctor_id !== -1 &&
          this.props_doctor_id !== "-1"
        ) {
          formattedService.doctor_id = {
            id: this.props_doctor_id.toString(),
            label:
              this.doctors.find((d) => d.id === this.props_doctor_id)?.label ||
              "",
          };
        }

        // Make the API call
        post("service_save", formattedService)
          .then((response) => {
            this.loading = false;
            this.submitted = false;

            if (response.data.status) {
              if (this.getUserRole() === "administrator") {
                this.$store.dispatch("userDataModule/fetchUserData", {});
              }

              this.service = this.defaultServiceData();
              displayMessage(response.data.message);
              this.$emit("closeForm");
              this.$emit("getServicesListData");
              // this.getService();

              if (this.hideDoctor) {
                this.$emit("closeServiceModal");
                this.hideDoctor = false;
              }
            } else {
              displayErrorMessage(response.data.message);
            }
          })
          .catch((error) => {
            console.error("Service save error:", error);
            this.loading = false;
            this.submitted = false;
            displayErrorMessage(
              this.formTranslation.common.internal_server_error
            );
          });
      } else {
        this.loading = false;
      }
    },

    formatDuration(duration) {
      if (duration === 0 || duration === "0") return "00:00";
      if (!duration) return "00:00";
      const minutes = parseInt(duration);
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours.toString().padStart(2, "0")}:${remainingMinutes
        .toString()
        .padStart(2, "0")}`;
    },

    getServiceType() {
      this.categoryMultiselectLoader = true;
      // Updated to use new category system instead of static_data
      get("category_list", {
        module_type: "service"
      })
        .then((response) => {
          if (response.data.status) {
            // Transform category data to match the expected format for multiselect
            this.types = response.data.data.map(category => ({
              id: category.id,
              label: category.name,
              slug: category.slug,
              visibility: category.visibility
            }));

            // Add system service category if not present
            if (
              this.types.length > 0 &&
              !this.types.some((t) => t.id === "system_service")
            ) {
              this.types.push({
                id: "system_service",
                label: "System Service",
              });
            }
          }
          this.categoryMultiselectLoader = false;
        })
        .catch((error) => {
          this.categoryMultiselectLoader = false;
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    addNewServiceCategory(value) {
      // Updated to use new category system
      const categoryObj = {
        name: value,
        module_type: "service",
        slug: value.toLowerCase().replace(/\s+/g, '_'),
        visibility: "public",
        status: 1,
        sort_order: 999
      };

      post("category_save", categoryObj)
        .then((response) => {
          if (response.data.status) {
            const newType = {
              id: response.data.data.id,
              label: value,
              slug: response.data.data.slug,
              visibility: response.data.data.visibility
            };
            this.types.push(newType);
            this.service.type = newType;
            this.getServiceType();
          }
        })
        .catch((error) => {
          console.error(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    async getDoctorDropdown() {
      this.doctorMultiselectLoader = true;
      try {
        const response = await get("get_static_data", {
          data_type: "clinic_doctors",
          module_type: "service_module",
          clinic_id: this.service_clinic_id,
        });

        if (response.data.status) {
          this.doctors = response.data.data;
          this.oldDoctorList = this.doctors;

          // If we have a preselected doctor, set it now
          if (this.service.doctor_id) {
            const doctorId =
              typeof this.service.doctor_id === "object"
                ? this.service.doctor_id.id
                : this.service.doctor_id;
            const selectedDoctor = this.doctors.find((d) => d.id === doctorId);
            if (selectedDoctor) {
              this.service.doctor_id = selectedDoctor;
            }
          }
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.common.internal_server_error);
      } finally {
        this.doctorMultiselectLoader = false;
      }
    },

    async get_service(serviceId) {
      if (!serviceId) return;

      try {
        const response = await get("service_edit", { id: serviceId });

        if (response.data.status && response.data.data) {
          const serviceData = response.data.data;

          // Convert duration from "HH:mm" to minutes and find matching time slot
          const [hours, minutes] = (serviceData.duration || "00:00")
            .split(":")
            .map(Number);
          const durationInMinutes = hours * 60 + minutes;
          const durationSlot = this.time_slots.find(
            (slot) => slot.value === durationInMinutes
          );

          this.service = {
            ...this.defaultServiceData(),
            ...serviceData,
            duration: durationSlot || "", // Set the matched duration object
            type: serviceData.type || "",
            price: parseFloat(serviceData.price) || 0,
            multiservice: serviceData.multiservice || {
              id: "yes",
              label: this.formTranslation.common.yes,
            },
          };

          if (serviceData.clinic_id?.id) {
            this.service_clinic_id = serviceData.clinic_id.id;
            await this.getDoctorDropdown();
          }

          this.telemedEnableChange(serviceData.telemed_service);

          // Scroll to top
          this.$el?.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      } catch (error) {
        console.error(error);
        displayErrorMessage(this.formTranslation.widgets.record_not_found);
      }
    },

    telemedEnableChange(selectedValue) {
      if (this.userData.addOns.telemed || this.userData.addOns.googlemeet) {
        this.doctorSelectAll = false;
        this.doctors = this.oldDoctorList;

        if (selectedValue === "yes") {
          let selected_doctor_telemed_active = false;
          this.doctors = this.doctors.filter((val) => {
            if (
              val.enableTeleMed &&
              this.service.doctor_id &&
              this.service.doctor_id.id
            ) {
              if (val.id == this.service.doctor_id.id) {
                selected_doctor_telemed_active = true;
              }
            }
            return val.enableTeleMed;
          });

          if (
            !selected_doctor_telemed_active &&
            this.getUserRole() !== "doctor"
          ) {
            this.service.doctor_id = {};
          }
        } else {
          if (this.service.id !== undefined) {
            this.doctors = this.oldDoctorList;
          }
        }
      }
    },

    clinicList(data) {
      this.clinicMultiselectLoader = true;
      get("get_static_data", data)
        .then((response) => {
          this.clinicMultiselectLoader = false;
          if (response.data.status) {
            this.clinic = response.data.data;
          }
        })
        .catch((error) => {
          this.clinicMultiselectLoader = false;
          console.error(error);
          displayErrorMessage("Internal server error");
        });
    },
  },

  computed: {
    currency() {
      return this.currencyData;
    },

    currencyPrefix() {
      if (this.currency?.currency_prefix) {
        return this.currency.currency_prefix;
      }
      return "";
    },

    currencyPostfix() {
      if (this.currency?.currency_postfix) {
        return this.currency.currency_postfix;
      }
      return "";
    },

    clinicId() {
      return this.$store.state.userDataModule.clinic?.id;
    },

    userData() {
      return this.$store.state.userDataModule.user;
    },

    isServiceEdit() {
      return !(
        this.serviceId !== undefined &&
        this.serviceId !== 0 &&
        this.serviceId !== -1
      );
    },
  },

  watch: {
    selectedServiceData: {
      immediate: true,
      handler(newVal) {
        if (Object.keys(newVal).length > 0) {
          // Convert duration from "HH:mm" to minutes and find matching time slot
          const [hours, minutes] = (newVal.duration || "00:00")
            .split(":")
            .map(Number);
          const durationInMinutes = hours * 60 + minutes;
          const durationSlot = this.time_slots.find(
            (slot) => slot.value === durationInMinutes
          );

          this.service = {
            ...this.defaultServiceData(),
            ...newVal,
            type: newVal.type || "",
            price: parseFloat(newVal.price) || 0,
            doctor_id: newVal.doctor_id || "",
            clinic_id: newVal.clinic_id || "",
            duration: durationSlot || "", // Set the matched duration object
            // Always set multiservice to "no" regardless of what's in the data
            multiservice: {
              id: "no",
              label: this.formTranslation.common.no,
            },
            status: newVal.status || {
              id: 1,
              label: this.formTranslation.common.active,
            },
            telemed_service: newVal.telemed_service || "no",
          };

          if (this.service.clinic_id?.id) {
            this.service_clinic_id = this.service.clinic_id.id;
            this.getDoctorDropdown();
          }

          this.telemedEnableChange(this.service.telemed_service);
        } else {
          this.service = this.defaultServiceData();
        }
      },
    },

    doctorSelectAll(value) {
      if (
        this.serviceId == undefined ||
        this.serviceId == 0 ||
        this.serviceId == -1
      ) {
        if (value) {
          this.service.doctor_id = this.doctors;
        } else {
          this.service.doctor_id = [];
        }
      }
    },

    clinicSelectAll(value) {
      if (value) {
        this.service.clinic_id = this.clinic;
      } else {
        this.service.clinic_id = [];
      }
      this.clinicChange(this.service.clinic_id);
    },
  },
};
</script>
