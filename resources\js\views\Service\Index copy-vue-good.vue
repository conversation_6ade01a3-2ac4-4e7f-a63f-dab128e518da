<template>
  <div class="bg-white rounded-lg shadow p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-xl font-semibold text-gray-800">{{formTranslation.service.service_list}}</h1>
      <div class="flex gap-3">
        <button 
          v-if="userData.addOns.kiviPro && kcCheckPermission('service_add') && kivicareCompareVersion(requireProVersion,userData.pro_version)"
          class="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition"
          @click="$refs.module_data_import.openModel = true"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="17 8 12 3 7 8"/>
            <line x1="12" x2="12" y1="3" y2="15"/>
          </svg>
          {{formTranslation.common.import_data}}
        </button>
        <button 
          v-if="kcCheckPermission('service_add')"
          class="flex items-center gap-2 px-4 py-2 text-white bg-black rounded-lg hover:bg-gray-800 transition"
          @click="handleServiceDataForm({})"
        >
          <svg v-if="!visible" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M5 12h14"/>
            <path d="M12 5v14"/>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M5 12h14"/>
          </svg>
          {{ (visible ? formTranslation.service.close_form_btn : formTranslation.service.add_service_btn) }}
        </button>
      </div>
    </div>

    <!-- Import Module -->
    <module-data-import 
      v-if="userData.addOns.kiviPro && kcCheckPermission('service_add') && kivicareCompareVersion(requireProVersion,userData.pro_version)" 
      ref="module_data_import" 
      @reloadList="getServiceData"
      :required_data="[
        {label:formTranslation.service.category,value:'category'},
        {label:formTranslation.service.name,value:'name'},
        {label:formTranslation.service.charges,value:'charges'},
        {label:formTranslation.service.doctor+' ' +formTranslation.service.id,value:'doctor_id'},
      ]" 
      :module_name="formTranslation.common.service" 
      module_type="service"
    />

    <!-- Add/Edit Forms -->
    <div class="mb-6">
      <div id="static-data-add-tab" :class="{ hidden: !showAddForm }">
        <Create 
          :serviceId="-1" 
          @getServiceData="getServiceData" 
          @closeForm="closeForm" 
          v-if="showAddForm"
        />
      </div>
      <div id="static-data-edit-tab" :class="{ 'mt-2': true, hidden: !showEditForm }">
        <Create 
          :serviceId="serviceId" 
          @getServiceData="getServiceData" 
          @closeForm="closeForm"
          v-if="serviceId && showEditForm"
        />
      </div>
    </div>

    <!-- Table Section -->
    <div class="relative">
      <!-- Loader -->
      <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10" v-show="pageLoader">
        <loader-component-2/>
      </div>

      <!-- Vue Good Table with new design -->
      <vue-good-table
        ref="dataTable"
        :columns="serviceList.column"
        :rows="serviceList.data"
        :search-options="{
          enabled: true,
          placeholder: formTranslation.common.search_service_field_data_global_placeholder,
          className: 'pl-10 pr-4 py-2 border border-gray-200 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full mb-6'
        }"
        :select-options="{
          enabled: true,
          selectOnCheckboxOnly: true,
          selectionInfoClass: 'text-primary',
          selectionText: formTranslation.common.rows_selected,
          clearSelectionText: formTranslation.common.clear,
          disableSelectInfo: false,
          selectAllByGroup: true,
        }"
        :pagination-options="{
          enabled: true,
          mode: 'pages',
          className: 'px-4 py-3 flex items-center justify-between border-t border-gray-200'
        }"
        styleClass="vgt-table border border-gray-200 rounded-lg overflow-hidden"
        mode="remote"
        @on-sort-change="onSortChange"
        @on-column-filter="onColumnFilter"
        @on-page-change="onPageChange"
        @on-per-page-change="onPerPageChange"
        @on-search="globalFilter"
        @on-selected-rows-change="(selected_row) => { globalCheckboxApplyData.data = selected_row }"
        :totalRows="totalRows"
      >
        <!-- Empty state -->
        <div slot="emptystate" class="text-gray-500 text-center py-8">
          {{formTranslation.common.no_data_found}}
        </div>

        <!-- Column Filters -->
        <template slot="column-filter" slot-scope="{ column, updateFilters }">
          <div v-if="column.field === 'service_type'" class="px-4 py-2">
            <select 
              class="w-full border border-gray-200 rounded-lg px-3 py-2"
              v-model="serverParams.columnFilters.service_type"  
              @change="(value) => updateFilters(column, serverParams.columnFilters.service_type)"
            >
              <option value="">{{ column.filterOptions.placeholder }}</option>
              <option v-for="(value,index) in serviceCategory" :value="value.value" :key="index">
                {{value.text}}
              </option>
            </select>
          </div>
          <div v-else-if="column.field === 'duration'" class="px-4 py-2">
            <vue-timepicker 
              v-model="serverParams.columnFilters.duration" 
              :minute-interval="5"
              class="w-full border border-gray-200 rounded-lg"
              @change="(value) => updateFilters(column, serverParams.columnFilters.duration)"
            />
          </div>
        </template>

        <!-- Table Actions -->
        <div slot="table-actions" class="px-4 py-2">
          <module-data-export 
            :module_data="serviceList.data" 
            :module_name="formTranslation.service.service_list"  
            module_type="services" 
            v-if="kcCheckPermission('service_export')"
          />
        </div>

        <!-- Selected Row Actions -->
        <div slot="selected-row-actions" class="px-4 py-2 flex justify-end items-center gap-2">
          <select 
            class="border border-gray-200 rounded-lg px-3 py-2" 
            v-model="globalCheckboxApplyData.action_perform"
          >
            <option v-for="(option, index) in globalCheckboxApplyDataActions" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          <button 
            class="px-4 py-2 text-white bg-black rounded-lg hover:bg-gray-800 transition"
            @click="confirmDelete"
          >
            {{ formTranslation.common.apply }}
          </button>
        </div>

        <!-- Table Row Template -->
        <template slot="table-row" slot-scope="props">
          <!-- Name Column with Avatar -->
          <div v-if="props.column.field == 'name'" class="flex items-center gap-3 px-4 py-2">
            <img 
              v-if="props.row.image != '' && props.row.image != null" 
              :src="props.row.image" 
              alt="service_image"  
              class="h-12 w-12 rounded-full object-cover"
            />
            <div 
              v-else 
              class="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center text-gray-600"
            >
              {{getInitials(props.formattedRow.name)}}
            </div>
            <span>{{props.formattedRow.name}}</span>
          </div>

          <!-- Status Column -->
          <div v-else-if="props.column.field == 'status'" class="flex items-center gap-2 px-4 py-2">
            <!-- Toggle Switch -->
            <div 
              v-if="kcCheckPermission('service_edit')"
              class="relative w-10 h-5 flex items-center rounded-full p-1 cursor-pointer transition-colors ease-in-out duration-200"
              :class="props.row.status === '1' ? 'bg-black' : 'bg-gray-300'"
              @click="changeModuleValueStatus({
                module_type:'doctor_service',
                id: props.row.id,
                value: props.row.status === '1' ? '0' : '1'
              })"
            >
              <div 
                class="w-4 h-4 rounded-full bg-white transform transition-transform duration-200"
                :class="props.row.status === '1' ? 'translate-x-4' : 'translate-x-0'"
              />
            </div>
            <!-- Status Badge -->
            <span 
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              :class="props.row.status === '1' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
            >
              {{props.row.status === '1' ? formTranslation.common.active : formTranslation.common.inactive}}
            </span>
          </div>

          <!-- Actions Column -->
          <div v-else-if="props.column.field == 'actions'" class="flex gap-2 px-4 py-2">
            <button 
              v-if="kcCheckPermission('service_edit')"
              class="p-1 hover:bg-gray-100 rounded transition"
              @click="editServiceData(props.row, props.row.id)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600">
                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"/>
              </svg>
            </button>
            <button 
              v-if="kcCheckPermission('service_delete') && (!['telemed','Telemed'].includes(props.row.name))"
              class="p-1 hover:bg-gray-100 rounded transition"
              @click="deleteServiceData(props)"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500">
                <path d="M3 6h18"/>
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                <line x1="10" x2="10" y1="11" y2="17"/>
                <line x1="14" x2="14" y1="11" y2="17"/>
              </svg>
            </button>
          </div>

          <!-- Duration Column -->
          <div v-else-if="props.column.field == 'duration'" class="px-4 py-2">
            {{formatDuration(props.row.duration)}}
          </div>

          <!-- Default Column -->
          <div v-else class="px-4 py-2">
            {{ props.formattedRow[props.column.field] }}
          </div>
        </template>
      </vue-good-table>
    </div>
  </div>
</template>

<script>

import VueTimepicker from 'vue2-timepicker';
import 'vue2-timepicker/dist/VueTimepicker.css';
import {post, get} from "../../config/request";
import Create from "../Service/Create";

export default {
  components: {Create, VueTimepicker},
  data: () => {
    return {
      visible: false,
      showEditForm: false,
      showAddForm: false,
      pageLoader: true,
      serviceList: {
        data:[],
        column:[]
      },
      sessionEdit: false,
      serviceId: -1,
      editSessionDataIndex: "",
      serverParams: {
        columnFilters: {
          service_type:'',
          duration:''
        },
        sort: [
          {
            field: '',
            type: ''
          }
        ],
        page: 1,
        perPage: 10,
        searchTerm:'',
        type:'list'
      },
      oldServerParams:{
        columnFilters:{

        },
        searchTerm:'',
        perPage:10
      },
      totalRows: 0,
      serviceCategory:[],
      globalCheckboxApplyData: {},
      globalCheckboxApplyDataActions: [

      ]
    }
  },
  mounted() {
    this.getServiceCategoryType();
    this.init();
  },
  methods: {
    // changeDurationHandler(event) {
    //   const duration = parseInt(event.data.HH) * 60 + parseInt(event.data.mm);
    //   this.serverParams.columnFilters.duration = duration.toString();
    // },
    init: function () {
      this.serviceList = this.defaultServiceList()
      this.getServiceData();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData()
      this.globalCheckboxApplyDataActions = this.defaultGlobalCheckboxApplyDataActions()
    },
    defaultServiceList: function () {
      return {
        column: [
          {
            field: 'id',
            label: this.formTranslation.common.id,
            width:'100px',
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.common.id,
              filterValue: '',
            }
          },
          {
            field: 'service_id',
            label: this.formTranslation.widget_setting.service_setting + ' '+ this.formTranslation.common.id,
            width:'120px',
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.widget_setting.service_setting+ ' '+ this.formTranslation.common.id,
              filterValue: '',
            }
          },
          {
            field: 'name',
            width:'150px',
            label: this.formTranslation.service.dt_lbl_name,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_name_fltr,
              filterValue: '',
            }
          },
          {
            field: 'clinic_name',
            width:'150px',
            label: this.formTranslation.patient_encounter.dt_lbl_clinic,
            filterOptions: {
              enabled: !(window.request_data.current_user_role === 'kiviCare_clinic_admin' || window.request_data.current_user_role === 'kiviCare_receptionist'),
              placeholder: this.formTranslation.patient_encounter.dt_plh_fltr_by_clinic,
              filterValue: '',
            }
          },
          {
            field: 'doctor_name',
            width:'150px',
            label: this.formTranslation.service.dt_lbl_doctor,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_fltr_by_doc,
              filterValue: '',
            }
          },
          {
            field: 'charges',
            width:'150px',
            label: this.formTranslation.service.dt_lbl_charges,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_fltr_by_price,
              filterValue: '',
            }
          },
          {
            field: 'duration',
            width:'150px',
            label: this.formTranslation.patient_encounter.duration,
            hidden:this.userData.addOns.kiviPro !== true,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient_encounter.duration,
              filterValue: '',
              // filterDropdownItems:[
              //   {value:5,text:'5min'},
              //   {value:10,text:'10min'},
              //   {value:15,text:'15min'},
              //   {value:20,text:'20min'},
              //   {value:25,text:'25min'},
              //   {value:30,text:'30min'},
              //   {value:35,text:'35min'},
              //   {value:40,text:'40min'},
              //   {value:45,text:'45min'},
              //   {value:50,text:'50min'},
              //   {value:55,text:'55min'},
              //   {value:60,text:'1hr'},
              //   {value:75,text:'1hr 15min'},
              //   {value:90,text:'1hr 30min'},
              //   {value:105,text:'1hr 45min'},
              //   {value:120,text:'2hr'},
              //   {value:150,text:'2hr 30min'}
              // ]
            }
          },
          {
            field: 'service_type',
            label: this.formTranslation.service.dt_lbl_category,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_name_fltr,
              filterDropdownItems:this.serviceCategory,
              filterValue: '',
            }
          },
          {
            field: 'status',
            label: this.formTranslation.service.dt_lbl_status,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
              filterValue: '',
              filterDropdownItems: [
                { value: '1', text: this.formTranslation.common.active },
                { value: '0', text: this.formTranslation.common.inactive } ]
            },
          },
          {
            field: 'actions',
            sortable:false,
            label: this.formTranslation.service.dt_lbl_action
          }
        ],
        data: []
      }
    },
    getServiceData: function () {
      this.pageLoader = true;
      get('service_list', this.serverParams)
          .then((data) => {
            this.pageLoader = false;
            if (data.data.status !== undefined && data.data.status === true) {

              this.serviceList.data = [];
              this.serviceList.data = data.data.data
              this.totalRows = data.data.total_rows;
            } else {
              this.pageLoader = false;
              this.serviceList.data = [];
              this.totalRows = 0;
            }
          })
          .catch((error) => {
            this.pageLoader = false;
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error);
          })
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: 'delete',
        module: 'doctor_service',
        data: []
      }
    },

    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt
        },
      ]
    },

    confirmDelete() {
      let content = '';
      if (this.globalCheckboxApplyData.action_perform === 'delete') {
        content = this.formTranslation.common.py_delete;
      } else if (this.globalCheckboxApplyData.action_perform === 'active' || this.globalCheckboxApplyData.action_perform === 'inactive') {
        content = this.formTranslation.common.py_status;
      }
      $.confirm({
        title: this.formTranslation.clinic_schedule.dt_are_you_sure,
        content: content,
        type: 'red',
        buttons: {
          ok: {
            text: this.formTranslation.common.yes,
            btnClass: 'btn-danger',
            keys: ['enter'],
            action: () => {        
              this.globalCheckboxApply();
            }
          },
          cancel:{
            text:this.formTranslation.common.cancel
          }
        }
      });
    },

    globalCheckboxApply() {
      this.pageLoader = true;
      post('module_wise_multiple_data_update', this.globalCheckboxApplyData)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
            this.getServiceData();
          } else {
            displayErrorMessage(data.data.message)
          }
        })
        .catch((error) => {
          this.pageLoader = true;
          console.log(error);
        })
    },
    
    updateParams: function(newProps) {
      this.serverParams = Object.assign({}, this.serverParams, newProps);
      this.getServiceData()
    },

    onPageChange(params) {
      this.updateParams({page: params.currentPage});
    },

    onPerPageChange(params) {
      if(this.oldServerParams.perPage === params.currentPerPage){
        return ;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({perPage: params.currentPerPage,page: params.currentPage});
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter:_.debounce(function(params) {

      if(this.oldServerParams.searchTerm === params.searchTerm){
        return;
      }
      this.oldServerParams.searchTerm = params.searchTerm;
      this.updateParams({searchTerm: params.searchTerm,perPage: this.serverParams.perPage,page: 1});
    },300),
    onColumnFilter:_.debounce(function(params) {
      var emptyValue =  true;
      var emptyValue2 = true;
      Object.values(params.columnFilters).map(function (value, index, array){
        if(value){
          emptyValue = false;
        }
      })
      Object.values(this.oldServerParams.columnFilters).map(function (value, index, array){
        if(value){
          emptyValue2 = false;
        }
      })
      if(!emptyValue || !emptyValue2){
        this.oldServerParams.columnFilters = Object.assign({}, params.columnFilters)
        this.updateParams({columnFilters:params.columnFilters,perPage: this.serverParams.perPage,page: 1});
      }
    },300),
    getServiceCategoryType () {
      let _this = this;
      get('get_static_data', {
        data_type: 'static_data_with_label',
        static_data_type: 'service_type'
      })
          .then((response) => {
            if (response.data.status !== undefined && response.data.status === true) {
              this.serviceCategory = [];
              if(response.data.data.length > 0) {
                let checkExists = false ;
                response.data.data.map(function(value,key){
                  if(value.id == 'system_service'){
                    checkExists = true;
                  }
                  _this.serviceCategory.push({"value": value.id, "text": value.label});
                })
                if(!checkExists){
                  this.serviceCategory.push({"value":'system_service',"text":'System Service'})
                }
              }
            } else {
              displayErrorMessage(response.data.message)
            }
          })
          .catch((error) => {
            console.log(error);
            displayErrorMessage(this.formTranslation.common.internal_server_error)
          })
    },
    deleteServiceData: function (data) {
      if (data.index !== undefined) {
        $.confirm({
          title: this.formTranslation.clinic_schedule.dt_are_you_sure ,
          content: this.formTranslation.common.deleting_services,
          type: 'red',
          buttons: {
            ok: {
              text:this.formTranslation.common.yes,
              btnClass: 'btn-danger',
              keys: ['enter'],
              action: () => {
                get('service_delete', {
                  id: data.row.id
                })
                  .then((response) => {
                    if(this.getUserRole() === 'administrator'){
                      this.$store.dispatch("userDataModule/fetchUserData", {});
                    }
                    if (response.data.status !== undefined && response.data.status === true) {
                      this.serviceList.data.splice(data.index, 1);
                      displayMessage(response.data.message);
                    }
                  })
                  .catch((error) => {

                    if (error.response.data !== undefined && error.response.data.message !== undefined) {
                      displayErrorMessage(error.response.data.message);
                    } else {
                      displayErrorMessage(this.formTranslation.common.internal_server_error);
                    }

                  })
              }
            },
            cancel: {
              text:this.formTranslation.common.cancel
            }
          }
        });
      }
    },
    editServiceData: function (data, collapseID) {
      this.serviceId = collapseID
      this.showAddForm = false
      this.showEditForm = true
      this.visible = false
    },
    handleServiceDataForm() {
      if (!this.showAddForm) {
        this.visible = true
        this.showAddForm = true
        this.showEditForm = false
      } else {
        this.visible = false
        this.showAddForm = false
        this.showEditForm = false
      }
    },
    closeForm() {
      this.visible = false
      this.serviceId = -1;
      this.showAddForm = false
      this.showEditForm = false
    },
    formatDuration(duration){
      if(!duration){
        return '';
      }
      if(parseInt(duration) > 0 ){
        var hours = Math.floor(duration / 60);
        var minutes = duration % 60;
        minutes += 'min';
        if(hours > 0){
          return hours+ 'hr ' + minutes;
        }
        return minutes;
      }

      return duration;
    },
    getInitials(name) {
      if(name !== undefined && name !== '' && name !== null){
        const patient_name = name.split(" ");
        const initials = patient_name.map(patient_name => patient_name.charAt(0).toUpperCase());
        return initials.join("");
      }else{
        return ' - ';
      }
    }
  },
  computed: {
    servicesListExport() {
      return 'Services List - ' + moment().format('YYYY-MM-DD');
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
    // formTranslation: function () {
    //   return this.$store.state.staticDataModule.langTranslateData ;
    // }
  },
  watch: {}
}
</script>
