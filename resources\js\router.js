import Vue from "vue";
import VueRouter from "vue-router";
import Dashboard from "./views/Dashboard/Index";
import AppointmentQueueList from "./views/Dashboard/AppointmentList";
import ERROR403 from "./views/Errors/403";

// Import for chat module
import ChatDashboard from "./views/Chat/ChatDashboard";

// Import for notifications module
import NotificationsIndex from "./views/Notifications/Index";

// Import for dictation module
import DictationIndex from "./views/Dictation/Index";

// Import for the setup wizard...
import SetupWizard from "./views/SetupWizard/Index";

// Import for static data module...
import StaticDataList from "./views/StaticData/Index";
import StaticDataCreate from "./views/StaticData/Create";

// Import for static data module...
import ServiceList from "./views/Service/Index";
import ServiceCreate from "./views/Service/Create";

// Import for the Doctors module...
import DoctorList from "./views/Doctor/Index";
import DoctorCreate from "./views/Doctor/Create";
import <PERSON><PERSON>ro<PERSON><PERSON> from "./views/Doctor/Profile";

// Import for the Patient module...
import PatientList from "./views/Patient/Index";
import PatientCreate from "./views/Patient/Create";
import PatientProfile from "./views/Patient/Profile";
import PatientProfileView from "./views/Patient/ProfileView";
import PatientSetting from "./views/Patient/UniqueIdSetting";

// Import for the widget setting module...
import WidgetSetting from "./views/WidgetSetting/WidgetSetting";

// Import for the Doctors module...
import ClinicList from "./views/Clinic/Index";
import ClinicCreate from "./views/Clinic/Create";
import ClinicProfile from "./views/Clinic/Profile";
import Update from "./components/Update";

import CommanSetting from "./views/ProSetting/CommanSetting";
import LanguageSetting from "./views/ProSetting/LanguageSetting";

// Import for the Appointment module...
import AppointmentCreate from "./views/Clinic/Create";

// Import for the Patient encounter module...
import PatientEncounterList from "./views/PatientEncounter/Index";
import PatientEncounterCreate from "./views/PatientEncounter/Create";

// Import for the Receptionist
import ReceptionistList from "./views/Receptionist/Index";
import ReceptionistCreate from "./views/Receptionist/Create";
import ReceptionistProfile from "./views/Receptionist/Profile";

// Import for the Billing records module...
import CustomFieldList from "./views/CustomField/Index";
import CustomFieldCreate from "./views/CustomField/Create";

// Import for the Terms & Condition  module...
import TermsConditionCreate from "./views/TermsCondition/Create";

// Import for the Clinic Schedule  module...
import ClinicScheduleSettingList from "./views/ClinicScheduleSetting/Index";
import SubscriptionSetting from "./views/Subscription/Setting";
import ClinicScheduleSettingCreate from "./views/ClinicScheduleSetting/Create";

// Import for the Notification module...
import NotificationSetting from "./views/Notification/Email/Create";
import SMSTemplate from "./views/Notification/SMS/Create";
import store from "./store";

// Import for the Common Setting module...
import CommonSetting from "./views/CommonSetting/Create";
import AppConfig from "./views/Settings/AppConfig";

// Import for General settings module...
import Settings from "./views/Settings/Index";

// Import for Payment setting module
import Payment from "./views/Payment/Index";

// Import for Payment setting module
import RestrictAppointment from "./views/Settings/AppointmentSetting";

// Import for Payment setting module
import GeneralSetting from "./views/Settings/GeneralSetting";

// Import for Payment setting module
import Subscription from "./views/Settings/Subscription";

// Import for Payment setting module
import GoogleMeet from "./views/Telemed/GoogleMeetConfiguration";

// Import for permission setting module
import PermissionSetting from "./views/Settings/PermissionSetting";

//Import for sidebar dashboard module
import DashboardSidebarSetting from "./views/Settings/DashboardSidebarSetting";

// Import for the Change password module...
import ChangePassword from "./views/ChangePassword/Create";

// Import for the Setup wizard step...
import SetupStep1 from "./views/SetupWizard/setupStep1";
import ModuleConfig from "./views/SetupWizard/moduleConfig";
import SetupStep2 from "./views/SetupWizard/setupStep2";
import SetupStep3 from "./views/SetupWizard/setupStep3";
import SetupStep4 from "./views/SetupWizard/setupStep4";
import SetupStep5 from "./views/SetupWizard/setupStep5";
import ClinicAdmin from "./views/SetupWizard/clinicAdmin";

// Import for All the Appointment List
import AllAppointmentList from "./views/Appointment/AllAppointment";
import AppointmentDataList from "./views/Appointment/AllappointmentList";

import PatientAppointmentList from "./views/Appointment/PatientAppointmentList";
import PatientConsultationList from "./views/PatientEncounter/PatientConsultationList";
import PatientDoctors from "./views/Doctor/PatientDoctors";
import PatientLabTest from "./views/LabTest/PatientLabTest";
import PatientInvoices from "./views/Payment/PatientInvoices";
import PatientMedications from "./views/Medications/PatientMedications";

import NewAppointment from "./views/Appointment/NewAppointment";
import AppointmentDashboard from "./views/Appointment/Dashboard";
import ClinicalDashboard from "./views/Patient/ClinicalDashboard";
import PatientPrescription from "./views/Appointment/PatientPrescription";

// Import for the Contacts module
import ContactList from "./views/Contacts/Index";
import ContactCreate from "./views/Contacts/Create";

// Import for activity logs
import ActivityLogs from "./views/ActivityLog/Index";

// Import for the Task Manager
import TaskDashboard from "./components/Task/TaskDashboard";

// Import for the Doctor Session
import DoctorSession from "./views/DoctorSession/Create";

// Import for the All Appointment List
import ZoomConfiguration from "./views/Telemed/ZoomConfiguration";

import Loading from "vue-loading-overlay";
import PatientMedicalReport from "./views/Patient/Report";
import BillingRecord from "./views/BillingRecord/Index";
import RevenuReport from "./views/Reports/RevenuReport";
import GoogleCalender from "./views/GoogleEvent/GoogleCalender";
import GoogleMeetConfig from "./views/Telemed/GoogleMeet";
import DoctorEncounterSetting from "./views/Settings/DoctorEncounterSetting";
import ZoomOAuth from "./views/Telemed/ZoomOAuth";
import ZoomOAuthConfig from "./views/Telemed/ZoomOAuthConfig";

import GoogleEventTemplate from "./views/GoogleEvent/Create";
// Import stylesheet

import TaxList from "./views/Tax/Index.vue";
import TaxCreate from "./views/Tax/Create";

// Import for Category module
import CategoryList from "./views/Category/Index.vue";
import CategoryCreate from "./views/Category/Create.vue";

import GetHelp from "./views/GetHelp/Index";
import GetPro from "./views/GetPro/Index";
import "vue-loading-overlay/dist/vue-loading.css";

import PatientClinic from "./views/Patient/PatientClinic";
import CustomNotification from "./views/Notification/Custom/Index";
import CustomForm from "./views/CustomForm/Index.vue";
import CustomFormCreate from "./views/CustomForm/Create.vue";

// Import for the SignatureRX module
import SignatureRXList from "./views/Prescription/SignatureRXList.vue";

//body chart addons
import BodyChartSetting from "./views/BodyChart/Setting";
import BodyChartIndex from "./views/BodyChart/Index";
import BodyChartCreate from "./views/BodyChart/Create";

//webhooks
import WebhooksIndex from "./views/Webhooks/Index";
import WebhooksCreate from "./views/Webhooks/Create";
import WebhooksLog from "./views/Webhooks/LogIndex.vue";

// Import for Template Manager
import TemplateManagerList from "./views/Templates/Index";

// Import for TDL Labs module
import TDLSettings from "./views/TDLLabs/TDLSettings";
import TDLTests from "./views/TDLLabs/TDLTests";
import TDLRequests from "./views/TDLLabs/TDLRequests";
import TDLRequestDetails from "./views/TDLLabs/TDLRequestDetails";
import TDLNewRequest from "./views/TDLLabs/TDLNewRequest";
import TDLResults from "./views/TDLLabs/TDLResults";
import TDLResultDetails from "./views/TDLLabs/TDLResultDetails";
import TDLImportResults from "./views/TDLLabs/TDLImportResults";

Vue.use(Loading);
Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "dashboard",
    component: Dashboard,
    meta: { label: "Home" },
  },
  {
    path: "/appointment-list",
    name: "appointment-list",
    meta: { label: "Appointments" },
    component: AppointmentQueueList,
  },
  {
    path: "/setup",
    name: "setup",
    meta: {
      setup: true,
      fullPage: true,
    },
    component: SetupWizard,
    children: [
      {
        path: "/setup/step-1",
        name: "setup.step1",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: SetupStep1,
      },
      {
        path: "/setup/step-2",
        name: "setup.step2",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: ModuleConfig,
      },
      {
        path: "/setup/step-3",
        name: "setup.step3",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: SetupStep2,
      },
      {
        path: "/setup/clinic/admin",
        name: "setup.clinic.admin",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: ClinicAdmin,
      },
      {
        path: "/setup/step-4",
        name: "setup.step4",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: SetupStep3,
      },
      {
        path: "/setup/step-5",
        name: "setup.step5",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: SetupStep4,
      },
      {
        path: "/setup/step-6",
        name: "setup.step6",
        meta: {
          setup: true,
          fullPage: true,
        },
        component: SetupStep5,
      },
    ],
  },

  { path: "/403", name: "403", component: ERROR403, meta: { fullPage: true } },
  { path: "/update", name: "update", component: Update },
  // Routes for the Static data module...
  {
    path: "/static-data",
    name: "static-data",
    component: StaticDataList,
    meta: {
      label: "Static Data",
      permission: "static_data_list",
      module: "static_data",
    },
  },
  {
    path: "/static-data/create",
    name: "static-data.create",
    component: StaticDataCreate,
    meta: {
      label: "Add Static Data",
      permission: "static_data_add",
      module: "static_data",
    },
  },
  {
    path: "/static-data/edit/:id",
    name: "static-data.edit",
    component: StaticDataCreate,
    meta: {
      label: "Edit Static Data",
      permission: "static_data_edit",
      module: "static_data",
    },
  },

  // Routes for the Static data module...
  {
    path: "/service",
    name: "service",
    component: ServiceList,
    meta: { label: "Services", permission: "service_list", module: "service" },
  },
  {
    path: "/service/create",
    name: "service.create",
    component: ServiceCreate,
    meta: {
      label: "Add Service",
      permission: "service_add",
      module: "service",
    },
  },
  {
    path: "/service/edit/:id",
    name: "service.edit",
    component: ServiceCreate,
    meta: {
      label: "Edit Service",
      permission: "service_edit",
      module: "service",
    },
  },

  // Routes for the doctor module...
  {
    path: "/doctor",
    name: "doctor",
    component: DoctorList,
    meta: { label: "Doctors", permission: "doctor_list", module: "doctor" },
  },
  {
    path: "/doctor/create",
    name: "doctor.create",
    component: DoctorCreate,
    meta: { label: "Add Doctor", permission: "doctor_add", module: "doctor" },
  },
  {
    path: "/doctor/edit/:id",
    name: "doctor.edit",
    component: DoctorCreate,
    meta: { label: "Edit Doctor", permission: "doctor_edit", module: "doctor" },
  },
  {
    path: "/doctor/profile",
    name: "doctor.profile",
    component: DoctorProfile,
    meta: { label: "Profile", permission: "doctor_profile", module: "doctor" },
  },
  {
    path: "/doctor/zoom-configuration",
    name: "doctor.zoom-configuration",
    component: ZoomConfiguration,
    meta: {
      label: "Zoom configuration",
      permission: "doctor_profile",
      module: "doctor",
    },
  },

  // Routes for the Receptionist module...
  {
    path: "/receptionist",
    name: "receptionist",
    component: ReceptionistList,
    meta: {
      label: "Receptionists",
      permission: "receptionist_list",
      module: "receptionist",
      dynamicModule: "receptionist",
    },
  },
  {
    path: "/receptionist/create",
    name: "receptionist.create",
    component: ReceptionistCreate,
    meta: {
      label: "Add Receptionist",
      permission: "receptionist_add",
      module: "receptionist",
      dynamicModule: "receptionist",
    },
  },
  {
    path: "/receptionist/edit/:id",
    name: "receptionist.edit",
    component: ReceptionistCreate,
    meta: {
      label: "Edit Receptionist",
      permission: "receptionist_edit",
      module: "receptionist",
      dynamicModule: "receptionist",
    },
  },
  {
    path: "/receptionist/profile/",
    name: "receptionist.profile",
    component: ReceptionistProfile,
    meta: {
      label: "Profile",
      permission: "receptionist_profile",
      module: "receptionist",
      dynamicModule: "receptionist",
    },
  },

  // Routes for the patient module...
  {
    path: "/patient",
    name: "patient",
    component: PatientList,
    meta: { label: "Patients", permission: "patient_list" },
  },
  {
    path: "/patient/create",
    name: "patient.create",
    component: PatientCreate,
    meta: {
      label: "Add Patient",
      permission: "patient_add",
      module: "patient",
    },
  },
  {
    path: "/patient/edit/:id",
    name: "patient.edit",
    component: PatientCreate,
    meta: {
      label: "Edit Patient",
      permission: "patient_edit",
      module: "patient",
    },
  },
  {
    path: "/patient/profile",
    name: "patient.profile",
    component: PatientProfile,
    meta: {
      label: "Profile",
      permission: "patient_profile",
      module: "patient",
    },
  },
  {
    path: "/patient/profile-view/:id",
    name: "patient-profile-view",
    component: PatientProfileView,
    meta: { label: "Patients", permission: "patient_edit", module: "patient" },
  },

  // Routes for the clinic module...
  {
    path: "/clinic",
    name: "clinic",
    component: ClinicList,
    meta: { label: "Clinics", permission: "clinic_list", module: "clinic" },
  },
  {
    path: "/clinic/create",
    name: "clinic.create",
    component: ClinicCreate,
    meta: { label: "Add Clinic", permission: "clinic_add", module: "clinic" },
  },
  {
    path: "/clinic/edit/:id",
    name: "clinic.edit",
    component: ClinicCreate,
    meta: { label: "Edit Clinic", permission: "clinic_edit", module: "clinic" },
  },
  {
    path: "/clinic/profile",
    name: "clinic.profile",
    component: ClinicProfile,
    meta: { label: "Profile", permission: "clinic_edit", module: "clinic" },
  },

  // Routes for the Appointment module...
  {
    path: "/appointment-list",
    name: "appointment",
    component: AppointmentQueueList,
    meta: {
      label: "Appointments",
      permission: "appointment_list",
      module: "appointment",
    },
  },
  {
    path: "/appointment/create",
    name: "appointment.create",
    component: AppointmentCreate,
    meta: {
      label: "Add Appointments",
      permission: "appointment_add",
      module: "appointment",
    },
  },
  {
    path: "/appointment/edit/:id",
    name: "appointment.edit",
    component: AppointmentCreate,
    meta: {
      label: "Edit Appointments",
      permission: "appointment_edit",
      module: "appointment",
    },
  },

  // Routes for the Appointment module...
  {
    path: "/custom-field",
    name: "custom-field",
    component: CustomFieldList,
    meta: {
      label: "Custom Fields",
      permission: "custom_field_list",
      dynamicModule: "custom_fields",
      module: "custom_fields",
    },
  },
  {
    path: "/custom-field/create",
    name: "custom-field.create",
    component: CustomFieldCreate,
    meta: {
      label: "Add Custom Fields",
      permission: "custom_field_add",
      dynamicModule: "custom_fields",
      module: "custom_fields",
    },
  },
  {
    path: "/custom-field/edit/:id",
    name: "custom-field.edit",
    component: CustomFieldCreate,
    meta: {
      label: "Edit Custom Fields",
      permission: "custom_field_edit",
      dynamicModule: "custom_fields",
      module: "custom_fields",
    },
  },

  // Routes for the Patient encounter module...
  {
    path: "/patient-encounter/:patient_id",
    name: "patient-encounter",
    component: PatientEncounterList,
    meta: {
      label: "Encounters",
      permission: "patient_encounter_list",
      module: "patient",
    },
  },
  {
    path: "/encounter-list",
    name: "encounter-list",
    component: PatientEncounterList,
    meta: {
      label: "Encounters",
      permission: "patient_encounter_list",
      module: "patient_encounter",
    },
  },
  {
    path: "/encounter-template-list",
    name: "encounter-template",
    component: PatientEncounterList,
    meta: {
      label: "Encounters",
      permission: "encounters_template_list",
      module: "encounter_template",
    },
  },
  {
    path: "/encounters/:patient_id",
    name: "encounters",
    component: PatientEncounterList,
    meta: {
      label: "Encounters",
      permission: "patient_encounter_view",
      module: "patient",
    },
  },
  {
    path: "/patient-encounter/create/:patient_id",
    name: "patient-encounter.create",
    component: PatientEncounterCreate,
    meta: {
      label: "Add Encounters",
      permission: "patient_encounter_add",
      module: "patient",
    },
  },
  {
    path: "/patient-encounter/edit/:id",
    name: "patient-encounter.edit",
    component: PatientEncounterCreate,
    meta: {
      label: "Edit Encounters",
      permission: "patient_encounter_edit",
      module: "patient",
    },
  },
  {
    path: "/patient-encounter/dashboard/:encounter_id",
    name: "patient-encounter.dashboard",
    component: AppointmentDashboard,
    meta: {
      label: "Encounter Dashboard",
      permission: "patient_encounter_view",
      module: "patient_encounter_list",
    },
  },
  {
    path: "/clinical-dashboard/:patient_id",
    name: "patient-clinical.dashboard",
    component: ClinicalDashboard,
    meta: {
      label: "Patient Dashboard",
      permission: "patient_encounter_view",
      module: "patient_encounter_list",
    },
  },
  {
    path: "/appointment/new/:encounter_id",
    name: "appointment.new",
    component: NewAppointment,
    meta: {
      label: "Create Encounter Dashboard",
      permission: "patient_encounter_add",
      module: "patient_encounter_list",
    },
  },
  {
    path: "/appointment/prescription/:encounter_id",
    name: "appointment.prescription",
    component: PatientPrescription,
    meta: {
      label: "Create Patient Prescription",
      permission: "patient_encounter_add",
      module: "patient_encounter_list",
    },
  },
  {
    path: "/patient-encounter/body-chart/:encounter_id",
    name: "patient-encounter.body-chart",
    component: BodyChartIndex,
    meta: {
      label: "Encounter Body chart list",
      permission: "body_chart_list",
      module: "patient_encounter_list",
    },
  },

  {
    path: "/patient-encounter/body-chart/create/:encounter_id",
    name: "patient-encounter.body-create",
    component: BodyChartCreate,
    meta: {
      label: "Encounter Body chart create",
      permission: "body_chart_add",
      module: "patient_encounter_list",
    },
  },
  {
    path: "/patient-encounter/body-chart/edit/:body_chart_id",
    name: "patient-encounter.body-edit",
    component: BodyChartCreate,
    meta: {
      label: "Encounter Body chart edit",
      permission: "body_chart_edit",
      module: "patient_encounter",
    },
  },
  // Routes for the Terms & Condition module...
  {
    path: "/terms-condition/create",
    name: "terms-condition.create",
    component: TermsConditionCreate,
    meta: { label: "Terms & Condition" },
  },

  // Routes for the Clinic Schedule module...

  {
    path: "/common-settings/create",
    name: "common-settings.create",
    component: CommonSetting,
    meta: { label: "Configuration" },
  },

  // Routes for the Settings module...

  {
    path: "/settings",
    name: "settings.index",
    component: Settings,
    meta: {
      label: "General Settings",
      permission: "settings",
      module: "settings",
    },
    children: [
      {
        path: "/setting/general",
        name: "setting.general",
        component: ClinicProfile,
        meta: { module: "general_settings", permission: "settings" },
      },
      {
        path: "/setting/patient-setting",
        name: "setting.patient_setting",
        component: PatientSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/setting/widget-setting",
        name: "setting.widget_setting",
        component: WidgetSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/setting/comman-settings",
        name: "setting.comman_settings",
        component: CommanSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/setting/language-settings",
        name: "setting.language_settings",
        component: LanguageSetting,
        meta: { module: "language_settings", permission: "settings" },
      },
      {
        path: "/setting/google_calender",
        name: "setting.google_calender",
        component: GoogleCalender,
        meta: { module: "google_calender", permission: "settings" },
      },
      {
        path: "/setting/google_meet_config",
        name: "setting.google_meet_config",
        component: GoogleMeetConfig,
        meta: { module: "google_meet_config", permission: "settings" },
      },
      {
        path: "/setting/encounter_template_setting",
        name: "setting.encounter_template_setting",
        component: DoctorEncounterSetting,
        meta: { module: "encounter_template_setting", permission: "settings" },
      },
      {
        path: "/setting/session",
        name: "doctor.session.setting",
        component: DoctorSession,
        meta: { module: "doctor_session", permission: "settings" },
      },
      {
        path: "/setting/telemed",
        name: "doctor.telemed.setting",
        component: ZoomConfiguration,
        meta: { module: "telemed", permission: "settings" },
      },
      {
        path: "/clinic/schedule",
        name: "clinic.schedule",
        component: ClinicScheduleSettingList,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/subscription",
        name: "subscription",
        component: SubscriptionSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/configuration",
        name: "clinic.configuration",
        component: CommonSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/app-config",
        name: "app-config",
        component: AppConfig,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/email/template",
        name: "email.template",
        component: NotificationSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/sms/template",
        name: "sms.template",
        component: SMSTemplate,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/custom-notification",
        name: "custom_notification",
        component: CustomNotification,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/custom-form",
        name: "custom_form",
        component: CustomForm,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/body-chart-setting",
        name: "body_chart_setting",
        component: BodyChartSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/google/event/template",
        name: "google.event.template",
        component: GoogleEventTemplate,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/terms-condition",
        name: "terms.condition",
        component: TermsConditionCreate,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/static-data/list",
        name: "static.data",
        component: StaticDataList,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/custom-fields",
        name: "custom.field",
        component: CustomFieldList,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/payment",
        name: "payment.setting",
        component: Payment,
      },
      {
        path: "/appointment-setting",
        name: "setting.appointment-setting",
        component: RestrictAppointment,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/general-setting",
        name: "setting.general-setting",
        component: GeneralSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/setting/subscription",
        name: "setting.subscription",
        component: Subscription,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/googlemeet",
        name: "setting.googlemeet",
        component: GoogleMeet,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/zoom-telemed",
        name: "setting.telemed",
        component: ZoomOAuthConfig,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/permission-setting",
        name: "setting.permission-setting",
        component: PermissionSetting,
        meta: { module: "settings", permission: "settings" },
      },
      {
        path: "/dashboard-sidebar-setting",
        name: "setting.dashboard-sidebar-setting",
        component: DashboardSidebarSetting,
        meta: { module: "settings", permission: "settings" },
      },

      //Routes for webhooks module
      {
        path: "webhooks",
        name: "webhooks",
        component: WebhooksIndex,
        meta: { label: "Webhooks", module: "settings", permission: "settings" },
      },
      {
        path: "webhooks/create",
        name: "webhooks.create",
        component: WebhooksCreate,
        meta: {
          label: "Add webhooks",
          module: "settings",
          permission: "settings",
        },
      },
      {
        path: "webhooks/edit/:id",
        name: "webhooks.edit",
        component: WebhooksCreate,
        meta: {
          label: "Edit webhooks",
          module: "settings",
          permission: "settings",
        },
      },
      {
        path: "webhooks/log/:webhook_id",
        name: "webhooks.log",
        component: WebhooksLog,
        meta: {
          label: "Webhooks logs",
          module: "settings",
          permission: "settings",
        },
      },
      {
        path: "templates",
        name: "templates",
        component: TemplateManagerList,
        meta: {
          label: "Template Manager",
          module: "settings",
          permission: "settings",
        },
      },
    ],
  },

  {
    path: "/custom-form/create",
    name: "custom-form.create",
    component: CustomFormCreate,
    meta: { module: "custom-form", permission: "custom_form_add" },
  },
  {
    path: "/custom-form/edit/:id",
    name: "custom-form.edit",
    component: CustomFormCreate,
    meta: { module: "custom-form", permission: "custom_form_add" },
  },
  // Routes for doctor change password...
  {
    path: "/account-setting/change-password",
    name: "account-setting.password",
    component: ChangePassword,
    meta: { label: "Change Password", permission: "change_password" },
  },

  // Routes for the Clinic Schedule module...
  {
    path: "/clinic-schedule/",
    name: "clinic-schedule",
    component: ClinicScheduleSettingList,
    meta: { label: "Holidays", module: "clinic_schedule" },
  },
  {
    path: "/clinic-schedule/create",
    name: "clinic-schedule.create",
    component: ClinicScheduleSettingCreate,
    meta: { label: "Add Holiday", module: "clinic_schedule" },
  },
  {
    path: "/clinic-schedule/edit/:id",
    name: "clinic-schedule.edit",
    component: ClinicScheduleSettingCreate,
    meta: { label: "Edit Holiday", module: "clinic_schedule" },
  },

  // Router for Doctor Session module....
  {
    path: "/doctor-session",
    name: "doctor-session.create",
    component: DoctorSession,
    meta: { label: "Availablity", module: "doctor_session" },
  },
  
  // Routes for Chat module
  {
    path: "/chat",
    name: "chat",
    component: ChatDashboard,
    meta: { 
      label: "Chat", 
      permission: "dashboard", 
      module: "chat" 
    },
  },
  
  // Routes for Notifications module
  {
    path: "/notifications",
    name: "notifications",
    component: NotificationsIndex,
    meta: { 
      label: "Notifications", 
      permission: "dashboard", 
      module: "notifications" 
    },
  },
  
  // Routes for Dictation module
  {
    path: "/dictation",
    name: "dictation",
    component: DictationIndex,
    meta: { 
      label: "Dictation", 
      permission: "dashboard", 
      module: "dictation" 
    },
  },
  {
    path: "/doctor-session/:id",
    name: "doctor-session.detail",
    component: DoctorSession,
    meta: { label: "Availablity", module: "doctor_session" },
  },
  {
    path: "/notification/create",
    name: "notification.create",
    component: NotificationSetting,
    meta: { label: "Notification" },
  },
  {
    path: "/signaturerx-prescriptions",
    name: "signaturerx-prescriptions",
    component: SignatureRXList,
    meta: { 
      label: "SignatureRX Prescriptions", 
      permission: "prescription_list",
      module: "prescription" 
    },
  },

  // Routes for patient appointment
  {
    path: "/all-appointment",
    name: "all-appointment",
    component: AllAppointmentList,
    meta: { label: "Appointments" },
  },
  {
    path: "/all-appointment-list",
    name: "appointment-list.index",
    component: AppointmentDataList,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient-appointment-list/:patient_id",
    name: "patient-appointment-list",
    component: AppointmentDataList,
    meta: { label: "Appointments", module: "appointment_list" },
  },

  // Patient login routes
  {
    path: "/patient/appointment-list",
    name: "patient.appointment-list",
    component: PatientAppointmentList,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient/encounter-list",
    name: "patient.encounter-list",
    component: PatientConsultationList,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient/doctors-list",
    name: "patient.doctors-list",
    component: PatientDoctors,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient/lab-test",
    name: "patient.lab-test",
    component: PatientLabTest,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient/invoices",
    name: "patient.invoices",
    component: PatientInvoices,
    meta: { label: "Appointments", module: "appointment_list" },
  },
  {
    path: "/patient/medications",
    name: "patient.medications",
    component: PatientMedications,
    meta: { label: "Appointments", module: "appointment_list" },
  },

  // patient_id
  {
    path: "/patient-medical-report_id/:patient_id",
    name: "patient-medical-report_id",
    component: PatientMedicalReport,
    meta: { label: "Patient Medical Report", module: "patient" },
  },
  {
    path: "/patient-medical-report/:encounter_id",
    name: "patient-medical-report",
    component: PatientMedicalReport,
    meta: { label: "Patient Medical Report" },
  },

  {
    path: "/billings",
    name: "billings",
    component: BillingRecord,
    meta: { label: "Billing Records" },
  },

  {
    path: "/clinic-revenue-report",
    name: "clinic-revenue-reports",
    component: RevenuReport,
    meta: { label: "Revenue Reports" },
  },

  {
    path: "/help",
    name: "get_help",
    component: GetHelp,
    meta: { label: "Get help" },
  },
  {
    path: "/pro",
    name: "get_pro",
    component: GetPro,
    meta: { label: "Get pro" },
  },

  //patient clinic change
  {
    path: "/patient-clinic",
    name: "patient-clinic",
    component: PatientClinic,
    meta: { label: "Patient Clinic" },
  },

  // Routes for the tax module...
  {
    path: "/tax",
    name: "tax",
    component: TaxList,
    meta: { label: "Taxes", module: "tax" },
  },
  {
    path: "/tax/create",
    name: "tax.create",
    component: TaxCreate,
    meta: { label: "Add Tax", module: "tax" },
  },
  {
    path: "/tax/edit/:id",
    name: "tax.edit",
    component: TaxCreate,
    meta: { label: "Edit Tax", module: "tax" },
  },

  // Routes for the Contacts Directory module
  {
    path: "/contacts-directory",
    name: "contacts-directory",
    component: ContactList,
    meta: { 
      label: "Contacts", 
      permission: "dashboard", 
      module: "contacts_directory"
    },
  },

  // Route for Activity Logs
  {
    path: "/activity-logs",
    name: "activity-logs",
    component: ActivityLogs,
    meta: { 
      label: "Activity Logs", 
      permission: "dashboard", 
      module: "activity_logs"
    },
  },
  
  // Routes for the Task Manager
  {
    path: "/tasks",
    name: "tasks",
    component: TaskDashboard,
    meta: {
      label: "Task Manager",
      permission: "dashboard", // Temporarily using dashboard permission which everyone has
      module: "task_manager"
    },
  },

  // Routes for Category Management (Admin Only)
  {
    path: "/categories",
    name: "categories",
    component: CategoryList,
    meta: {
      label: "Categories",
      permission: "category_list",
      module: "categories",
      adminOnly: true
    },
  },

  {
    path: "/clinic/payments-setting",
    name: "clinic.payment-setting",
    component: Payment,
    meta: { label: "Edit Tax", module: "tax" },
  },

  // Routes for TDL Labs module
  {
    path: "/tdl-settings",
    name: "tdl-settings",
    component: TDLSettings,
    meta: { 
      label: "TDL Labs Settings", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-tests",
    name: "tdl-tests",
    component: TDLTests,
    meta: { 
      label: "TDL Test Catalog", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-requests",
    name: "tdl-requests",
    component: TDLRequests,
    meta: { 
      label: "TDL Test Requests", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-request/:id",
    name: "tdl-request-details",
    component: TDLRequestDetails,
    meta: { 
      label: "TDL Test Request Details", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-new-request",
    name: "tdl-new-request",
    component: TDLNewRequest,
    meta: { 
      label: "New TDL Test Request", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-results",
    name: "tdl-results",
    component: TDLResults,
    meta: { 
      label: "TDL Test Results", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-result/:id",
    name: "tdl-result-details",
    component: TDLResultDetails,
    meta: { 
      label: "TDL Test Result Details", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
  {
    path: "/tdl-import-results",
    name: "tdl-import-results",
    component: TDLImportResults,
    meta: { 
      label: "Import TDL Test Results", 
      permission: "clinic_edit", 
      module: "tdl_labs"
    },
  },
];

const router = new VueRouter({
  // base: 'kivicare/wp-admin/admin.php?page=dashboard/',
  // mode: 'history',
  routes,
});

let loader = null;

router.beforeEach((to, from, next) => {
  let user = store.state.userDataModule.user;

  // if (window.innerWidth < 1500) {
  //     store.commit('TOGGLE_SIDEBAR', false)
  // }
  // else
  // {
  store.commit("TOGGLE_SIDEBAR", true);
  // }
  if (user.ID === undefined) {
    store.dispatch("userDataModule/fetchUserData").then((r) => {
      const user = store.state.userDataModule.user;
      // store.dispatch("fetchAllClinic", {self: this})
      if (
        user !== undefined &&
        user.addOns.kiviPro !== undefined &&
        user.addOns.kiviPro == true
      ) {
        store.dispatch("fetchAllClinic", { self: this });
      } else {
        store.dispatch("fetchAllClinic", { self: this });
      }
      routerValidation(to, user, next).then(() =>
        store.commit("TOGGLE_LOADER", false)
      );
    });
  } else {
    routerValidation(to, user, next).then(() =>
      store.commit("TOGGLE_LOADER", false)
    );
  }
});
router.afterEach((to, from) => {
  setTimeout(() => {
    // loader.hide()
  }, 1300);
});
const routerValidation = async (to, user, next) => {
  if (to.meta.setup !== undefined && to.meta.setup) {
    const step_config = user.step_config.filter((step) => {
      if (to.name === step.routeName && !step.completed) {
        return true;
      } else {
        return false;
      }
    });

    if (step_config.length > 0) {
      const routeObj = user.step_config.filter((step) => {
        return !step.completed;
      });
      if (routeObj.length > 0) {
        if (routeObj[0].routeName !== to.name) {
          next({ name: routeObj[0].routeName });
        } else {
          next();
        }
      } else {
        next();
      }
    } else {
      const step_config = user.step_config.filter((step) => {
        if (!step.completed) {
          return true;
        } else {
          return false;
        }
      });
      if (step_config.length > 0) {
        next();
      } else {
        next({ name: "dashboard" });
      }
    }
  } else if (to.meta.dynamicModule !== undefined) {
    const module = store.state.userDataModule.user.module.module_config.filter(
      (thing) => thing.name === to.meta.dynamicModule && thing.status === "1"
    );

    if (module !== undefined && module.length > 0) {
      next();
    } else {
      next({ name: "dashboard" });
    }
  } else {
    if (user.step_config !== undefined) {
      const step_config = user.step_config.filter((step) => {
        return !step.completed;
      });

      if (step_config.length > 0) {
        next({ name: step_config[0].routeName });
      } else {
        // Check for admin-only routes
        if (to.meta.adminOnly !== undefined && to.meta.adminOnly === true) {
          const isAdmin = user.roles && user.roles.includes('administrator');
          if (!isAdmin) {
            next({ name: "403" });
            return;
          }
        }

        if (to.meta.permission !== undefined) {
          let permission = false;

          if (user.permissions !== undefined) {
            let permission_name = window.pluginPREFIX + to.meta.permission;

            if (
              user.permissions[permission_name] !== undefined &&
              (user.permissions[permission_name] === true ||
                user.permissions[permission_name] === 1)
            ) {
              permission = true;
            }
          } else {
            if (JSON.stringify(user) === "{}") {
              permission = true;
            }
          }
          if (permission === false) {
            next({ name: "403" });
          } else {
            next();
          }
        } else {
          next();
        }
      }
    }
  }
};

export default router;