<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-screen overflow-y-auto">
      <!-- Header -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">
          {{ isEdit ? 'Edit Category' : 'Add Category' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 transition-colors">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="saveCategory" class="p-6 space-y-4">
        <!-- Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Name <span class="text-red-500">*</span>
          </label>
          <input
            v-model="form.name"
            type="text"
            required
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="Enter category name"
          />
        </div>

        <!-- Description -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea
            v-model="form.description"
            rows="3"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="Enter category description (optional)"
          ></textarea>
        </div>

        <!-- Module Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Module Type <span class="text-red-500">*</span>
          </label>
          <select
            v-model="form.module_type"
            required
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">Select module type</option>
            <option value="service">Service</option>
            <option value="product">Product</option>
            <option value="other">Other</option>
          </select>
        </div>

        <!-- Parent Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Parent Category</label>
          <select
            v-model="form.parent_id"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">None (Top Level)</option>
            <option
              v-for="parentCategory in availableParents"
              :key="parentCategory.id"
              :value="parentCategory.id"
            >
              {{ parentCategory.name }}
            </option>
          </select>
        </div>

        <!-- Visibility -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Visibility <span class="text-red-500">*</span>
          </label>
          <select
            v-model="form.visibility"
            required
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="public">Public - Shows in frontend and backend</option>
            <option value="backend_only">Backend Only - Shows only in admin area</option>
            <option value="disabled">Disabled - Hidden everywhere</option>
          </select>
        </div>

        <!-- Sort Order -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
          <input
            v-model.number="form.sort_order"
            type="number"
            min="0"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            placeholder="0"
          />
          <p class="text-sm text-gray-500 mt-1">Lower numbers appear first</p>
        </div>

        <!-- Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <select
            v-model="form.status"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option :value="1">Active</option>
            <option :value="0">Inactive</option>
          </select>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors disabled:opacity-50"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
            {{ isEdit ? 'Update' : 'Create' }} Category
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { get, post } from "../../config/request";

export default {
  name: "CategoryForm",
  props: {
    category: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      availableParents: [],
      form: {
        name: '',
        description: '',
        module_type: 'service',
        parent_id: '',
        visibility: 'public',
        sort_order: 0,
        status: 1
      }
    };
  },
  mounted() {
    this.initForm();
    this.getAvailableParents();
  },
  methods: {
    initForm() {
      if (this.isEdit && this.category) {
        this.form = {
          id: this.category.id,
          name: this.category.name || '',
          description: this.category.description || '',
          module_type: this.category.module_type || 'service',
          parent_id: this.category.parent_id || '',
          visibility: this.category.visibility || 'public',
          sort_order: this.category.sort_order || 0,
          status: this.category.status || 1
        };
      }
    },

    getAvailableParents() {
      const params = {
        module_type: this.form.module_type
      };

      get('category_list', params)
        .then(response => {
          if (response.data.status) {
            // Filter out current category and its children to prevent circular references
            this.availableParents = response.data.data.filter(cat => {
              if (this.isEdit && this.category) {
                return cat.id !== this.category.id && cat.parent_id !== this.category.id;
              }
              return true;
            });
          }
        })
        .catch(error => {
          console.error('Error fetching parent categories:', error);
        });
    },

    saveCategory() {
      this.loading = true;

      const formData = { ...this.form };
      
      // Convert empty parent_id to null
      if (!formData.parent_id) {
        formData.parent_id = null;
      }

      post('category_save', formData)
        .then(response => {
          this.loading = false;
          if (response.data.status) {
            this.$swal('Success!', response.data.message, 'success');
            this.$emit('saved');
          } else {
            this.$swal('Error!', response.data.message, 'error');
          }
        })
        .catch(error => {
          this.loading = false;
          console.error('Error saving category:', error);
          this.$swal('Error!', 'Failed to save category', 'error');
        });
    }
  },
  watch: {
    'form.module_type'() {
      this.getAvailableParents();
    }
  }
};
</script>

<style scoped>
/* Modal styles are handled by Tailwind classes */
</style>