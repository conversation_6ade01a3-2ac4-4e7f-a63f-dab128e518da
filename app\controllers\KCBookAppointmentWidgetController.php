<?php

namespace App\controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCAppointment;
use App\models\KCBillItem;
use App\models\KCClinicSession;
use App\models\KCClinic;
use App\models\KCPatientEncounter;
use App\models\KCAppointmentServiceMapping;
use App\controllers\KCPaymentController;
use App\models\KCService;
use DateTime;
use Exception;

class KCBookAppointmentWidgetController extends KCBase
{

    public $db;

    private $request;

    public function __construct()
    {

        global $wpdb;

        $this->db = $wpdb;

        $this->request = new KCRequest();

        parent::__construct();
    }

    public function getDoctors()
    {
        $request_data = $this->request->getInputs();
        $doctor_role = $this->getDoctorRole();
        $table_name = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $prefix = '';
        $postfix = '';

        $request_data['clinic_id'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['clinic_id']), true));

        if (!empty($request_data['clinic_id']['id'])) {

            $request_data['clinic_id']['id'] = (int) $request_data['clinic_id']['id'];
            $doctor_condition = ' ';
            if (!empty($request_data['props_doctor_id']) && !in_array($request_data['props_doctor_id'], [0, '0'])) {
                if (strpos($request_data['props_doctor_id'], ',') !== false) {
                    $request_data['props_doctor_id'] = implode(',', array_map('absint', explode(',', sanitize_text_field($request_data['props_doctor_id']))));
                    if (isKiviCareProActive()) {
                        $doctor_condition = ' AND doctor_id IN (' . $request_data['props_doctor_id'] . ')';
                    } else {
                        $doctor_condition = ' WHERE ID IN (' . $request_data['props_doctor_id'] . ')';
                    }
                } else {
                    if (isKiviCareProActive()) {
                        $doctor_condition = ' AND doctor_id =' . (int) $request_data['props_doctor_id'];
                    } else {
                        $doctor_condition = ' WHERE ID =' . (int) $request_data['props_doctor_id'];
                    }
                }
            }
            if (!empty($request_data['props_clinic_id']) && !in_array($request_data['props_clinic_id'], [0, '0'])) {
                $request_data['clinic_id']['id'] = (int) $request_data['props_clinic_id'];
            }

            if (isKiviCareProActive()) {
                $query = "SELECT `doctor_id` FROM {$table_name} WHERE `clinic_id` =" . $request_data['clinic_id']['id'] . $doctor_condition;
            } else {
                $query = "SELECT `ID` FROM {$this->db->base_prefix}users {$doctor_condition}";
            }

            if (isKiviCareProActive()) {
                $result = collect($this->db->get_results($query))->unique('doctor_id')->pluck('doctor_id');
            } else {
                $result = collect($this->db->get_results($query))->unique('ID')->pluck('ID');
            }

            $users = get_users(['role' => $doctor_role, 'user_status' => '0']);
            $users = collect($users)->whereIn('ID', $result)->values();
            $prefix_postfix = $this->db->get_var('select extra from ' . $this->db->prefix . 'kc_clinics where id=' . $request_data['clinic_id']['id']);
            if ($prefix_postfix != null) {
                $prefix_postfix = json_decode($prefix_postfix);
                $prefix = isset($prefix_postfix->currency_prefix) ? $prefix_postfix->currency_prefix : '';
                $postfix = isset($prefix_postfix->currency_postfix) ? $prefix_postfix->currency_postfix : '';
            }
        }
        $results = [];
        if (!empty($users) && count($users) > 0) {
            foreach ($users as $key => $user) {
                $image_attachment_id = get_user_meta($user->ID, 'doctor_profile_image', true);
                $user_image_url = wp_get_attachment_url($image_attachment_id);
                $results[$key]['id'] = $user->ID;
                $results[$key]['display_name'] = $user->data->display_name;
                $user_data = get_user_meta($user->ID, 'basic_data', true);
                if ($user_data) {
                    $user_data = json_decode($user_data);
                    $results[$key]['description'] = get_user_meta($user->ID, 'doctor_description', true);
                    $results[$key]['address'] = isset($user_data->address) ? $user_data->address : "";
                    $results[$key]['city'] = isset($user_data->city) ? $user_data->city : "";
                    $results[$key]['state'] = isset($user_data->state) ? $user_data->state : "";
                    $results[$key]['country'] = isset($user_data->country) ? $user_data->country : "";
                    $results[$key]['currency'] = '';
                    $results[$key]['postal_code'] = isset($user_data->postal_code) ? $user_data->postal_code : "";
                    $results[$key]['timeSlot'] = isset($user_data->time_slot) ? $user_data->time_slot : "";
                    $results[$key]['price'] = isset($user_data->price) ? $user_data->price : "";
                    $results[$key]['gender'] = isset($user_data->gender) ? $user_data->gender : "";
                    $results[$key]['qualifications'] = isset($user_data->qualifications) ? $user_data->qualifications : "";
                    $results[$key]['specialties'] = isset($user_data->specialties) ? $user_data->specialties : [];
                    $results[$key]['enableTeleMed'] = false;
                    $results[$key]['custom_fields'] = kcGetCustomFields('appointment_module', $user->ID);
                    $results[$key]['user_profile'] = $user_image_url;
                    $results[$key]['contact_no'] = !empty($user_data->mobile_number) ? $user_data->mobile_number : '';
                    $results[$key]['dob'] = !empty($user_data->dob) ? $user_data->dob : '';
                    $results[$key]['specialties_all'] = !empty($user_data->specialties) && is_array($user_data->specialties) ? collect($user_data->specialties)->pluck('label')->implode(',') : [];
                    $results[$key]['full_address'] = (!empty($user_data->address) ? $user_data->address : "") . ',' . (!empty($user_data->city) ? $user_data->city : '') . ',' .
                        (!empty($user_data->state) ? $user_data->state : "") . ',' . (!empty($user_data->country) ? $user_data->country : "");
                    $results[$key]['enableTeleMed'] = kcDoctorTelemedServiceEnable($user->ID);
                }
            }
            wp_send_json([
                'status' => true,
                'message' => __('Doctor details', 'kc-lang'),
                'data' => $results,
                'prefix' => $prefix,
                'postfix' => $postfix,
            ]);
        } else {
            wp_send_json([
                'status' => false,
                'message' => __('Doctor details', 'kc-lang'),
                'data' => []
            ]);
        }



    }
    public function getClinic()
    {
        $response = apply_filters('kcpro_get_clinic_data', ['data' => $this->request->getInputs()]);
        wp_send_json($response);
    }

    public function getTimeSlots()
    {
        $formData = $this->request->getInputs();

        // Handle timezone if provided
        $timezone = isset($formData['timezone']) ? $formData['timezone'] : 'UTC';
        date_default_timezone_set($timezone);

        // Log the date and timezone for debugging
        error_log('Appointment date: ' . $formData['date'] . ' Timezone: ' . $timezone);

        $timeSlots = kvGetTimeSlots([
            'date' => $formData['date'],
            'doctor_id' => $formData['doctor_id'],
            'clinic_id' => $formData['clinic_id'],
            'service' => $formData['service'],
            'timezone' => $timezone
        ], "", true);
        $htmldata = '';
        if (count($timeSlots)) {
            $status = true;
            $message = __('Time slots', 'kc-lang');
            if (!empty($formData['widgetType']) && $formData['widgetType'] === 'phpWidget') {
                ob_start();
                foreach ($timeSlots as $sessions) {
                    foreach ($sessions as $time_slot) {
                        ?>
                        <div class="iq-client-widget iq-time-slot time-container p-2 text-sm rounded-lg transition-colors bg-gray-50 hover:bg-violet-50 text-gray-700 cursor-pointer h-10 text-center"
                            data-time="<?php echo esc_attr($time_slot['time']); ?>"
                            onclick="selectTimeSlot('<?php echo esc_js($time_slot['time']); ?>')">
                            <input type="radio" class="card-checkbox selected-time hidden" name="card_main"
                                id="time_slot_<?php echo esc_html($time_slot['time']); ?>" value="<?php echo esc_html($time_slot['time']); ?>">

                            <?php echo esc_html($time_slot['time']); ?>
                        </div>
                        <?php
                    }
                }
                $htmldata = ob_get_clean();
            }
        } else {
            $status = false;
            $message = __('Doctor is not available for this date', 'kc-lang');
        }

        wp_send_json([
            'status' => $status,
            'message' => $message,
            'data' => $timeSlots,
            'html' => $htmldata
        ]);

    }

    public function saveAppointment()
    {

        if (!is_user_logged_in()) {
            wp_send_json([
                "status" => false,
                "message" => __('Sign in to book appointment', 'kc-lang')
            ]);
        }
        if ($this->getPatientRole() !== $this->getLoginUserRole()) {
            wp_send_json([
                "status" => false,
                "message" => __('User must be patient to book appointment', 'kc-lang')
            ]);
        }

        global $wpdb;

        $formData = $this->request->getInputs();
        $proPluginActive = isKiviCareProActive();
        $telemedZoomPluginActive = isKiviCareTelemedActive();
        $telemedGooglemeetPluginActive = isKiviCareGoogleMeetActive();

        try {
            //check if service is single or multiple, if single create array
            if (empty(array_filter($formData['visit_type'], 'is_array'))) {
                $formData['visit_type'] = [$formData['visit_type']];
            }
            ;
            $clinic_id = (int) (isset($formData['clinic_id']['id']) ? $formData['clinic_id']['id'] : kcGetDefaultClinicId());
            $formData['doctor_id']['id'] = (int) $formData['doctor_id']['id'];
            $appointment_day = strtolower(date('l', strtotime($formData['appointment_start_date'])));
            $day_short = substr($appointment_day, 0, 3);

            $doctor_time_slot = $wpdb->get_var("SELECT time_slot FROM {$wpdb->prefix}kc_clinic_sessions
				WHERE `doctor_id` = {$formData['doctor_id']['id']} AND `clinic_id` ={$clinic_id}
				AND ( `day` = '{$day_short}' OR `day` = '{$appointment_day}') ");

            $time_slot = !empty($doctor_time_slot) ? $doctor_time_slot : 15;

            $end_time = strtotime("+" . $time_slot . " minutes", strtotime($formData['appointment_start_time']));
            $appointment_end_time = date('H:i:s', $end_time);
            $appointment_date = date('Y-m-d', strtotime($formData['appointment_start_date']));
            $appointment_start_time = date('H:i:s', strtotime($formData['appointment_start_time']));
            if (isKiviCareProActive()) {
                $verifyTimeslot = apply_filters('kcpro_verify_appointment_timeslot', $formData);
                if (is_array($verifyTimeslot) && array_key_exists('end_time', $verifyTimeslot) && !empty($verifyTimeslot['end_time'])) {
                    if (empty($verifyTimeslot['status'])) {
                        wp_send_json($verifyTimeslot);
                    }
                    $appointment_end_time = date('H:i:s', $verifyTimeslot['end_time']);
                }
            }
            // appointment data
            $tempAppointmentData = [
                'appointment_start_date' => $appointment_date,
                'appointment_start_time' => $appointment_start_time,
                'appointment_end_date' => $appointment_date,
                'appointment_end_time' => $appointment_end_time,
                'visit_type' => $formData['visit_type'],
                'clinic_id' => $clinic_id,
                'doctor_id' => $formData['doctor_id']['id'],
                'patient_id' => get_current_user_id(),
                'description' => $formData['description'],
                'status' => $formData['status'],
                'created_at' => current_time('Y-m-d H:i:s')
            ];

            if (isset($formData['file']) && is_array($formData['file']) && count($formData['file']) > 0) {
                kcUpdateFields($wpdb->prefix . 'kc_appointments', ['appointment_report' => 'longtext NULL']);
                $tempAppointmentData['appointment_report'] = json_encode($formData['file']);
            }

            $patient_appointment_id = (new KCAppointment())->insert($tempAppointmentData);

            if ($patient_appointment_id) {
                $formData['id'] = $patient_appointment_id;
                if (isset($formData['custom_fields']) && $formData['custom_fields'] !== []) {
                    kvSaveCustomFields('appointment_module', $patient_appointment_id, $formData['custom_fields']);
                }
                if (!empty($formData['tax'])) {
                    apply_filters('kivicare_save_tax_data', [
                        'type' => 'appointment',
                        'id' => $patient_appointment_id,
                        'tax_data' => $formData['tax']
                    ]);
                }
                $message = __('Appointment has been booked successfully', 'kc-lang');
                $status = true;
            } else {
                $message = __('Appointment booking failed.', 'kc-lang');
                $status = false;
            }

            $doctorTelemedType = kcCheckDoctorTelemedType($patient_appointment_id);
            $notification = '';
            $telemed_service_include = false;
            $all_appointment_service_name = [];
            if (gettype($formData['visit_type']) === 'array') {
                $telemed_service_in_appointment_service = collect($formData['visit_type'])->map(function ($v) use ($formData, $clinic_id) {
                    $temp_service_id = (int) $v['service_id'];
                    // After migration: try mapping_id first, then direct ID
                    $service = $this->db->get_row("SELECT telemed_service FROM {$this->db->prefix}kc_services WHERE mapping_id = {$temp_service_id} AND clinic_id = {$clinic_id} AND doctor_id = " . (int) $formData['doctor_id']['id']);
                    if (!$service) {
                        $service = $this->db->get_row("SELECT telemed_service FROM {$this->db->prefix}kc_services WHERE id = {$temp_service_id} AND clinic_id = {$clinic_id} AND doctor_id = " . (int) $formData['doctor_id']['id']);
                    }
                    return $service ? $service->telemed_service : 'no';
                })->toArray();
                foreach ($formData['visit_type'] as $key => $value) {
                    $service = strtolower($value['name']);
                    $all_appointment_service_name[] = $service;
                    if (in_array('yes', $telemed_service_in_appointment_service)) {
                        if ($telemedZoomPluginActive || $telemedGooglemeetPluginActive) {
                            $formData['appointment_id'] = $patient_appointment_id;
                            $formData['time_slot'] = $time_slot;
                            if ($doctorTelemedType == 'googlemeet') {
                                // $telemed_res_data = apply_filters('kcgm_save_appointment_event',['appoinment_id' => $patient_appointment_id,'service' => kcServiceListFromRequestData($formData)]);
                                $telemed_res_data = ['status' => true];
                            } else {
                                $telemed_res_data = apply_filters('kct_create_appointment_meeting', $formData);
                            }
                            if (empty($telemed_res_data['status'])) {
                                (new KCAppointmentServiceMapping())->delete(['appointment_id' => (int) $patient_appointment_id]);
                                (new KCAppointment())->delete(['id' => (int) $patient_appointment_id]);
                                do_action('kc_appointment_cancel', $patient_appointment_id);
                                wp_send_json([
                                    'status' => false,
                                    'message' => __('Failed to generate Video Meeting.', 'kc-lang'),
                                ]);
                            }
                            // send zoom link
                            $telemed_service_include = true;
                        }
                    }

                    if ($patient_appointment_id) {
                        // Find the correct service ID after migration
                        $old_service_id = (int) $value['service_id'];
                        $new_service_id = $old_service_id;
                        
                        // First try to find by mapping_id (for backward compatibility with old IDs)
                        $service = $this->db->get_row("SELECT id FROM {$this->db->prefix}kc_services WHERE mapping_id = {$old_service_id} AND clinic_id = {$clinic_id} AND doctor_id = {$doctor_id}");
                        if ($service) {
                            $new_service_id = $service->id;
                        } else {
                            // If not found by mapping_id, assume it's already a new service ID
                            $service_exists = $this->db->get_var("SELECT id FROM {$this->db->prefix}kc_services WHERE id = {$old_service_id} AND clinic_id = {$clinic_id} AND doctor_id = {$doctor_id}");
                            if (!$service_exists) {
                                continue; // Skip if service not found
                            }
                        }
                        
                        (new KCAppointmentServiceMapping())->insert([
                            'appointment_id' => (int) $patient_appointment_id,
                            'service_id' => (int) $new_service_id,
                            'created_at' => current_time('Y-m-d H:i:s'),
                            'status' => 1
                        ]);
                    }
                }
            }

            if (in_array((string) $formData['status'], ['2', '4'])) {
                KCPatientEncounter::createEncounter($patient_appointment_id);
                KCBillItem::createAppointmentBillItem($patient_appointment_id);
            }

            if (!empty($patient_appointment_id) && $patient_appointment_id !== 0) {
                // hook for appointment booked
                do_action('kc_appointment_book', $patient_appointment_id);
            }
            $formData['calender_content'] = '';
            if ($proPluginActive && $formData['status'] == '1') {

                $clinic_data = $this->db->get_row("SELECT name, CONCAT(address, ', ',city,', '
		           ,postal_code,', ',country) AS clinic_full_address FROM {$this->db->prefix}kc_clinics WHERE id={$clinic_id}");

                $appointment_data = [
                    "clinic_name" => !empty($clinic_data->name) ? $clinic_data->name : '',
                    "clinic_address" => !empty($clinic_data->clinic_full_address) ? $clinic_data->clinic_full_address : '',
                    "id" => $patient_appointment_id,
                    "start_date" => $appointment_date,
                    "start_time" => $appointment_start_time,
                    "end_date" => $appointment_date,
                    "end_time" => $appointment_end_time,
                    "appointment_service" => implode(",", $all_appointment_service_name),
                    "extra" => $formData
                ];

                $formData['calender_content'] = kcAddToCalendarContent($appointment_data);
            }
            switch ($formData['payment_mode']) {
                case 'paymentWoocommerce':
                    $woocommerce_response = kcWoocommerceRedirect($patient_appointment_id, $formData);
                    if (isset($woocommerce_response['status']) && $woocommerce_response['status']) {
                        if (!empty($woocommerce_response['woocommerce_cart_data'])) {
                            wp_send_json($woocommerce_response);
                        }
                    }
                    break;
                case 'paymentPaypal':
                    $this->db->update($this->db->prefix . "kc_appointments", ['status' => 0], ['id' => $patient_appointment_id]);
                    $paypal_response = (new KCPaymentController())->makePaypalPayment($formData, $patient_appointment_id);
                    if (empty($paypal_response['status'])) {
                        (new KCAppointment())->loopAndDelete(['id' => $patient_appointment_id], true);
                    }
                    $paypal_response['appointment_id'] = $patient_appointment_id;
                    $paypal_response['data'] = $formData;
                    wp_send_json($paypal_response);
                    break;
                case 'paymentStripepay':
                    $this->db->update($this->db->prefix . "kc_appointments", ['status' => 0], ['id' => $patient_appointment_id]);

                    $formData['visit_type'] =collect($formData['visit_type'])->map(function($item){
                        $item['name']= ( new KCService )->get_var( [ 'id' => (int)$item['service_id']], 'name', true );

                        return $item;
                    })->toArray();

                    $stripepay_response = apply_filters('kivicare_create_stripepay_order', [], $formData, $patient_appointment_id);
                    if (empty($stripepay_response['status'])) {
                        (new KCAppointment())->loopAndDelete(['id' => $patient_appointment_id], true);
                    }
                    $stripepay_response['appointment_id'] = $patient_appointment_id;
                    $stripepay_response['data'] = $formData;
                    wp_send_json($stripepay_response);
                    break;
                case 'paymentRazorpay':
                    $this->db->update($this->db->prefix . "kc_appointments", ['status' => 0], ['id' => $patient_appointment_id]);
                    $formData['appointment_id'] = $patient_appointment_id;
                    $formData['page'] = 'dashboard';
                    $razorpay_response = apply_filters('kivicare_create_razorpay_order', $formData);
                    if (is_array($razorpay_response) && array_key_exists('checkout_detail', $razorpay_response) && !empty($razorpay_response['status'])) {
                        $razorpay_response['appointment_id'] = $patient_appointment_id;
                        $razorpay_response['data'] = $formData;
                        wp_send_json($razorpay_response);
                    } else {
                        (new KCAppointment())->loopAndDelete(['id' => $patient_appointment_id], true);
                        wp_send_json([
                            'status' => false,
                            'message' => esc_html__('Failed to create razorpay payment link', 'kc-lang'),
                            'error_message' => is_array($razorpay_response) && !empty($razorpay_response['message']) ? $razorpay_response['message'] : ''
                        ]);
                    }
                    break;
                case 'paymentOffline':
                    $service_name = kcServiceListFromRequestData($formData);
                    if ($proPluginActive || $telemedZoomPluginActive || $telemedGooglemeetPluginActive) {
                        $notification = kcProAllNotification($patient_appointment_id, $service_name, $telemed_service_include);
                    } else {
                        $notification = kivicareCommonSendEmailIfOnlyLitePluginActive($patient_appointment_id, $service_name);
                    }
                    break;
            }

            wp_send_json([
                'status' => $status,
                'message' => $message,
                'data' => $formData,
                'notification' => $notification,
            ]);

        } catch (Exception $e) {

            $code = $e->getCode();
            $message = $e->getMessage();

            header("Status: $code $message");

            wp_send_json([
                'status' => false,
                'message' => $message
            ]);
        }

    }

    public function getClinicSelectedArray()
    {
        $request_data = $this->request->getInputs();
        $table_name = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $preselected_doctor = '';
        if (!empty($request_data['preselected_doctor'])) {
            $preselected_doctor = implode(',', array_filter(array_map('absint', explode(',', $request_data['preselected_doctor']))));
        }
        if (!empty($request_data['service_id'])) {
            $request_data['service_id'] = implode(",", array_map('absint', $request_data['service_id']));
            $doctor_preselect_condition = ' ';
            if (!empty($preselected_doctor)) {
                $doctor_preselect_condition = " AND doctor_id IN ($preselected_doctor) ";
            }
            // After migration: handle both old mapping IDs and new service IDs
            $service_doctor = collect($this->db->get_results("SELECT doctor_id FROM {$this->db->prefix}kc_services WHERE (mapping_id IN ({$request_data['service_id']}) OR id IN ({$request_data['service_id']})) {$doctor_preselect_condition}"))->pluck('doctor_id')->toArray();
            if (!empty($service_doctor) && !empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
                $service_doctor = implode(",", $service_doctor);
                $result = collect($this->db->get_results("SELECT doctor_id FROM {$table_name} WHERE doctor_id IN ({$service_doctor}) AND clinic_id = {$request_data['clinic_id']}"))->unique('doctor_id')->pluck('doctor_id')->toArray();
            } else {
                $result = $service_doctor;
            }
            $all_service_name = collect($this->db->get_results("SELECT telemed_service from {$this->db->prefix}kc_services WHERE (mapping_id IN ({$request_data['service_id']}) OR id IN ({$request_data['service_id']})) "))->pluck('telemed_service')->toArray();
            if (in_array('yes', $all_service_name)) {
                $result = array_filter($result, function ($v) {
                    return kcDoctorTelemedServiceEnable($v);
                });
            }
        } else {
            $doctor_preselect_condition = ' ';
            if (!empty($preselected_doctor)) {
                $doctor_preselect_condition = " AND doctor_id IN ($preselected_doctor) ";
            }
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $clinic_id = (int) $request_data['clinic_id'];
                $query = "SELECT `doctor_id` FROM {$table_name} WHERE `clinic_id` = {$clinic_id} {$doctor_preselect_condition}";
            } else {
                $query = "SELECT `doctor_id` FROM {$table_name} WHERE 0=0 {$doctor_preselect_condition}";
            }
            $result = collect($this->db->get_results($query))->unique('doctor_id')->pluck('doctor_id')->toArray();
        }
        $defaultQueryOption = [
            'role' => $this->getDoctorRole(),
            'user_status' => '0',
            'orderby' => 'display_name',
            'include' => !empty($result) ? $result : [-1]
        ];


        ob_start();
        $users = collect(get_users($defaultQueryOption))->map(function ($v) use ($request_data) {
            $next_slot = $this->getNextAvailableSlot($v->data->ID, $request_data['clinic_id']);
            $v->next_available = $next_slot ? $next_slot['formatted'] : 'No slots available';

            $v->user_image = $v->basic_data = $v->description = $v->mobile_number = $v->specialties_all = '';
            $v->no_of_experience = 0;
            $v->telemed = false;
            if (!empty($v->data->ID)) {
                $v->telemed = kcDoctorTelemedServiceEnable($v->data->ID);
                $allUserMetaData = get_user_meta($v->data->ID);
                $v->user_image = !empty($allUserMetaData['doctor_profile_image'][0]) ? wp_get_attachment_url($allUserMetaData['doctor_profile_image'][0]) : '';
                $v->description = !empty($allUserMetaData['doctor_description'][0]) ? $allUserMetaData['doctor_description'][0] : '';
                $user_data = !empty($allUserMetaData['basic_data'][0]) ? $allUserMetaData['basic_data'][0] : [];
                if (!empty($user_data)) {
                    $user_data = json_decode($user_data);
                    $v->specialties_all = !empty($user_data->specialties) ? collect($user_data->specialties)->pluck('label')->implode(',') : '';
                    $v->mobile_number = !empty($user_data->mobile_number) ? $user_data->mobile_number : '';
                    $v->no_of_experience = !empty($user_data->no_of_experience) ? $user_data->no_of_experience : 0;
                    $v->qualifications = !empty($user_data->qualifications) ? collect($user_data->qualifications)->pluck('degree')->implode(',') : '';
                }
            }
            if (!empty($request_data['searchKey'])) {
                $searchKey = mb_strtolower($request_data['searchKey']);
                if (
                    strpos(mb_strtolower($v->data->user_email), $searchKey) !== false
                    || strpos(mb_strtolower($v->data->display_name), $searchKey) !== false
                    || strpos(mb_strtolower($v->specialties_all), $searchKey) !== false
                    || strpos(strtolower($v->mobile_number), $searchKey) !== false
                ) {
                    $this->doctorHtmlContent($v);
                    return $v;
                };
            } else {
                $this->doctorHtmlContent($v);
                return $v;
            }
        })->values()->toArray();


        $users = array_filter($users);

        if (empty($users)) {
            wp_send_json([
                'status' => false,
                'data' => __('No Doctor Available For This Clinic', 'kc-lang'),
            ]);
        }

        wp_send_json([
            'status' => true,
            'data' => ob_get_clean()
        ]);

    }

    private function getNextAvailableSlot($doctor_id, $clinic_id, $service = null) {
        $schedule_table = $this->db->prefix . 'kc_clinic_schedule';
        $today = date('Y-m-d');

        // Check next 30 days
        for ($i = 0; $i < 30; $i++) {
            $check_date = date('Y-m-d', strtotime("+$i days"));

            // Check if doctor is on leave
            $is_on_leave = $this->db->get_var("
                SELECT COUNT(*)
                FROM {$schedule_table}
                WHERE ((module_type = 'doctor' AND module_id = " . (int)$doctor_id . ")
                       OR (module_type = 'clinic' AND module_id = " . (int)$clinic_id . "))
                AND start_date <= '{$check_date}'
                AND end_date >= '{$check_date}'
                AND status = 1
            ");

            if ($is_on_leave) {
                continue;
            }

            // Use existing kvGetTimeSlots function to get available slots
            $timeSlots = kvGetTimeSlots([
                'date' => $check_date,
                'doctor_id' => $doctor_id,
                'clinic_id' => $clinic_id,
                'service' => $service
            ], "", true);

            // If slots found for this day
            if (!empty($timeSlots)) {
                foreach ($timeSlots as $sessionSlots) {
                    foreach ($sessionSlots as $slot) {
                        if ($slot['available']) {
                            $date = $check_date === $today ? 'Today' : date('d/m/Y', strtotime($check_date));
                            return [
                                'formatted' => "Next available: {$date} {$slot['time']}",
                                'date' => $date,
                                'time' => $slot['time']
                            ];
                        }
                    }
                }
            }
        }

        return null;
    }

    public function getClinicArray()
    {
        $request_data = $this->request->getInputs();
        $searchKey = esc_sql($request_data['searchKey']);
        $clinic_selected_condition = ' ';
        $clinics_table = $this->db->prefix . 'kc_clinics';
        $clinic_doctor_table = $this->db->prefix . 'kc_doctor_clinic_mappings';

        if (!isKiviCareProActive()) {
            $clinics[] = kcGetDefaultClinicId();
        } else {
            $clinics = collect($this->db->get_results("SELECT id FROM {$clinics_table} "))->pluck('id')->toArray();
        }

        $preselected_clinic = '';
        if (!empty($request_data['preselected_clinic'])) {
            $preselected_clinic = array_filter(array_map('absint', explode(',', $request_data['preselected_clinic'])));
            $clinics = $preselected_clinic;
        }

        if (!empty($request_data['doctor_id'])) {
            $doctor_id = (int) $request_data['doctor_id'];
            $clinics = collect($this->db->get_results("SELECT clinic_id FROM {$clinic_doctor_table} WHERE doctor_id = {$doctor_id} "))->pluck('clinic_id')->toArray();
        }

        if (!empty($request_data['service_id'])) {
            $request_data['service_id'] = implode(",", array_map('absint', $request_data['service_id']));
            $doctor_condition = !empty($request_data['doctor_id']) ? " AND doctor_id=" . (int) $request_data['doctor_id'] . " " : ' ';
            $clinics = collect($this->db->get_results("SELECT clinic_id
            FROM {$this->db->prefix}kc_services
            WHERE (mapping_id IN ({$request_data['service_id']}) OR id IN ({$request_data['service_id']})) {$doctor_condition} "))->pluck('clinic_id')->toArray();
        }

        if (!empty($clinics)) {
            $clinics = array_unique($clinics);
            $clinics = implode(',', $clinics);
            $clinic_selected_condition = " AND id IN ({$clinics}) ";
        }

        $query = "SELECT *,CONCAT(address, ', ', city,', ',postal_code,', ',country) AS clinic_full_addres,
       email AS user_email,telephone_no AS mobile_number  FROM {$clinics_table} WHERE status='1'
       {$clinic_selected_condition} AND ( name like '%$searchKey%' OR email LIKE '%$searchKey%' OR telephone_no LIKE '%$searchKey%' OR city LIKE '%$searchKey%' OR postal_code LIKE '%$searchKey%' OR address LIKE '%$searchKey%' OR country LIKE '%$searchKey%' OR specialties LIKE '%$searchKey%' OR address LIKE '%$searchKey%') ORDER BY name";

        $clinicList = $this->db->get_results($query);

        if (empty($clinicList)) {
            wp_send_json([
                'status' => false,
                'data' => '',
            ]);
        }

        // Check if the request is from the enhanced Vue booking widget
        $widgetType = isset($request_data['widgetType']) ? sanitize_text_field($request_data['widgetType']) : '';
        $moduleType = isset($request_data['module_type']) ? sanitize_text_field($request_data['module_type']) : '';
        
        // If the request is from the enhanced Vue widget, return JSON data
        if ($widgetType === 'enhanced' || ($widgetType === 'phpWidget' && $moduleType === 'vue')) {
            $formattedClinics = [];
            foreach ($clinicList as $clinic) {
                $specialtiesArray = [];
                if (!empty($clinic->specialties)) {
                    $specialties = json_decode($clinic->specialties, true);
                    if (is_array($specialties)) {
                        foreach ($specialties as $specialty) {
                            if (isset($specialty['label'])) {
                                $specialtiesArray[] = $specialty['label'];
                            }
                        }
                    }
                }
                
                $formattedClinics[] = [
                    'id' => $clinic->id,
                    'name' => $clinic->name,
                    'address' => $clinic->clinic_full_addres,
                    'email' => $clinic->user_email,
                    'telephone' => $clinic->mobile_number,
                    'description' => $clinic->description ?? '',
                    'specialties' => $specialtiesArray
                ];
            }
            
            wp_send_json([
                'status' => true,
                'data' => $formattedClinics,
            ]);
            return;
        }
        
        // For the original widget, return HTML as before
        ob_start();
        foreach ($clinicList as $clinic) {
            ?>
            <div class="iq-client-widget clinic-container">
                <input type="radio" class="hidden card-checkbox selected-clinic" name="card_main"
                    id="clinic_<?php echo esc_html($clinic->id); ?>" value="<?php echo esc_html($clinic->id); ?>"
                    clinicName="<?php echo esc_html($clinic->name); ?>"
                    clinicAddress="<?php echo esc_html($clinic->clinic_full_addres); ?>">
                <label class="w-full" for="clinic_<?php echo esc_html($clinic->id); ?>">
                    <div class="w-full text-left p-4 rounded-lg border transition-all border-gray-200 hover:border-blue-200">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-building2 w-4 h-4 text-gray-400 mr-2 ">
                                        <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path>
                                        <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path>
                                        <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path>
                                        <path d="M10 6h4"></path>
                                        <path d="M10 10h4"></path>
                                        <path d="M10 14h4"></path>
                                        <path d="M10 18h4"></path>
                                    </svg>
                                    <span class="font-medium text-lg text-gray-900 m-0"><?php echo esc_html($clinic->name); ?></span>
                                </div>
                                <?php if (kcGetSingleWidgetSetting('showClinicAddress')) { ?>
                                    <p class="text-sm text-gray-500 mt-1"><?php echo esc_html($clinic->clinic_full_addres); ?>
                                        <a class="inline-flex ml-1" target="_blank"
                                            href="<?php echo add_query_arg('q', $clinic->clinic_full_addres, 'https://www.google.com/maps'); ?>">
                                            <svg class="w-4 h-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round">
                                                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                                <polyline points="15 3 21 3 21 9"></polyline>
                                                <line x1="10" y1="14" x2="21" y2="3"></line>
                                            </svg>
                                        </a>
                                    </p>
                                <?php } ?>

                                <?php if (!empty($clinic->specialties)) { ?>
                                    <div class="flex flex-wrap gap-2 mt-2">
                                        <?php
                                        $specialties = json_decode($clinic->specialties, true); // Decode the JSON string into an associative array
                                        foreach ($specialties as $specialty) { ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-600">
                                                <?php echo htmlspecialchars($specialty['label']); ?>
                                            </span>
                                        <?php } ?>
                                    </div>
                                <?php } ?>

                            </div>
                            <!-- <span class="text-sm text-gray-500 ml-4 whitespace-nowrap">Next available: Tomorrow 2:00 PM</span> -->
                        </div>
                    </div>
                </label>
            </div>
            <?php
        }
        wp_send_json([
            'status' => true,
            'data' => ob_get_clean(),
        ]);
    }
    
    /**
     * Get clinic details for the enhanced booking widget with JSON data
     */
    public function getClinicDetailsJson()
    {
        $request_data = $this->request->getInputs();
        $searchKey = esc_sql($request_data['searchKey']);
        $clinic_selected_condition = ' ';
        $clinics_table = $this->db->prefix . 'kc_clinics';
        $clinic_doctor_table = $this->db->prefix . 'kc_doctor_clinic_mappings';

        if (!isKiviCareProActive()) {
            $clinics[] = kcGetDefaultClinicId();
        } else {
            $clinics = collect($this->db->get_results("SELECT id FROM {$clinics_table} "))->pluck('id')->toArray();
        }

        $preselected_clinic = '';
        if (!empty($request_data['preselected_clinic'])) {
            $preselected_clinic = array_filter(array_map('absint', explode(',', $request_data['preselected_clinic'])));
            $clinics = $preselected_clinic;
        }

        if (!empty($request_data['doctor_id'])) {
            $doctor_id = (int) $request_data['doctor_id'];
            $clinics = collect($this->db->get_results("SELECT clinic_id FROM {$clinic_doctor_table} WHERE doctor_id = {$doctor_id} "))->pluck('clinic_id')->toArray();
        }

        if (!empty($request_data['service_id'])) {
            $request_data['service_id'] = implode(",", array_map('absint', $request_data['service_id']));
            $doctor_condition = !empty($request_data['doctor_id']) ? " AND doctor_id=" . (int) $request_data['doctor_id'] . " " : ' ';
            $clinics = collect($this->db->get_results("SELECT clinic_id
            FROM {$this->db->prefix}kc_services
            WHERE (mapping_id IN ({$request_data['service_id']}) OR id IN ({$request_data['service_id']})) {$doctor_condition} "))->pluck('clinic_id')->toArray();
        }

        if (!empty($clinics)) {
            $clinics = array_unique($clinics);
            $clinics = implode(',', $clinics);
            $clinic_selected_condition = " AND id IN ({$clinics}) ";
        }

        $query = "SELECT *,CONCAT(address, ', ', city,', ',postal_code,', ',country) AS clinic_full_addres,
       email AS user_email,telephone_no AS mobile_number  FROM {$clinics_table} WHERE status='1' 
       {$clinic_selected_condition} AND ( name like '%$searchKey%' OR email LIKE '%$searchKey%' OR telephone_no LIKE '%$searchKey%' OR city LIKE '%$searchKey%' OR postal_code LIKE '%$searchKey%' OR address LIKE '%$searchKey%' OR country LIKE '%$searchKey%' OR specialties LIKE '%$searchKey%' OR address LIKE '%$searchKey%') ORDER BY name";

        $clinicList = $this->db->get_results($query);

        if (empty($clinicList)) {
            wp_send_json([
                'status' => false,
                'data' => [],
                'message' => __('No clinics found', 'kc-lang')
            ]);
        }
        
        $formattedClinics = [];
        foreach ($clinicList as $clinic) {
            $specialtiesArray = [];
            if (!empty($clinic->specialties)) {
                $specialties = json_decode($clinic->specialties, true);
                if (is_array($specialties)) {
                    foreach ($specialties as $specialty) {
                        if (isset($specialty['label'])) {
                            $specialtiesArray[] = $specialty['label'];
                        }
                    }
                }
            }
            
            $formattedClinics[] = [
                'id' => $clinic->id,
                'name' => $clinic->name,
                'address' => $clinic->clinic_full_addres,
                'email' => $clinic->user_email,
                'telephone' => $clinic->mobile_number,
                'description' => $clinic->description ?? '',
                'specialties' => $specialtiesArray
            ];
        }
        
        wp_send_json([
            'status' => true,
            'data' => $formattedClinics,
        ]);
    }

    public function appointmentConfirmPage_delete()
    {
        $request_data = $this->request->getInputs();

        $field_id = 0;
        $label_list = [];
        if (!empty($request_data['custom_field']) && count($request_data['custom_field'])) {
            foreach ($request_data['custom_field'] as $custom_key => $custom) {
                $field_id = (int) str_replace("custom_field_", "", $custom_key);
                $query = "SELECT fields FROM {$this->db->prefix}kc_custom_fields WHERE id = {$field_id}";
                $label_list[$custom_key] = collect($this->db->get_results($query))->pluck('fields')->map(function ($x) use ($custom) {
                    return !empty(json_decode($x)->label) ? json_decode($x)->label : '';
                })->toArray();
            }
        }

        $request_data['doctor_id'] = (int) $request_data['doctor_id'];
        $request_data['clinic_id'] = (int) $request_data['clinic_id'];
        $doctor_name = $this->db->get_var("SELECT display_name FROM {$this->db->base_prefix}users WHERE ID = {$request_data['doctor_id']}");
        $request_data['service_list_data'] = $request_data['service_list'];
        $request_data['service_list'] = implode(",", array_map('absint', $request_data['service_list']));
        $request_data['tax_details'] = apply_filters('kivicare_calculate_tax', [
            'status' => false,
            'message' => '',
            'data' => []
        ], [
            "id" => '',
            "type" => 'appointment',
            "doctor_id" => $request_data['doctor_id'],
            "clinic_id" => $request_data['clinic_id'],
            "service_id" => $request_data['service_list_data'],
            "total_charge" => $this->db->get_var("SELECT SUM(charges) FROM {$this->db->prefix}kc_services
                                        WHERE doctor_id = {$request_data['doctor_id']} AND  clinic_id = {$request_data['clinic_id']}
                                         AND (mapping_id IN ({$request_data['service_list']}) OR id IN ({$request_data['service_list']})) "),
            'extra_data' => $request_data
        ]);
        $patient_id = get_current_user_id();
        $patient_data = $this->db->get_row("SELECT * FROM {$this->db->base_prefix}users WHERE ID = {$patient_id}");
        $clinic_currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
        $patient_basic_data = json_decode(get_user_meta($patient_id, 'basic_data', true));

        $service_list_data = $this->db->get_results("SELECT * FROM {$this->db->prefix}kc_services AS service
                                                        WHERE (service.mapping_id IN ({$request_data['service_list']}) OR service.id IN ({$request_data['service_list']}))
                                                        AND service.clinic_id= {$request_data['clinic_id']}
                                                        AND service.doctor_id={$request_data['doctor_id']}");

        $name = $address = '';
        $patient_country_calling_code = get_user_meta($patient_id, 'country_calling_code', true);
        $country_calling_code = !empty($patient_country_calling_code) ? '+' . $patient_country_calling_code : '';

        if (!isKiviCareProActive()) {
            $data = kcClinicDetail(kcGetDefaultClinicId());
        } else {
            $data = kcClinicDetail((int) $request_data['clinic_id']);
        }

        if (!empty($data)) {
            $name = $data->name;
            $address = $data->address . ', ' . $data->postal_code . ', ' . $data->city . ', ' . $data->country;
        }
        ob_start();
        ?>
        <div class="kivi-col-6 pr-4">
            <div class="kc-confirmation-info-section">
                <h6 class="iq-text-uppercase iq-color-secondary iq-letter-spacing-1 mb-2">
                    <?php echo esc_html__('Clinic info', 'kc-lang'); ?>
                </h6>
                <div class="iq-card iq-preview-details">
                    <table class="iq-table-border mb-0" style="border:0;">
                        <tr>
                            <td>
                                <h6 style="width: 15em;"><?php echo esc_html($name); ?></h6>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <p style="width: 15em;"><?php echo esc_html(!empty($address) ? $address : ''); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="kc-confirmation-info-section">
                <h6 class="iq-text-uppercase iq-color-secondary iq-letter-spacing-1 mb-2">
                    <?php echo esc_html__('Patient info', 'kc-lang'); ?>
                </h6>
                <div class="iq-card iq-preview-details kc-patient-info">
                    <table class="iq-table-border mb-0" style="border:0;">
                        <tr>
                            <td>
                                <h6><?php echo esc_html__('Name', 'kc-lang'); ?>:</h6>
                            </td>
                            <td id="patientName">
                                <p><?php echo esc_html(!empty($patient_data->display_name) ? $patient_data->display_name : ''); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h6><?php echo esc_html__('Number', 'kc-lang'); ?>:</h6>
                            </td>
                            <td id="patientTelephone">
                                <p><?php echo esc_html(!empty($patient_basic_data->mobile_number) ? $country_calling_code . ' ' . $patient_basic_data->mobile_number : ''); ?>
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h6><?php echo esc_html__('Email', 'kc-lang'); ?>:</h6>
                            </td>
                            <td id="patientEmail">
                                <p><?php echo esc_html(!empty($patient_data->user_email) ? $patient_data->user_email : ''); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <?php
        ?>
        <div class="item-img-1 kivi-col-6 mb-2 pr-4">
            <h6 class="iq-text-uppercase iq-color-secondary iq-letter-spacing-1">
                <?php echo esc_html__('Appointment summary', 'kc-lang'); ?>
            </h6>
            <div class="iq-card iq-card-border mt-3">
                <div class="d-flex justify-content-between align-items-center">
                    <p><?php echo esc_html__('Doctor', 'kc-lang'); ?> :</p>
                    <h6 id="doctorname"><?php echo esc_html($doctor_name); ?></h6>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <p><?php echo esc_html__('Date ', 'kc-lang'); ?> :</p>
                    <h6><span id="dateOfAppointment"><?php echo esc_html(kcGetFormatedDate($request_data['date'])); ?></span>
                    </h6>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <p><?php echo esc_html__('Time ', 'kc-lang'); ?> :</p>
                    <h6><span id="timeOfAppointment"><?php echo esc_html($request_data['time']); ?></span></h6>
                </div>
                <div class="iq-card iq-preview-details mt-4">
                    <h6><?php echo esc_html__('Services', 'kc-lang'); ?></h6>

                    <span id="services_list">
                        <?php
                        if (!empty($service_list_data) && count($service_list_data) > 0) {
                            $service_total_charge = array_sum(collect($service_list_data)->pluck('charges')->toArray());
                            foreach ($service_list_data as $service_data) {
                                ?>
                                <div class="d-flex justify-content-between align-items-center mt-1">
                                    <p> <?php echo esc_html($service_data->name); ?></p>
                                    <h6><?php echo esc_html((!empty($clinic_currency_detail['prefix']) ? $clinic_currency_detail['prefix'] : '') . $service_data->charges . (!empty($clinic_currency_detail['postfix']) ? $clinic_currency_detail['postfix'] : '')); ?>
                                    </h6>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </span>
                    <?php
                    if (!empty($request_data['tax_details']['data'])) {
                        ?>
                        <h6 style="padding-top: 16px;"><?php echo esc_html__('Taxes', 'kc-lang'); ?></h6>
                        <?php
                        foreach ($request_data['tax_details']['data'] as $tax) {
                            ?>
                            <div class="d-flex justify-content-between align-items-center mt-1">
                                <p> <?php echo esc_html($tax->name); ?></p>
                                <h6><?php echo esc_html((!empty($clinic_currency_detail['prefix']) ? $clinic_currency_detail['prefix'] : '') . $tax->charges . (!empty($clinic_currency_detail['postfix']) ? $clinic_currency_detail['postfix'] : '')); ?>
                                </h6>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
                <?php
                $request_data['service_total_charge'] = $service_total_charge;
                $request_data['clinic_currency_detail'] = $clinic_currency_detail;
                $this->appointmentTaxDetailHtml($request_data);
                ?>
            </div>
        </div>

        <?php
        if (kcAppointmentMultiFileUploadEnable() && !empty($request_data['file'])) {
            $request_data['file'] = array_map('absint', $request_data['file']);
            ?>
            <div class="kivi-col-6 pr-4">
                <h6 class="iq-text-uppercase iq-color-secondary iq-letter-spacing-1">
                    <?php echo esc_html__('Uploaded files', 'kc-lang'); ?>
                </h6>
                <div class="iq-card iq-preview-details mt-3">
                    <table class="iq-table-border" style="border: 0;">
                        <?php
                        foreach ($request_data['file'] as $key => $file) {
                            ?>
                            <tr>
                                <td>
                                    <a href="<?php echo esc_url(wp_get_attachment_url($file)); ?>" target="_blank"
                                        alt="<?php echo esc_html(get_the_title($file)); ?>">
                                        <i class="fas fa-external-link-alt"></i><?php echo ' ' . esc_html(get_the_title($file)) ?>
                                    </a>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                    </table>
                </div>
            </div>
            <?php
        }
        $custom_field_values = [];
        if (!empty($request_data['custom_field'])) {
            foreach ($request_data['custom_field'] as $key => $val) {
                if (!empty($val)) {
                    array_push($custom_field_values, $val);
                }
            }
        }
        if ((kcCheckExtraTabConditionInAppointmentWidget('description') && !empty($request_data['description'])) || (!empty($request_data['custom_field']) && !empty($custom_field_values))) {
            ?>
            <div class="kivi-col-6 pr-4">
                <h6 class="iq-text-uppercase iq-color-secondary iq-letter-spacing-1">
                    <?php echo esc_html__('Other info', 'kc-lang'); ?>
                </h6>
                <div class="iq-card iq-preview-details mt-3">
                    <table class="iq-table-border" style="border: 0;">
                        <?php if (!empty($request_data['description'])) { ?>
                            <tr>
                                <td>
                                    <h6><?php echo esc_html__('Description', 'kc-lang'); ?>:</h6>
                                </td>
                                <td id="AppointmentDescription">
                                    <?php echo esc_html(!empty($request_data['description']) ? $request_data['description'] : ''); ?>
                                </td>
                            </tr>
                            <?php
                        }
                        if (!empty($label_list)) {
                            foreach ($label_list as $label_key => $label_value) {
                                if (!empty($request_data['custom_field'][$label_key])) {
                                    if (
                                        is_array($request_data['custom_field'][$label_key]) &&
                                        isset($request_data['custom_field'][$label_key][0]['text'])
                                    ) {
                                        $request_data['custom_field'][$label_key] = collect($request_data['custom_field'][$label_key])->pluck('text')->implode(', ');
                                    } else {
                                        $request_data['custom_field'][$label_key] = is_array($request_data['custom_field'][$label_key])
                                            ? implode(', ', $request_data['custom_field'][$label_key]) : $request_data['custom_field'][$label_key];

                                    }
                                    ?>
                                    <tr>
                                        <td>
                                            <h6><?php echo esc_html($label_value[0]); ?>:</h6>
                                        </td>
                                        <td>
                                            <?php echo esc_html(!empty($request_data['custom_field'][$label_key]) ? $request_data['custom_field'][$label_key] : ''); ?>
                                        </td>
                                    </tr>
                                    <?php
                                }
                            }
                        }
                        ?>
                    </table>
                </div>
            </div>
            <?php
        }

        $htmldata = ob_get_clean();

        wp_send_json([
            'status' => true,
            'message' => __('confirm page details', 'kc-lang'),
            'data' => $htmldata,
            'tax_details' => !empty($request_data['tax_details']['data']) ? $request_data['tax_details']['data'] : []
        ]);
    }

    public function appointmentConfirmPage()
    {
        $request_data = $this->request->getInputs();

        // Process custom fields
        $field_id = 0;
        $label_list = [];
        if (!empty($request_data['custom_field']) && count($request_data['custom_field'])) {
            foreach ($request_data['custom_field'] as $custom_key => $custom) {
                $field_id = (int) str_replace("custom_field_", "", $custom_key);
                $query = "SELECT fields FROM {$this->db->prefix}kc_custom_fields WHERE id = {$field_id}";
                $label_list[$custom_key] = collect($this->db->get_results($query))->pluck('fields')->map(function ($x) use ($custom) {
                    return !empty(json_decode($x)->label) ? json_decode($x)->label : '';
                })->toArray();
            }
        }

        // Get doctor details
        $request_data['doctor_id'] = (int) $request_data['doctor_id'];
        $request_data['clinic_id'] = (int) $request_data['clinic_id'];
        $doctor_name = $this->db->get_var("SELECT display_name FROM {$this->db->base_prefix}users WHERE ID = {$request_data['doctor_id']}");

        // Process services
        $request_data['service_list_data'] = $request_data['service_list'];
        $request_data['service_list'] = implode(",", array_map('absint', $request_data['service_list']));

        // Calculate tax
        $request_data['tax_details'] = apply_filters('kivicare_calculate_tax', [
            'status' => false,
            'message' => '',
            'data' => []
        ], [
            "id" => '',
            "type" => 'appointment',
            "doctor_id" => $request_data['doctor_id'],
            "clinic_id" => $request_data['clinic_id'],
            "service_id" => $request_data['service_list_data'],
            "total_charge" => $this->db->get_var("SELECT SUM(charges) FROM {$this->db->prefix}kc_services
                WHERE doctor_id = {$request_data['doctor_id']} AND clinic_id = {$request_data['clinic_id']}
                AND (mapping_id IN ({$request_data['service_list']}) OR id IN ({$request_data['service_list']})) "),
            'extra_data' => $request_data
        ]);

        // Get patient details
        $patient_id = get_current_user_id();
        $patient_data = $this->db->get_row("SELECT * FROM {$this->db->base_prefix}users WHERE ID = {$patient_id}");
        $clinic_currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
        $patient_basic_data = json_decode(get_user_meta($patient_id, 'basic_data', true));

        // Get service details
        $service_list_data = $this->db->get_results("SELECT * FROM {$this->db->prefix}kc_services AS service
            WHERE (service.mapping_id IN ({$request_data['service_list']}) OR service.id IN ({$request_data['service_list']}))
            AND service.clinic_id= {$request_data['clinic_id']}
            AND service.doctor_id={$request_data['doctor_id']}");

        // Get clinic details
        if (!isKiviCareProActive()) {
            $clinic_data = kcClinicDetail(kcGetDefaultClinicId());
        } else {
            $clinic_data = kcClinicDetail((int) $request_data['clinic_id']);
        }

        $clinic_name = !empty($clinic_data) ? $clinic_data->name : '';
        $clinic_address = !empty($clinic_data) ? $clinic_data->address . ', ' . $clinic_data->postal_code . ', ' . $clinic_data->city . ', ' . $clinic_data->country : '';

        // Get patient contact details
        $patient_country_calling_code = get_user_meta($patient_id, 'country_calling_code', true);
        $country_calling_code = !empty($patient_country_calling_code) ? '+' . $patient_country_calling_code : '';
        $patient_contact = !empty($patient_basic_data->mobile_number) ? $country_calling_code . ' ' . $patient_basic_data->mobile_number : '';

        ob_start();
        ?>
        <div class="">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Clinic Information -->
                    <div class="bg-white rounded-lg border p-4">
                        <h2 class="text-lg text-left font-medium text-gray-900 mb-3">
                            <?php echo esc_html__('Clinic Information', 'kc-lang'); ?></h2>
                        <div class="space-y-2">
                            <div class="flex items-start space-x-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="w-5 h-5 text-gray-400 mt-1">
                                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                                    <circle cx="12" cy="10" r="3"></circle>
                                </svg>
                                <div class="text-left">
                                    <p class="font-medium"><?php echo esc_html($clinic_name); ?></p>
                                    <p class="text-gray-600"><?php echo esc_html($clinic_address); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Patient Information -->
                    <div class="bg-white rounded-lg border p-4">
                        <h2 class="text-lg text-left font-medium text-gray-900 mb-3">
                            <?php echo esc_html__('Patient Information', 'kc-lang'); ?></h2>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="w-5 h-5 text-gray-400 mr-3">
                                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                                <span class="text-left"><?php echo esc_html($patient_data->display_name); ?></span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="w-5 h-5 text-gray-400 mr-3">
                                    <path
                                        d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
                                    </path>
                                </svg>
                                <span class="text-left"><?php echo esc_html($patient_contact); ?></span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="w-5 h-5 text-gray-400 mr-3">
                                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                                </svg>
                                <span class="text-left"><?php echo esc_html($patient_data->user_email); ?></span>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($request_data['description'])): ?>
                        <!-- Visit Reason -->
                        <div class="bg-white rounded-lg border p-4">
                            <h2 class="text-lg text-left font-medium text-gray-900 mb-3"><?php echo esc_html__('Visit Reason', 'kc-lang'); ?>
                            </h2>
                            <p class="text-gray-600"><?php echo esc_html($request_data['description']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Right Column -->
                <div class="bg-white rounded-lg border p-4">
                    <h2 class="text-lg text-left font-medium text-gray-900 mb-4">
                        <?php echo esc_html__('Appointment Summary', 'kc-lang'); ?></h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center py-2">
                            <span class="text-gray-600"><?php echo esc_html__('Doctor', 'kc-lang'); ?>:</span>
                            <span class="font-medium"><?php echo esc_html($doctor_name); ?></span>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <span class="text-gray-600"><?php echo esc_html__('Date', 'kc-lang'); ?>:</span>
                            <span class="font-medium"><?php echo esc_html(kcGetFormatedDate($request_data['date'])); ?></span>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <span class="text-gray-600"><?php echo esc_html__('Time', 'kc-lang'); ?>:</span>
                            <span class="font-medium"><?php echo esc_html($request_data['time']); ?></span>
                        </div>

                        <!-- Services -->
                        <div class="border-t pt-4 mt-4">
                        <h2 class="text-lg text-left font-medium text-gray-900 mb-4"><?php echo esc_html__('Services', 'kc-lang'); ?></h2>
                            <?php
                            $total_amount = 0;
                            if (!empty($service_list_data)) {
                                foreach ($service_list_data as $service) {
                                    $total_amount += $service->charges;
                                    ?>
                                    <div class="bg-gray-50 p-3 rounded-lg">
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-600"><?php echo esc_html($service->name); ?></span>
                                            <span class="font-medium">
                                                <?php echo esc_html($clinic_currency_detail['prefix'] . $service->charges . $clinic_currency_detail['postfix']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php
                                }
                            }
                            ?>

                            <?php if (!empty($request_data['tax_details']['data'])): ?>
                                <div class="border-t pt-4 mt-4">
                                    <h3 class="font-medium mb-3"><?php echo esc_html__('Taxes', 'kc-lang'); ?></h3>
                                    <?php
                                    foreach ($request_data['tax_details']['data'] as $tax) {
                                        $total_amount += $tax->charges;
                                        ?>
                                        <div class="bg-gray-50 p-3 rounded-lg">
                                            <div class="flex justify-between items-center">
                                                <span class="text-gray-600"><?php echo esc_html($tax->name); ?></span>
                                                <span class="font-medium">
                                                    <?php echo esc_html($clinic_currency_detail['prefix'] . $tax->charges . $clinic_currency_detail['postfix']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>
                            <?php endif; ?>

                            <!-- Total -->
                            <div class="border-t pt-4 mt-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-medium"><?php echo esc_html__('Total Price', 'kc-lang'); ?></span>
                                    <span class="text-lg font-medium text-purple-600">
                                        <?php echo esc_html($clinic_currency_detail['prefix'] . $total_amount . $clinic_currency_detail['postfix']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (kcAppointmentMultiFileUploadEnable() && !empty($request_data['file'])): ?>
                <!-- Uploaded Files -->
                <div class="bg-white rounded-lg border p-4 mt-6">
                    <h2 class="text-lg text-left font-medium text-gray-900 mb-3"><?php echo esc_html__('Uploaded Files', 'kc-lang'); ?></h2>
                    <div class="space-y-2">
                        <?php
                        $request_data['file'] = array_map('absint', $request_data['file']);
                        foreach ($request_data['file'] as $file):
                            $file_url = wp_get_attachment_url($file);
                            $file_name = get_the_title($file);
                            ?>
                            <div class="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <a href="<?php echo esc_url($file_url); ?>" target="_blank" class="hover:underline">
                                    <?php echo esc_html($file_name); ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Custom Fields and Other Info -->
            <?php
            $custom_field_values = [];
            if (!empty($request_data['custom_field'])) {
                foreach ($request_data['custom_field'] as $key => $val) {
                    if (!empty($val)) {
                        array_push($custom_field_values, $val);
                    }
                }
            }

            if (!empty($label_list)): ?>
                <div class="bg-white rounded-lg border p-4 mt-6">
                    <h2 class="text-lg text-left font-medium text-gray-900 mb-3">
                        <?php echo esc_html__('Additional Information', 'kc-lang'); ?></h2>
                    <div class="space-y-3">
                        <?php foreach ($label_list as $label_key => $label_value):
                            if (!empty($request_data['custom_field'][$label_key])):
                                if (is_array($request_data['custom_field'][$label_key]) && isset($request_data['custom_field'][$label_key][0]['text'])) {
                                    $field_value = collect($request_data['custom_field'][$label_key])->pluck('text')->implode(', ');
                                } else {
                                    $field_value = is_array($request_data['custom_field'][$label_key])
                                        ? implode(', ', $request_data['custom_field'][$label_key])
                                        : $request_data['custom_field'][$label_key];
                                }
                                ?>
                                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                                    <span class="text-gray-600 text-left"><?php echo esc_html($label_value[0]); ?></span>
                                    <span class="font-medium"><?php echo esc_html($field_value); ?></span>
                                </div>
                            <?php
                            endif;
                        endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>


        </div>
        <?php
        $html_content = ob_get_clean();

        wp_send_json([
            'status' => true,
            'message' => __('confirm page details', 'kc-lang'),
            'data' => $html_content,
            'tax_details' => !empty($request_data['tax_details']['data']) ? $request_data['tax_details']['data'] : []
        ]);
    }

    /**
     * @throws Exception
     */
    public function getAppointmentPrint()
    {
        $request_data = $this->request->getInputs();
        if (empty($request_data['id'])) {
            wp_send_json([
                'data' => '',
                'status' => false
            ]);
        }
        $appointment_id = (int) $request_data['id'];

        if (!((new KCAppointment())->appointmentPermissionUserWise($appointment_id))) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $patient_appointment_table = $this->db->prefix . 'kc_appointments';
        $clinics_table = $this->db->prefix . 'kc_clinics';
        $users_table = $this->db->base_prefix . 'users';
        $appointments_service_table = $this->db->prefix . 'kc_appointment_service_mapping';
        $service_table = $this->db->prefix . 'kc_services';


        $patient_condition = "";
        if ($this->getLoginUserRole() == $this->getPatientRole()) {
            $patient_id = get_current_user_id();
            $patient_condition = " AND {$patient_appointment_table}.patient_id = {$patient_id} ";
        }
        $query = "SELECT {$patient_appointment_table}.*,
                       {$patient_appointment_table}.status AS appointment_status,
                       doctors.display_name  AS doctor_name,
                       doctors.user_email AS doctor_email,
                       patients.display_name AS patient_name,
                       patients.user_email AS patient_email,
                       GROUP_CONCAT({$service_table}.name) AS all_services_name,
                       {$clinics_table}.*,
                       CONCAT('#',{$clinics_table}.address, ', ', {$clinics_table}.city,', '
		             ,{$clinics_table}.postal_code,', ',{$clinics_table}.country) AS clinic_address
                    FROM  {$patient_appointment_table}
                       LEFT JOIN {$users_table} doctors
                              ON {$patient_appointment_table}.doctor_id = doctors.id
                      LEFT JOIN {$users_table} patients
                              ON {$patient_appointment_table}.patient_id = patients.id
                       LEFT JOIN {$clinics_table}
                              ON {$patient_appointment_table}.clinic_id = {$clinics_table}.id
                      LEFT JOIN {$appointments_service_table}
                          ON {$patient_appointment_table}.id = {$appointments_service_table}.appointment_id
                      LEFT JOIN {$service_table}
				            ON {$appointments_service_table}.service_id = {$service_table}.id
                    WHERE {$patient_appointment_table}.id = {$appointment_id} {$patient_condition} GROUP BY {$patient_appointment_table}.id";

        $encounter = $this->db->get_row($query);

        $patient_id_match = false;
        if ($this->getLoginUserRole() == $this->getPatientRole()) {
            $patient_id = get_current_user_id();
            // $patient_id_match = false;
            // $parient_condition = " AND {$patient_appointment_table}.patient_id = {$patient_id} ";
            if ($encounter->patient_id == $patient_id) {
                $patient_id_match = true;
            }
        }

        if (!empty($encounter)) {
            $encounter->medical_history = '';
            $encounter->prescription = '';
            $basic_data = get_user_meta((int) $encounter->doctor_id, 'basic_data', true);
            $basic_data = json_decode($basic_data);
            $basic_data->qualifications = !empty($basic_data->qualifications) ? $basic_data->qualifications : [];
            foreach ($basic_data->qualifications as $q) {
                $qualifications[] = $q->degree;
                $qualifications[] = $q->university;
            }
            $patient_basic_data = json_decode(get_user_meta((int) $encounter->patient_id, 'basic_data', true));
            $encounter->patient_gender = !empty($patient_basic_data->gender)
                ? ($patient_basic_data->gender === 'female'
                    ? 'F' : 'M') : '';
            $encounter->patient_address = (!empty($patient_basic_data->address) ? $patient_basic_data->address : '');
            $encounter->patient_city = (!empty($patient_basic_data->city) ? $patient_basic_data->city : '');
            $encounter->patient_state = (!empty($patient_basic_data->state) ? $patient_basic_data->state : '');
            $encounter->patient_country = (!empty($patient_basic_data->country) ? $patient_basic_data->country : '');
            $encounter->patient_postal_code = (!empty($patient_basic_data->postal_code) ? $patient_basic_data->postal_code : '');
            $encounter->contact_no = (!empty($patient_basic_data->mobile_number) ? $patient_basic_data->mobile_number : '');
            $encounter->patient_add = $encounter->patient_address . ',' . $encounter->patient_city
                . ',' . $encounter->patient_state . ',' . $encounter->patient_country . ',' . $encounter->patient_postal_code;
            $encounter->date = current_time('Y-m-d');
            $encounter->patient_age = '';
            if (!empty($patient_basic_data->dob)) {
                try {
                    $from = new DateTime($patient_basic_data->dob);
                    $to = new DateTime('today');
                    $years = $from->diff($to)->y;
                    $months = $from->diff($to)->m;
                    $days = $from->diff($to)->d;
                    if (empty($months) && empty($years)) {
                        $encounter->patient_age = $days . esc_html__(' Days', 'kc-lang');
                    } else if (empty($years)) {
                        $encounter->patient_age = $months . esc_html__(' Months', 'kc-lang');
                    } else {
                        $encounter->patient_age = $years . esc_html__(' Years', 'kc-lang');
                    }
                } catch (Exception $e) {
                    wp_send_json([
                        'data' => '',
                        'status' => false,
                        'calendar_content' => '',
                        'message' => $e->getMessage()
                    ]);
                }
            }
            $encounter->qualifications = !empty($qualifications) ? '(' . implode(", ", $qualifications) . ')' : '';
            $encounter->clinic_logo = !empty($encounter->profile_image) ? wp_get_attachment_url($encounter->profile_image) : KIVI_CARE_DIR_URI . 'assets/images/kc-demo-img.png';
            $encounter->calendar_enable = !empty($request_data['calendar_enable']);
        }

        $calender_content = '';
        if (
            isKiviCareProActive() && !empty($request_data['calendar_enable'])
            && $request_data['calendar_enable'] == 'yes'
            && (string) $encounter->appointment_status !== '0'
        ) {
            if (in_array($this->getLoginUserRole(), [$this->getDoctorRole(), $this->getPatientRole()])) {
                $appointment_data = [
                    "clinic_name" => $encounter->name,
                    "clinic_address" => $encounter->clinic_address,
                    "id" => $appointment_id,
                    "start_date" => $encounter->appointment_start_date,
                    "start_time" => $encounter->appointment_start_time,
                    "end_date" => $encounter->appointment_end_date,
                    "end_time" => $encounter->appointment_end_time,
                    "appointment_service" => $encounter->all_services_name,
                    "extra" => $encounter
                ];
                $calender_content = kcAddToCalendarContent($appointment_data);
                if (!empty($calender_content)) {
                    $calender_content = array_merge($calender_content, [
                        "options" => [
                            "Apple",
                            "Google",
                            "iCal",
                            "Microsoft365",
                            "MicrosoftTeams",
                            "Outlook.com",
                            "Yahoo"
                        ],
                        "trigger" => "click",
                    ]);
                }
            }
        }

        wp_send_json([
            'data' => kcPrescriptionHtml($encounter, $appointment_id, 'appointment'),
            'status' => true,
            'calendar_content' => $calender_content,
            'patient_id_match' => $patient_id_match
        ]);
    }

    public function getAppointmentCustomField()
    {
        $request_data = $this->request->getInputs();
        ob_start();
        if (!empty($request_data['user_role'])) {
            if ($request_data['user_role'] === 'kiviCare_doctor') {
                kcGetCustomFieldsListNewDesign('doctor_module', 0);
            } else if ($request_data['user_role'] == 'kiviCare_patient') {
                kcGetCustomFieldsListNewDesign('patient_module', 0);
            }
        } else {
            kcGetCustomFieldsListNewDesign('appointment_module', $request_data['doctor_id']);
        }
        $data = ob_get_clean();

        wp_send_json([
            'data' => $data,
            'status' => true
        ]);
    }

    public function getWidgetPaymentOptions()
    {
        if (!$this->userHasKivicareRole()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        $request_data = $this->request->getInputs();
        $request_data['doctor_id'] = (int) $request_data['doctor_id'];
        $request_data['clinic_id'] = (int) $request_data['clinic_id'];
        $doctor_name = $this->db->get_var("SELECT display_name FROM {$this->db->base_prefix}users WHERE ID = {$request_data['doctor_id']}");
        $clinic_currency_detail = kcGetClinicCurrenyPrefixAndPostfix();
        $service_total_charge = 0;
        $prefix = !empty($clinic_currency_detail['prefix']) ? $clinic_currency_detail['prefix'] : '';
        $postfix = !empty($clinic_currency_detail['postfix']) ? $clinic_currency_detail['postfix'] : '';
        $request_data['service_list_data'] = collect($request_data['service_list'])->pluck('service_id')->toArray();
        $request_data['service_list'] = array_map(function ($v) use ($request_data) {
            $temp = absint($v['service_id']);
            // Updated to work with merged services table structure
            $v['charges'] = $this->db->get_var("SELECT charges FROM {$this->db->prefix}kc_services WHERE id = {$temp} AND clinic_id = {$request_data['clinic_id']} AND doctor_id = {$request_data['doctor_id']}");
            return $v;
        }, $request_data['service_list']);
        ob_start();
        $implode_service_ids = implode(',', $request_data['service_list_data']);
        $request_data['tax_details'] = apply_filters('kivicare_calculate_tax', [
            'status' => false,
            'message' => '',
            'data' => []
        ], [
            "id" => '',
            "type" => 'appointment',
            "doctor_id" => $request_data['doctor_id'],
            "clinic_id" => $request_data['clinic_id'],
            "service_id" => $request_data['service_list_data'],
            "total_charge" => $this->db->get_var("SELECT SUM(charges) FROM {$this->db->prefix}kc_services
                                        WHERE doctor_id = {$request_data['doctor_id']} AND  clinic_id = {$request_data['clinic_id']}
                                         AND (mapping_id IN ({$implode_service_ids}) OR id IN ({$implode_service_ids})) "),
            'extra_data' => $request_data
        ]);
        $i = 0;
        ?>
        <div class="flex space-x-6">
    <!-- Payment Section -->
    <div class="w-1/2">
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="p-6">
                <h6 class="flex items-center space-x-3 text-lg font-semibold text-gray-800 mb-4">
                    <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span><?php echo esc_html__('Select Payment', 'kc-lang'); ?></span>
                </h6>

                <div class="space-y-3">
                    <?php
                    $i = 0;
                    foreach (kcAllPaymentMethodList() as $key => $value) {
                        $payment_logo = apply_filters('kivicare_payment_option_logo', KIVI_CARE_DIR_URI . 'assets/images/' . $key . '.png', $key);
                    ?>
                        <div class="relative">
                            <input type="radio"
                                   class="hidden peer"
                                   name="payment_option"
                                   id="<?php echo esc_html($key); ?>"
                                   value="<?php echo esc_html($key); ?>"
                                   <?php echo $i === 0 ? 'checked' : ''; ?>>

                            <label for="<?php echo esc_html($key); ?>"
                                   class="block w-full p-4 bg-white border-2 rounded-lg cursor-pointer transition-all duration-200
                                          hover:bg-purple-50 hover:border-purple-200
                                          peer-checked:border-purple-500 peer-checked:bg-purple-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-12 flex items-center justify-center bg-white rounded-lg p-2">
                                            <img src="<?php echo esc_url($payment_logo); ?>"
                                                 alt="<?php echo esc_attr($value); ?>"
                                                 class="max-h-full w-auto object-contain">
                                        </div>
                                        <span class="font-medium text-gray-700"><?php echo esc_html($value); ?></span>
                                    </div>
                                    <div class="w-5 h-5 border-2 rounded-full flex items-center justify-center
                                                peer-checked:border-purple-500 peer-checked:bg-purple-500">
                                        <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"/>
                                        </svg>
                                    </div>
                                </div>
                            </label>
                        </div>
                    <?php
                        $i++;
                    }
                    ?>
                </div>
            </div>

            <!-- Security Footer -->
            <div class="px-6 py-4 bg-gray-50 border-t">
                <div class="flex justify-between items-center">
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                        <?php echo esc_html__('Secure and encrypted payments', 'kc-lang'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Summary -->
    <div class="w-1/2">
        <div class="bg-white rounded-xl shadow-sm">
            <div class="p-6">
                <h6 class="flex items-center space-x-3 text-lg font-semibold text-gray-800 mb-6">
                    <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <span><?php echo esc_html__('Appointment Summary', 'kc-lang'); ?></span>
                </h6>

                <!-- Appointment Details -->
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-gray-100">
                        <span class="text-gray-600"><?php echo esc_html__('Doctor', 'kc-lang'); ?></span>
                        <span class="font-medium text-gray-900" id="doctorname"><?php echo esc_html($doctor_name); ?></span>
                    </div>

                    <div class="flex justify-between items-center py-3 border-b border-gray-100">
                        <span class="text-gray-600"><?php echo esc_html__('Date', 'kc-lang'); ?></span>
                        <span class="font-medium text-gray-900" id="dateOfAppointment">
                            <?php echo esc_html(kcGetFormatedDate($request_data['date'])); ?>
                        </span>
                    </div>

                    <div class="flex justify-between items-center py-3 border-b border-gray-100">
                        <span class="text-gray-600"><?php echo esc_html__('Time', 'kc-lang'); ?></span>
                        <span class="font-medium text-gray-900" id="timeOfAppointment">
                            <?php echo esc_html($request_data['time']); ?>
                        </span>
                    </div>
                </div>

                <!-- Services Section -->
                <div class="mt-6">
                    <h6 class="font-semibold text-gray-800 mb-4"><?php echo esc_html__('Services', 'kc-lang'); ?></h6>
                    <div class="space-y-3" id="services_list">
                        <?php
                        if (!empty($request_data['service_list'])) {
                            foreach ($request_data['service_list'] as $service_data) {
                                $service_total_charge += $service_data['charges'];
                        ?>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600"><?php echo esc_html($service_data['name']); ?></span>
                                    <span class="font-medium text-gray-900">
                                        <?php echo esc_html($prefix . $service_data['charges'] . $postfix); ?>
                                    </span>
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>
                </div>

                <!-- Taxes Section -->
                <?php if (!empty($request_data['tax_details']['data'])) { ?>
                    <div class="mt-6">
                        <h6 class="font-semibold text-gray-800 mb-4"><?php echo esc_html__('Taxes', 'kc-lang'); ?></h6>
                        <div class="space-y-3">
                            <?php foreach ($request_data['tax_details']['data'] as $tax) { ?>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600"><?php echo esc_html($tax->name); ?></span>
                                    <span class="font-medium text-gray-900">
                                        <?php echo esc_html((!empty($clinic_currency_detail['prefix']) ? $clinic_currency_detail['prefix'] : '') . $tax->charges . (!empty($clinic_currency_detail['postfix']) ? $clinic_currency_detail['postfix'] : '')); ?>
                                    </span>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                <?php } ?>

                <?php
                $request_data['service_total_charge'] = $service_total_charge;
                $request_data['clinic_currency_detail'] = $clinic_currency_detail;
                $this->appointmentTaxDetailHtml($request_data);
                ?>
            </div>
        </div>
    </div>
</div>
        <?php
        $data = ob_get_clean();

        wp_send_json([
            'data' => $data,
            'status' => true
        ]);
    }

    public function doctorHtmlContent($user)
    {
        ?>
        <div class="iq-client-widget widget-doctor-item" data-index="<?php echo esc_html($user->ID); ?>">
            <input type="radio" class="card-checkbox selected-doctor kivicare-doctor-widget hidden"
                data-index="<?php echo esc_html($user->ID); ?>" name="card_main" id="doctor_<?php echo esc_html($user->ID); ?>"
                value="<?php echo esc_html($user->ID); ?>" doctorName="<?php echo esc_html($user->data->display_name); ?>">

            <div class="w-full text-left p-4 rounded-lg border transition-all border-gray-200 hover:border-purple-200">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <?php if (kcGetSingleWidgetSetting('showDoctorImage')) { ?>
                            <img src="<?php echo esc_url(!empty($user->user_image) ? $user->user_image : KIVI_CARE_DIR_URI . '/assets/images/kc-demo-img.png'); ?>"
                                alt="<?php echo esc_html($user->display_name); ?>" class="w-16 h-16 rounded-lg object-cover">
                        <?php } ?>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between">
                            <h3 class="text-base font-medium text-gray-900 h3-title">
                                <?php echo esc_html($user->display_name); ?>
                            </h3>
                            <span class="text-sm text-gray-500"> <?php echo esc_html($user->next_available); ?></span>
                        </div>
                        <?php if (kcGetSingleWidgetSetting('showDoctorDegree')) { ?>
                            <p class="mt-1 text-sm text-gray-500">
                                <?php echo !empty($user->qualifications) ? esc_html($user->qualifications) : ''; ?>
                            </p>
                        <?php } ?>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <?php if (kcGetSingleWidgetSetting('showDoctorSpeciality')) { ?>
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-50 text-purple-600">
                                    <?php echo esc_html($user->specialties_all); ?>
                                </span>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }


    public function widgetUserProfileCardExtraDetail($type, $user)
    {
        $temp = kcGetSingleWidgetSetting($type . 'ContactDetails');
        if (!empty($temp->id)) {
            switch ((int) $temp->id) {
                case 1:
                    ?>
                    <div class="mt-2">
                        <div class="d-flex align-items-center justify-content-sm-between flex-sm-row flex-column flex-wrap">
                            <h6><?php echo esc_html__('Email', 'kc-lang'); ?></h6>
                            <p class=""><?php echo esc_html($user->user_email); ?></p>
                        </div>
                        <?php
                        if (!empty($user->mobile_number)) {
                            ?>
                            <div class="d-flex align-items-center justify-content-sm-between flex-sm-row flex-column flex-wrap mt-md-0 mt-1">
                                <h6><?php echo esc_html__('Contact', 'kc-lang'); ?></h6>
                                <p class=""><?php echo esc_html('+' . $user->country_calling_code . ' ' . $user->mobile_number); ?></p>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                    <?php
                    break;
                case 2:
                    ?>
                    <div class="mt-2">
                        <?php
                        if (!empty($user->mobile_number)) {
                            ?>
                            <div class="d-flex align-items-center justify-content-sm-between flex-sm-row flex-column flex-wrap">
                                <h6><?php echo esc_html__('Contact', 'kc-lang'); ?></h6>
                                <p class=""><?php echo esc_html('+' . $user->country_calling_code . ' ' . $user->mobile_number); ?></p>
                            </div>
                            <?php
                        }
                        ?>
                    </div>
                    <?php
                    break;
                case 3:
                    ?>
                    <div class="mt-2">
                        <div class="d-flex align-items-center justify-content-sm-between flex-sm-row flex-column flex-wrap">
                            <h6><?php echo esc_html__('Email', 'kc-lang'); ?></h6>
                            <p class=""><?php echo esc_html($user->user_email); ?></p>
                        </div>
                    </div>
                    <?php
                    break;
            }
        }
    }

    public function appointmentTaxDetailHtml($request_data, $appointment_id = '')
    {
        $service_total_charge = $request_data['service_total_charge'];
        $clinic_currency_detail = $request_data['clinic_currency_detail'];
        $tax_details = $request_data['tax_details'];

        if (!empty($tax_details['tax_total'])) {
            $service_total_charge += $tax_details['tax_total'];
        }
        ?>
        <hr class="mb-0">
        <div class="d-flex justify-content-between align-items-center kc-total-price mt-4">
            <h5><?php echo esc_html__('Total Price', 'kc-lang'); ?></h5>
            <h5 class="iq-color-primary kc-services-total" id="services_total">
                <?php echo esc_html((!empty($clinic_currency_detail['prefix']) ? $clinic_currency_detail['prefix'] : '') . $service_total_charge . (!empty($clinic_currency_detail['postfix']) ? $clinic_currency_detail['postfix'] : '')); ?>
            </h5>
        </div>
        <?php
    }
}
