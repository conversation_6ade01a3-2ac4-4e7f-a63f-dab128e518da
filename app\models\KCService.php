<?php


namespace App\models;

use App\baseClasses\KCModel;

class KCService extends KCModel {

	public function __construct() {
		parent::__construct('services');
	}
	
	/**
	 * Check if user has permission to access/modify this service
	 *
	 * @param int $service_id Service ID
	 * @return bool
	 */
	public function serviceUserPermission($service_id) {
		$service = $this->get_by_id($service_id);
		
		if (empty($service)) {
			return false;
		}
		
		// If no doctor assigned, allow admin users
		if (empty($service->doctor_id)) {
			$user_role = $this->getLoginUserRole();
			return $user_role === 'administrator' || $user_role === $this->getClinicAdminRole();
		}
		
		return (new KCUser())->doctorPermissionUserWise($service->doctor_id);
	}
	
	/**
	 * Get services for a specific doctor and clinic
	 *
	 * @param int $doctor_id
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServicesByDoctorAndClinic($doctor_id, $clinic_id = null) {
		$conditions = ['doctor_id' => $doctor_id, 'status' => 1];
		
		if ($clinic_id) {
			$conditions['clinic_id'] = $clinic_id;
		}
		
		return $this->get_all($conditions);
	}
	
	/**
	 * Get services by clinic
	 *
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServicesByClinic($clinic_id) {
		return $this->get_all([
			'clinic_id' => $clinic_id,
			'status' => 1
		]);
	}
	
	/**
	 * Get services by category ID (updated for new category system)
	 *
	 * @param int $category_id
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServicesByCategoryId($category_id, $clinic_id = null) {
		$conditions = ['category_id' => $category_id, 'status' => 1];

		if ($clinic_id) {
			$conditions['clinic_id'] = $clinic_id;
		}

		return $this->get_all($conditions);
	}

	/**
	 * Get services by type/category (backward compatibility)
	 *
	 * @param string $type
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServicesByType($type, $clinic_id = null) {
		$conditions = ['type' => $type, 'status' => 1];

		if ($clinic_id) {
			$conditions['clinic_id'] = $clinic_id;
		}

		return $this->get_all($conditions);
	}
	
	/**
	 * Check if service name already exists for doctor/clinic combination
	 *
	 * @param string $name
	 * @param string $type
	 * @param int $doctor_id
	 * @param int $clinic_id
	 * @param int $exclude_id
	 * @return bool
	 */
	public function serviceExists($name, $type, $doctor_id, $clinic_id, $exclude_id = null) {
		global $wpdb;
		
		$table = $this->getTableName();
		$query = "SELECT id FROM {$table} WHERE name = %s AND type = %s AND doctor_id = %d AND clinic_id = %d";
		$params = [$name, $type, $doctor_id, $clinic_id];
		
		if ($exclude_id) {
			$query .= " AND id != %d";
			$params[] = $exclude_id;
		}
		
		$result = $wpdb->get_var($wpdb->prepare($query, $params));
		
		return !empty($result);
	}
	
	/**
	 * Get service categories from the new category system
	 *
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServiceCategories($clinic_id = null) {
		global $wpdb;

		$service_table = $this->getTableName();
		$category_table = $wpdb->prefix . 'kc_categories';

		$query = "SELECT DISTINCT
					c.id as category_id,
					c.name as category_name,
					c.slug as category_slug,
					c.visibility as category_visibility,
					COUNT(s.id) as service_count
				FROM {$category_table} c
				INNER JOIN {$service_table} s ON s.category_id = c.id
				WHERE s.status = 1
					AND c.module_type = 'service'
					AND c.status = 1
					AND c.visibility IN ('public', 'backend_only')";

		if ($clinic_id) {
			$query .= $wpdb->prepare(" AND s.clinic_id = %d", $clinic_id);
		}

		$query .= " GROUP BY c.id, c.name, c.slug, c.visibility
				   ORDER BY c.sort_order ASC, c.name ASC";

		return $wpdb->get_results($query);
	}

	/**
	 * Get unique service categories for a clinic (backward compatibility)
	 *
	 * @param int $clinic_id
	 * @return array
	 */
	public function getServiceCategoriesLegacy($clinic_id = null) {
		global $wpdb;

		$table = $this->getTableName();
		$query = "SELECT DISTINCT
					CASE
						WHEN service_name_alias IS NOT NULL AND service_name_alias != ''
						THEN service_name_alias
						ELSE type
					END as category,
					COUNT(*) as service_count
				FROM {$table}
				WHERE status = 1 AND type != 'system_service'";

		if ($clinic_id) {
			$query .= $wpdb->prepare(" AND clinic_id = %d", $clinic_id);
		}

		$query .= " GROUP BY category ORDER BY category";

		return $wpdb->get_results($query);
	}
	
	/**
	 * Get service with full details by mapping ID (for backward compatibility)
	 *
	 * @param int $mapping_id
	 * @return object|null
	 */
	public function getByMappingId($mapping_id) {
		return $this->get_var(['mapping_id' => $mapping_id], '*');
	}
	
	/**
	 * Update service charges for specific service
	 *
	 * @param int $service_id
	 * @param float $charges
	 * @return bool
	 */
	public function updateCharges($service_id, $charges) {
		return $this->update(
			['charges' => $charges],
			['id' => $service_id]
		);
	}
	
	/**
	 * Get services with search and pagination
	 *
	 * @param array $filters
	 * @param int $limit
	 * @param int $offset
	 * @return array
	 */
	public function getServicesWithPagination($filters = [], $limit = 10, $offset = 0) {
		global $wpdb;
		
		$table = $this->getTableName();
		$users_table = $wpdb->base_prefix . 'users';
		$clinic_table = $wpdb->prefix . 'kc_clinics';
		
		$where_conditions = ["s.status = 1"];
		$params = [];
		
		// Apply filters
		if (!empty($filters['search'])) {
			$search = '%' . $filters['search'] . '%';
			$where_conditions[] = "(s.name LIKE %s OR s.type LIKE %s OR u.display_name LIKE %s)";
			$params = array_merge($params, [$search, $search, $search]);
		}
		
		if (!empty($filters['doctor_id'])) {
			$where_conditions[] = "s.doctor_id = %d";
			$params[] = $filters['doctor_id'];
		}
		
		if (!empty($filters['clinic_id'])) {
			$where_conditions[] = "s.clinic_id = %d";
			$params[] = $filters['clinic_id'];
		}
		
		if (!empty($filters['type'])) {
			$where_conditions[] = "s.type = %s";
			$params[] = $filters['type'];
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = "SELECT s.*, u.display_name as doctor_name, c.name as clinic_name
				FROM {$table} s
				LEFT JOIN {$users_table} u ON u.ID = s.doctor_id
				LEFT JOIN {$clinic_table} c ON c.id = s.clinic_id
				WHERE {$where_clause}
				ORDER BY s.id DESC
				LIMIT %d OFFSET %d";
		
		$params[] = $limit;
		$params[] = $offset;
		
		return $wpdb->get_results($wpdb->prepare($query, $params));
	}
	
	/**
	 * Get total count for pagination
	 *
	 * @param array $filters
	 * @return int
	 */
	public function getServicesCount($filters = []) {
		global $wpdb;
		
		$table = $this->getTableName();
		$users_table = $wpdb->base_prefix . 'users';
		
		$where_conditions = ["s.status = 1"];
		$params = [];
		
		// Apply same filters as getServicesWithPagination
		if (!empty($filters['search'])) {
			$search = '%' . $filters['search'] . '%';
			$where_conditions[] = "(s.name LIKE %s OR s.type LIKE %s OR u.display_name LIKE %s)";
			$params = array_merge($params, [$search, $search, $search]);
		}
		
		if (!empty($filters['doctor_id'])) {
			$where_conditions[] = "s.doctor_id = %d";
			$params[] = $filters['doctor_id'];
		}
		
		if (!empty($filters['clinic_id'])) {
			$where_conditions[] = "s.clinic_id = %d";
			$params[] = $filters['clinic_id'];
		}
		
		if (!empty($filters['type'])) {
			$where_conditions[] = "s.type = %s";
			$params[] = $filters['type'];
		}
		
		$where_clause = implode(' AND ', $where_conditions);
		
		$query = "SELECT COUNT(*) 
				FROM {$table} s
				LEFT JOIN {$users_table} u ON u.ID = s.doctor_id
				WHERE {$where_clause}";
		
		return (int) $wpdb->get_var($wpdb->prepare($query, $params));
	}
}

