<template>
  <div class="kivi-booking-step" id="step-login-register">
    <div v-if="isCheckingAuth" class="kivi-auth-checking flex flex-col items-center justify-center py-12">
      <div class="kivi-spinner w-12 h-12 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mb-4"></div>
      <p class="text-gray-600 font-medium">Checking authentication status...</p>
    </div>
    <div v-else>
      <div class="text-center mb-8">
        <h2 class="kivi-step-title text-2xl font-bold text-gray-800 mb-2">{{ isLogin ? 'Welcome Back' : 'Create Account' }}</h2>
        <p class="kivi-step-subtitle text-gray-600">{{ isLogin ? 'Sign in to your account to continue booking' : 'Join us to book your appointment' }}</p>
      </div>

    <div class="">
      <!-- Tab Switcher -->
      <div class="flex space-x-4 mb-8 shadow-sm rounded-lg overflow-hidden">
        <button
          @click="isLogin = false"
          :class="['flex-1 py-3 px-6 font-medium transition-all duration-200 text-center', !isLogin ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200']"
        >
          Register
        </button>
        <button
          @click="isLogin = true"
          :class="['flex-1 py-3 px-6 font-medium transition-all duration-200 text-center', isLogin ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200']"
        >
          Login
        </button>
      </div>

      <!-- General Error Message -->
      <div v-if="errors.general" class="kivi-form-error-general mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
        {{ errors.general }}
      </div>

      <!-- Login Form -->
      <form v-if="isLogin" @submit.prevent="handleLogin" class="kivi-form" id="login-form">
        <div class="kivi-form-group mb-6">
          <label class="kivi-form-label font-medium" for="login-email">Email <span class="required">*</span></label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <input
              type="email"
              class="kivi-form-input w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="login-email"
              placeholder="Enter your email address"
              v-model="loginData.username"
              :class="{ 'border-red-500 bg-red-50': errors.username }"
              required
            >
          </div>
          <div class="kivi-form-error" v-if="errors.username">{{ errors.username }}</div>
        </div>

        <div class="kivi-form-group mb-6">
          <label class="kivi-form-label font-medium" for="login-password">Password <span class="required">*</span></label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <input
              :type="showPassword ? 'text' : 'password'"
              class="kivi-form-input w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="login-password"
              placeholder="Enter your password"
              v-model="loginData.password"
              :class="{ 'border-red-500 bg-red-50': errors.password }"
              required
            >
            <button
              type="button"
              @click="togglePassword"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 focus:outline-none"
              aria-label="Toggle password visibility"
            >
              <i :class="['fas', showPassword ? 'fa-eye-slash' : 'fa-eye']"></i>
            </button>
          </div>
          <div class="kivi-form-error" v-if="errors.password">{{ errors.password }}</div>
        </div>

        <div class="flex justify-end mb-6">
          <a :href="forgotPasswordUrl" target="_blank" class="text-sm text-purple-600 hover:text-purple-800 font-medium">
            Forgot Password?
          </a>
        </div>

        <!-- Login button is hidden as we're using common navigation buttons -->
        <button
          type="submit"
          class="kivi-btn kivi-btn-primary w-full mt-4 d-none"
          :disabled="isLoading"
          style="display: none;"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Logging in...
          </span>
          <span v-else>Login</span>
        </button>

        <div class="text-center mt-6 pt-4 border-t border-gray-100">
          <span class="kivi-text-muted">Don't have an account?</span>
          <button type="button" class="kivi-link ml-2 font-medium hover:underline" @click="isLogin = false">Register Now</button>
        </div>
      </form>

      <!-- Register Form -->
      <form v-else @submit.prevent="handleRegister" class="kivi-form" id="register-form">
        <!-- General Error Message for Register Form -->
        <div v-if="errors.general" class="kivi-form-error-general mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
          {{ errors.general }}
        </div>
        <!-- Name Fields -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium" for="register-firstname">First Name <span class="required">*</span></label>
            <input
              type="text"
              class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="register-firstname"
              placeholder="Enter your first name"
              v-model="registerData.first_name"
              :class="{ 'border-red-500 bg-red-50': errors.first_name }"
              required
            >
            <div class="kivi-form-error" v-if="errors.first_name">{{ errors.first_name }}</div>
          </div>

          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium" for="register-lastname">Last Name <span class="required">*</span></label>
            <input
              type="text"
              class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="register-lastname"
              placeholder="Enter your last name"
              v-model="registerData.last_name"
              :class="{ 'border-red-500 bg-red-50': errors.last_name }"
              required
            >
            <div class="kivi-form-error" v-if="errors.last_name">{{ errors.last_name }}</div>
          </div>
        </div>

        <!-- Email Field -->
        <div class="kivi-form-group mb-6">
          <label class="kivi-form-label font-medium" for="register-email">Email <span class="required">*</span></label>
          <input
            type="email"
            class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            id="register-email"
            placeholder="Enter your email"
            v-model="registerData.user_email"
            :class="{ 'border-red-500 bg-red-50': errors.user_email }"
            required
          >
          <div class="kivi-form-error" v-if="errors.user_email">{{ errors.user_email }}</div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Phone Field -->
          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium" for="register-phone">Contact <span class="required">*</span></label>
            <div class="contact-box-inline flex">
              <select
                v-model="registerData.country_code.countryCode"
                @change="updateCallingCode"
                class="w-1/4 px-4 py-3 border border-gray-300 rounded-l-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                id="CountryCode"
              >
                <option v-for="(country, index) in countries" :key="index + '-' + country.code" :value="country.code">
                  {{ country.dial_code }} - {{ country.name }}
                </option>
              </select>
              <input
                type="tel"
                class="w-3/4 px-4 py-3 border border-gray-300 rounded-r-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                id="register-phone"
                placeholder="Enter your contact number"
                v-model="registerData.mobile_number"
                :class="{ 'border-red-500 bg-red-50': errors.mobile_number }"
                required
              >
            </div>
            <div class="kivi-form-error" v-if="errors.mobile_number">{{ errors.mobile_number }}</div>
          </div>

          <!-- Date of Birth Field -->
          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium" for="register-dob">Date of Birth <span class="required">*</span></label>
            <input
              type="date"
              class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="register-dob"
              v-model="registerData.dob"
              :class="{ 'border-red-500 bg-red-50': errors.dob }"
              required
              style="text-transform: uppercase;"
            >
            <div class="kivi-form-error" v-if="errors.dob">{{ errors.dob }}</div>
          </div>

          <!-- NHS Number Field -->
          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium" for="register-nhs">NHS No.</label>
            <input
              type="text"
              class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              id="register-nhs"
              placeholder="Enter NHS No."
              v-model="registerData.nhs"
              :class="{ 'border-red-500 bg-red-50': errors.nhs }"
            >
            <div class="kivi-form-error" v-if="errors.nhs">{{ errors.nhs }}</div>
          </div>

          <!-- Gender Field -->
          <div class="kivi-form-group">
            <label class="kivi-form-label font-medium">Gender <span class="required">*</span></label>
            <div class="flex space-x-6 mt-2">
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value="male"
                  v-model="registerData.gender"
                  class="form-radio w-5 h-5 text-purple-600 focus:ring-purple-500"
                  required
                >
                <span class="ml-2 text-gray-700">Male</span>
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value="female"
                  v-model="registerData.gender"
                  class="form-radio w-5 h-5 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-gray-700">Female</span>
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value="other"
                  v-model="registerData.gender"
                  class="form-radio w-5 h-5 text-purple-600 focus:ring-purple-500"
                >
                <span class="ml-2 text-gray-700">Other</span>
              </label>
            </div>
            <div class="kivi-form-error" v-if="errors.gender">{{ errors.gender }}</div>
          </div>
        </div>

        <!-- Address Field -->
        <div class="kivi-form-group mb-6">
          <label class="kivi-form-label font-medium" for="register-address">Address <span class="required">*</span></label>
          <textarea
            class="kivi-form-input px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            id="register-address"
            rows="3"
            placeholder="Enter your address"
            v-model="registerData.address"
            :class="{ 'border-red-500 bg-red-50': errors.address }"
            required
          ></textarea>
          <div class="kivi-form-error" v-if="errors.address">{{ errors.address }}</div>
        </div>

        <!-- Identity Information Section (Always Visible) -->
        <div class="border rounded-lg overflow-hidden bg-gradient-to-r from-purple-50 to-indigo-50 shadow-sm transition-all duration-200 mb-6 mt-6">
          <div class="px-4 py-3 flex items-center justify-between bg-gradient-to-r from-purple-100 to-indigo-100">
            <div class="flex items-center space-x-3">
              <div class="p-1.5 rounded-full bg-white shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                  fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" class="w-5 h-5 text-purple-600">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900">GP Information</h3>
            </div>
          </div>
          <div class="p-5 border-t border-purple-100">
            <div class="space-y-5">
              <!-- GP Name Field -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                  Your registered GP Name <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black shadow-sm"
                  placeholder="Enter your GP's name"
                  v-model="registerData.registered_gp_name"
                  :class="{ 'border-red-500 bg-red-50': errors.registered_gp_name }"
                  required
                >
                <div class="kivi-form-error" v-if="errors.registered_gp_name">{{ errors.registered_gp_name }}</div>
              </div>

              <!-- GP Address Field -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 px-1 text-left">
                  Registered GP's address <span class="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-black shadow-sm"
                  placeholder="Enter your GP's address"
                  v-model="registerData.registered_gp_address"
                  :class="{ 'border-red-500 bg-red-50': errors.registered_gp_address }"
                  required
                >
                <div class="kivi-form-error" v-if="errors.registered_gp_address">{{ errors.registered_gp_address }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Register button is hidden as we're using common navigation buttons -->
        <button
          type="submit"
          class="kivi-btn kivi-btn-primary w-full mt-4 d-none"
          :disabled="isLoading"
          style="display: none;"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Registering...
          </span>
          <span v-else>Register</span>
        </button>

        <div class="text-center mt-6 pt-4 border-t border-gray-100">
          <span class="kivi-text-muted">Already have an account?</span>
          <button type="button" class="kivi-link ml-2 font-medium hover:underline" @click="isLogin = true">Login Now</button>
        </div>
      </form>
    </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom styles for the login/register step */
.kivi-form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.kivi-form-error-general {
  color: #dc2626;
  font-weight: 500;
}

.required {
  color: #ef4444;
}

.kivi-link {
  color: #9333ea;
  font-weight: 500;
}

.kivi-link:hover {
  color: #7e22ce;
}

.kivi-text-muted {
  color: #6b7280;
}

/* Animated background for the GP Information section */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Improve form field focus states */
input:focus, select:focus, textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4);
  border-color: #a855f7;
  transition: all 0.2s ease;
}

/* Custom radio button styling */
.form-radio {
  color: #9333ea;
  border-color: #d1d5db;
}

.form-radio:focus {
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4);
}

/* Fix for input fields with icons */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.left-0 {
  left: 0;
}

.right-0 {
  right: 0;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pointer-events-none {
  pointer-events: none;
}

/* Ensure proper spacing for inputs with icons */
input.pl-10 {
  padding-left: 2.5rem;
}

input.pr-10 {
  padding-right: 2.5rem;
}
</style>

<script>
// No imports needed

export default {
  name: 'LoginRegisterStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isLogin: true,
      isCheckingAuth: true,
      showPassword: false,
      showIdentitySection: true, // Always show identity section
      forgotPasswordUrl: window.ajaxData && window.ajaxData.lost_password_url ? window.ajaxData.lost_password_url : '/wp-login.php?action=lostpassword',
      initialNonce: '', // Store the initial nonce
      loginData: {
        username: '',
        password: '',
      },
      registerData: {
        first_name: '',
        last_name: '',
        user_email: '',
        mobile_number: '',
        gender: '',
        dob: '',
        nhs: '',
        address: '',
        registered_gp_name: '',
        registered_gp_address: '',
        country_code: {
          countryCode: 'US',
          countryCallingCode: '+1'
        },
        user_role: 'kiviCare_patient', // Default role
        clinic: []
      },
      countries: [],
      errors: {},
      isLoading: false
    };
  },
  created() {
    // Initialize the nonce
    this.initialNonce = this.getInitialNonce();
    console.log('Initial nonce stored:', this.initialNonce);

    // Load the country codes from the JSON file
    this.loadCountryCodes();

    // Set the clinic from the booking data
    if (this.bookingData.clinic && this.bookingData.clinic.id) {
      this.registerData.clinic = [{
        id: this.bookingData.clinic.id,
        name: this.bookingData.clinic.name || ''
      }];
    }

    // Check if we have appointment data in session storage
    this.checkAppointmentData();

    // Pre-fill form with patient data if available
    if (this.bookingData.patient) {
      this.registerData.first_name = this.bookingData.patient.name ? this.bookingData.patient.name.split(' ')[0] : '';
      this.registerData.last_name = this.bookingData.patient.name ? this.bookingData.patient.name.split(' ').slice(1).join(' ') : '';
      this.registerData.user_email = this.bookingData.patient.email || '';
      this.registerData.mobile_number = this.bookingData.patient.phone || '';
    }

    // Check if the user is already logged in
    this.checkLoggedInUser();
  },
  methods: {
    async loadCountryCodes() {
      try {
        // Try to load country codes from the server
        if (window.ajaxData && window.ajaxData.country_codes) {
          try {
            const countryCodes = JSON.parse(window.ajaxData.country_codes);
            if (Array.isArray(countryCodes) && countryCodes.length > 0) {
              this.countries = this.removeDuplicateCountryCodes(countryCodes);
              return;
            }
          } catch (parseError) {
            console.error('Error parsing country codes:', parseError);
          }
        }

        // Try to fetch from a JSON file
        try {
          const response = await fetch('/wp-content/plugins/kivicare-clinic-management-system/assets/helper_assets/CountryCodes.json');
          if (response.ok) {
            const data = await response.json();
            if (Array.isArray(data) && data.length > 0) {
              this.countries = this.removeDuplicateCountryCodes(data);
              return;
            }
          }
        } catch (fetchError) {
          console.error('Error fetching country codes:', fetchError);
        }

        // Empty fallback list for production
        this.countries = [];
      } catch (error) {
        console.error('Error loading country codes:', error);
      }
    },

    // Helper method to remove duplicate country codes
    removeDuplicateCountryCodes(countryCodes) {
      const uniqueCodes = {};
      return countryCodes.filter(country => {
        if (!uniqueCodes[country.code]) {
          uniqueCodes[country.code] = true;
          return true;
        }
        return false;
      });
    },

    updateCallingCode() {
      const country = this.countries.find(c => c.code === this.registerData.country_code.countryCode);
      if (country) {
        this.registerData.country_code.countryCallingCode = country.dial_code;
      }
    },

    checkAppointmentData() {
      // This method is simplified to just check for basic appointment data
      console.log('Checking appointment data');
    },

    async checkLoggedInUser() {
      try {
        this.isCheckingAuth = true;

        // Check if WordPress has a logged-in user via ajaxData
        if (window.ajaxData && window.ajaxData.is_user_logged_in === 'yes') {
          // Create a basic user object
          const userData = {
            ID: window.ajaxData.current_user_id || 'current',
            display_name: window.ajaxData.user_display_name || 'Current User',
            user_email: window.ajaxData.user_email || '',
          };

          // Update the booking data with user information
          this.bookingData.patient = {
            ...this.bookingData.patient,
            name: userData.display_name,
            email: userData.user_email,
            user_id: userData.ID
          };
          this.$emit('update:booking-data', this.bookingData);

          // Emit authenticated event
          this.$emit('user-authenticated', userData);
        }
      } catch (error) {
        console.error('Error checking logged in user:', error);
      } finally {
        this.isCheckingAuth = false;
      }
    },

    async handleLogin() {
      this.clearErrors();

      // Validate form
      let isValid = true;

      if (!this.loginData.username.trim()) {
        this.errors.username = 'Email is required';
        isValid = false;
      } else if (!this.isValidEmail(this.loginData.username)) {
        this.errors.username = 'Please enter a valid email address';
        isValid = false;
      }

      if (!this.loginData.password.trim()) {
        this.errors.password = 'Password is required';
        isValid = false;
      }

      if (!isValid) return;

      this.isLoading = true;

      try {
        // Get the WordPress AJAX URL and nonce
        const ajaxurl = window.ajaxurl || 'https://medroid.ai/ehr/wp-admin/admin-ajax.php';
        const nonce = this.getNonce();

        if (!nonce) {
          this.errors.general = 'Authentication error. Please refresh the page and try again.';
          return;
        }

        console.log('Using nonce for login:', nonce);

        // Login with the regular endpoint
        const params = {
          action: 'ajax_post',
          route_name: 'login',
          username: this.loginData.username.trim(),
          password: this.loginData.password,
          _ajax_nonce: nonce
        };

        // Make the API request
        const response = await fetch(ajaxurl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: new URLSearchParams(params)
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();

        if (responseData.status) {
          // Login successful
          console.log('Login successful');

          // Update the booking data with user information
          const userData = responseData.data;
          this.bookingData.patient = {
            name: userData.display_name,
            email: userData.user_email,
            phone: userData.mobile_number || '',
            user_id: userData.ID
          };

          // First update booking data
          this.$emit('update:booking-data', this.bookingData);

          // Then emit authenticated event to trigger the next step
          console.log('Emitting user-authenticated event after login');
          this.$emit('user-authenticated', userData);
        } else {
          // Login failed
          this.errors.general = responseData.message || 'Login failed. Please check your credentials.';
        }
      } catch (error) {
        console.error('Error during login:', error);
        this.errors.general = 'An error occurred. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },

    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // Identity section is now always visible

    async handleRegister() {
      this.clearErrors();

      // Validate form
      let isValid = this.validateRegistrationForm();
      if (!isValid) return;

      this.isLoading = true;

      try {
        // Get the WordPress AJAX URL and nonce
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = this.getNonce();
        console.log('Using nonce for registration:', nonce);

        // Ensure clinic data is set
        if (this.bookingData.clinic && this.bookingData.clinic.id) {
          this.registerData.clinic = [{
            id: this.bookingData.clinic.id,
            name: this.bookingData.clinic.name || ''
          }];
        }

        // Create FormData for the request
        const formData = this.createRegistrationFormData(nonce);

        // Make the API request
        const response = await fetch(ajaxurl, {
          method: 'POST',
          body: formData
        });

        const responseData = await response.json();

        if (responseData.status) {
          // Registration successful
          console.log('Registration successful:', responseData);

          // If tokens were returned, update them for future requests
          if (responseData.token) {
            // Update nonces in window objects
            if (window.ajaxData) {
              if (responseData.token.get) window.ajaxData.get_nonce = responseData.token.get;
              if (responseData.token.post) window.ajaxData.post_nonce = responseData.token.post;
              console.log('Updated nonces in window.ajaxData', window.ajaxData);
            }

            if (window.request_data) {
              if (responseData.token.get) window.request_data.get_nonce = responseData.token.get;
              if (responseData.token.post) window.request_data.nonce = responseData.token.post;
              console.log('Updated nonces in window.request_data', window.request_data);
            }

            // Also store the new nonce for our component
            this.initialNonce = responseData.token.post || this.initialNonce;
            console.log('Updated initial nonce:', this.initialNonce);
          }

          // Update the booking data with user information
          this.bookingData.patient = {
            name: `${this.registerData.first_name} ${this.registerData.last_name}`.trim(),
            email: this.registerData.user_email,
            phone: this.registerData.mobile_number,
            user_id: responseData.data?.ID || '',
            address: this.registerData.address,
            dob: this.registerData.dob,
            nhs: this.registerData.nhs,
            gender: this.registerData.gender,
            registered_gp_name: this.registerData.registered_gp_name,
            registered_gp_address: this.registerData.registered_gp_address
          };

          // First update booking data
          this.$emit('update:booking-data', this.bookingData);

          // Then emit authenticated event to trigger the next step with token information
          console.log('Emitting user-authenticated event after registration');
          this.$emit('user-authenticated', {
            ...responseData.data,
            token: responseData.token
          });
        } else {
          // Registration failed
          this.errors.general = responseData.message || 'Registration failed. Please try again.';
        }
      } catch (error) {
        console.error('Error during registration:', error);
        this.errors.general = 'An error occurred. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },

    clearErrors() {
      this.errors = {};
    },

    isValidEmail(email) {
      const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return re.test(String(email).toLowerCase());
    },

    // Helper method to get initial nonce from various sources
    getInitialNonce() {
      if (window.ajaxData && window.ajaxData.post_nonce) {
        return window.ajaxData.post_nonce;
      } else if (window.ajaxData && window.ajaxData.nonce) {
        return window.ajaxData.nonce;
      } else if (window.request_data && window.request_data.nonce) {
        return window.request_data.nonce;
      }
      return '';
    },

    // Helper method to get the stored nonce
    getNonce() {
      // Always return the initial nonce that was stored when the component was created
      return this.initialNonce;
    },

    // Helper method to validate registration form
    validateRegistrationForm() {
      let isValid = true;

      if (!this.registerData.first_name.trim()) {
        this.errors.first_name = 'First name is required';
        isValid = false;
      }

      if (!this.registerData.last_name.trim()) {
        this.errors.last_name = 'Last name is required';
        isValid = false;
      }

      if (!this.registerData.user_email.trim()) {
        this.errors.user_email = 'Email is required';
        isValid = false;
      } else if (!this.isValidEmail(this.registerData.user_email)) {
        this.errors.user_email = 'Please enter a valid email address';
        isValid = false;
      }

      if (!this.registerData.mobile_number.trim()) {
        this.errors.mobile_number = 'Phone number is required';
        isValid = false;
      }

      if (!this.registerData.gender) {
        this.errors.gender = 'Please select your gender';
        isValid = false;
      }

      if (!this.registerData.dob) {
        this.errors.dob = 'Date of birth is required';
        isValid = false;
      }

      if (!this.registerData.address.trim()) {
        this.errors.address = 'Address is required';
        isValid = false;
      }

      if (!this.registerData.registered_gp_name.trim()) {
        this.errors.registered_gp_name = 'GP name is required';
        isValid = false;
      }

      if (!this.registerData.registered_gp_address.trim()) {
        this.errors.registered_gp_address = 'GP address is required';
        isValid = false;
      }

      return isValid;
    },

    // Helper method to create registration form data
    createRegistrationFormData(nonce) {
      const formData = new FormData();

      // Add all register data to FormData
      formData.append('action', 'ajax_post');
      formData.append('route_name', 'register');
      formData.append('first_name', this.registerData.first_name);
      formData.append('last_name', this.registerData.last_name);
      formData.append('user_email', this.registerData.user_email);
      formData.append('mobile_number', this.registerData.mobile_number);
      formData.append('gender', this.registerData.gender);
      formData.append('dob', this.registerData.dob);
      formData.append('nhs', this.registerData.nhs || '');
      formData.append('address', this.registerData.address);
      formData.append('registered_gp_name', this.registerData.registered_gp_name);
      formData.append('registered_gp_address', this.registerData.registered_gp_address);
      formData.append('country_code', JSON.stringify(this.registerData.country_code));
      formData.append('clinic', JSON.stringify(this.registerData.clinic));
      formData.append('user_role', 'kiviCare_patient');
      formData.append('_ajax_nonce', nonce);
      formData.append('widgettype', 'new_appointment_widget');

      return formData;
    },

    // Helper method to prepare form data for submission
    prepareFormData(params) {
      const formData = {};

      // Handle simple key-value pairs
      Object.keys(params).forEach(key => {
        if (key !== 'service_list' && key !== 'custom_field' && key !== 'file') {
          formData[key] = params[key];
        }
      });

      // Handle service_list array
      if (params.service_list && Array.isArray(params.service_list)) {
        params.service_list.forEach((service, index) => {
          formData[`service_list[${index}]`] = service;
        });
      }

      // Handle custom_field object
      if (params.custom_field) {
        Object.keys(params.custom_field).forEach(key => {
          formData[`custom_field[${key}]`] = params.custom_field[key];
        });
      }

      return formData;
    },

    async loadConfirmationPage() {
      try {
        console.log('Loading confirmation page with booking data');
        const ajaxurl = window.ajaxurl || '/wp-admin/admin-ajax.php';
        const nonce = this.getNonce();
        console.log('Using nonce for confirmation page:', nonce);

        // Extract booking data
        const clinicId = this.bookingData.clinic.id;
        const doctorId = this.bookingData.doctor.id;
        const serviceList = this.bookingData.services.map(service => service.service_id);
        const time = this.bookingData.time;
        const date = this.bookingData.date;
        const description = this.bookingData.description || '';
        const customField = this.bookingData.customField || {};

        // Format time to AM/PM format if needed
        let formattedTime = time;
        if (formattedTime && !formattedTime.toLowerCase().includes('am') && !formattedTime.toLowerCase().includes('pm')) {
          const timeParts = formattedTime.split(':');
          const hours = parseInt(timeParts[0]);
          const minutes = timeParts[1] || '00';
          const ampm = hours >= 12 ? 'pm' : 'am';
          const formattedHours = hours % 12 || 12;
          formattedTime = `${formattedHours}:${minutes} ${ampm}`;
        }

        // Prepare API parameters
        const params = {
          action: 'ajax_post',
          route_name: 'appointment_confirm_page',
          clinic_id: clinicId,
          doctor_id: doctorId,
          service_list: serviceList,
          time: formattedTime,
          date: date,
          description: description,
          file: [],
          custom_field: customField,
          _ajax_nonce: nonce
        };

        // Make the API request
        const response = await fetch(ajaxurl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(params)
        });

        const responseData = await response.json();

        if (responseData.status) {
          console.log('Confirmation page loaded successfully');
          this.$emit('confirmation-page-loaded', {
            html: responseData.data,
            taxDetails: responseData.tax_details || []
          });
          return true;
        }

        console.log('No success response from confirmation page');
        return false;
      } catch (error) {
        console.error('Error loading confirmation page:', error);
        return false;
      }
    },
    }
  };
</script>

<style scoped>
.kivi-booking-step {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.kivi-form-error-general {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: rgba(254, 226, 226, 1);
  border: 1px solid rgba(248, 180, 180, 1);
  color: rgba(185, 28, 28, 1);
  font-size: 0.875rem;
}

.kivi-step-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
  color: var(--primary-color, #4f46e5);
}

.kivi-step-subtitle {
  font-size: 1rem;
  color: var(--gray, #6b7280);
  margin-bottom: 2rem;
  text-align: center;
}

.kivi-login-register-form {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
}

.kivi-form-group {
  margin-bottom: 1.5rem;
}

.kivi-form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--dark-gray, #374151);
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color, #4f46e5);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-form-input.error {
  border-color: var(--danger-color, #ef4444);
}

.kivi-form-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--danger-color, #ef4444);
}

.kivi-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kivi-btn-primary {
  background-color: var(--primary-color, #4f46e5);
  color: white;
}

.kivi-btn-primary:hover {
  background-color: var(--primary-dark-color, #4338ca);
}

.kivi-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.kivi-text-muted {
  color: var(--gray, #6b7280);
}

.kivi-link {
  color: var(--primary-color, #4f46e5);
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  text-decoration: underline;
}

.kivi-link:hover {
  color: var(--primary-dark-color, #4338ca);
}

.contact-box-inline {
  display: flex;
  gap: 0.5rem;
}

.w-full {
  width: 100%;
}

.w-1\/4 {
  width: 25%;
}

.w-3\/4 {
  width: 75%;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.required, .text-red-500 {
  color: var(--danger-color, #ef4444);
}

.text-purple-600 {
  color: var(--primary-color, #4f46e5);
}

.text-purple-700:hover {
  color: var(--primary-dark-color, #4338ca);
}

.kivi-auth-checking {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.kivi-auth-checking .kivi-spinner {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(79, 70, 229, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color, #4f46e5);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

.kivi-auth-checking p {
  font-size: 1rem;
  color: var(--gray, #6b7280);
  text-align: center;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-6 {
  gap: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.inline-flex {
  display: inline-flex;
}

.form-radio {
  appearance: none;
  width: 1rem;
  height: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  background-color: #fff;
  cursor: pointer;
}

.form-radio:checked {
  background-color: var(--primary-color, #4f46e5);
  border-color: var(--primary-color, #4f46e5);
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='4'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-radio:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.border {
  border: 1px solid rgba(229, 231, 235, 1);
}

.rounded-lg {
  border-radius: 0.5rem;
}

.overflow-hidden {
  overflow: hidden;
}

.bg-white {
  background-color: white;
}

.transition-all {
  transition-property: all;
}

.duration-200 {
  transition-duration: 200ms;
}

.p-4 {
  padding: 1rem;
}

.border-t {
  border-top: 1px solid rgba(229, 231, 235, 1);
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.hover\:bg-gray-50:hover {
  background-color: rgba(249, 250, 251, 1);
}

.p-1 {
  padding: 0.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.bg-gray-100 {
  background-color: rgba(243, 244, 246, 1);
}

.text-fuchsia-600 {
  color: rgb(192, 38, 211);
}

.text-lg {
  font-size: 1.125rem;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: rgba(17, 24, 39, 1);
}

.text-gray-400 {
  color: rgba(156, 163, 175, 1);
}

.rotate-90 {
  transform: rotate(90deg);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 640px) {
  .kivi-login-register-form {
    padding: 1.5rem;
  }

  .contact-box-inline {
    flex-direction: column;
    gap: 0.75rem;
  }

  .w-1\/4, .w-3\/4 {
    width: 100%;
  }
}
</style>