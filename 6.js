/*! For license information please see 6.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[6],{774:function(e,t,n){e.exports=function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:a,create:l}=Object,{apply:c,construct:s}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),s||(s=function(e,t){return new e(...t)});const u=w(Array.prototype.forEach),m=w(Array.prototype.lastIndexOf),p=w(Array.prototype.pop),f=w(Array.prototype.push),d=w(Array.prototype.splice),h=w(String.prototype.toLowerCase),g=w(String.prototype.toString),T=w(String.prototype.match),y=w(String.prototype.replace),E=w(String.prototype.indexOf),A=w(String.prototype.trim),_=w(Object.prototype.hasOwnProperty),S=w(RegExp.prototype.test),N=(b=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return s(b,t)});var b;function w(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function R(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:h;t&&t(e,null);let i=o.length;for(;i--;){let t=o[i];if("string"==typeof t){const e=r(t);e!==t&&(n(o)||(o[i]=e),t=e)}e[t]=!0}return e}function O(e){for(let t=0;t<e.length;t++)_(e,t)||(e[t]=null);return e}function D(t){const n=l(null);for(const[o,r]of e(t))_(t,o)&&(Array.isArray(r)?n[o]=O(r):r&&"object"==typeof r&&r.constructor===Object?n[o]=D(r):n[o]=r);return n}function L(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return w(n.get);if("function"==typeof n.value)return w(n.value)}e=o(e)}return function(){return null}}const v=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),C=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),x=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),k=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),I=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),M=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=i(["#text"]),z=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),P=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),H=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=a(/<%[\w\W]*|[\w\W]*%>/gm),G=a(/\$\{[\w\W]*/gm),Y=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),j=a(/^aria-[\-\w]+$/),X=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),q=a(/^(?:\w+script|data):/i),$=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=a(/^html$/i),V=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var J=Object.freeze({__proto__:null,ARIA_ATTR:j,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:V,DATA_ATTR:Y,DOCTYPE_NAME:K,ERB_EXPR:W,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:q,MUSTACHE_EXPR:B,TMPLIT_EXPR:G});const Z=1,Q=3,ee=7,te=8,ne=9,oe=function(){return"undefined"==typeof window?null:window},re=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;t&&t.hasAttribute("data-tt-policy-suffix")&&(n=t.getAttribute("data-tt-policy-suffix"));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}};return function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:oe();const o=e=>t(e);if(o.version="3.2.5",o.removed=[],!n||!n.document||n.document.nodeType!==ne||!n.Element)return o.isSupported=!1,o;let{document:r}=n;const a=r,c=a.currentScript,{DocumentFragment:s,HTMLTemplateElement:b,Node:w,Element:O,NodeFilter:B,NamedNodeMap:W=n.NamedNodeMap||n.MozNamedAttrMap,HTMLFormElement:G,DOMParser:Y,trustedTypes:j}=n,q=O.prototype,$=L(q,"cloneNode"),V=L(q,"remove"),ie=L(q,"nextSibling"),ae=L(q,"childNodes"),le=L(q,"parentNode");if("function"==typeof b){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let ce,se="";const{implementation:ue,createNodeIterator:me,createDocumentFragment:pe,getElementsByTagName:fe}=r,{importNode:de}=a;let he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};o.isSupported="function"==typeof e&&"function"==typeof le&&ue&&void 0!==ue.createHTMLDocument;const{MUSTACHE_EXPR:ge,ERB_EXPR:Te,TMPLIT_EXPR:ye,DATA_ATTR:Ee,ARIA_ATTR:Ae,IS_SCRIPT_OR_DATA:_e,ATTR_WHITESPACE:Se,CUSTOM_ELEMENT:Ne}=J;let{IS_ALLOWED_URI:be}=J,we=null;const Re=R({},[...v,...C,...x,...I,...U]);let Oe=null;const De=R({},[...z,...P,...H,...F]);let Le=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),ve=null,Ce=null,xe=!0,ke=!0,Ie=!1,Me=!0,Ue=!1,ze=!0,Pe=!1,He=!1,Fe=!1,Be=!1,We=!1,Ge=!1,Ye=!0,je=!1;const Xe="user-content-";let qe=!0,$e=!1,Ke={},Ve=null;const Je=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ze=null;const Qe=R({},["audio","video","img","source","image","track"]);let et=null;const tt=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),nt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",rt="http://www.w3.org/1999/xhtml";let it=rt,at=!1,lt=null;const ct=R({},[nt,ot,rt],g);let st=R({},["mi","mo","mn","ms","mtext"]),ut=R({},["annotation-xml"]);const mt=R({},["title","style","font","a","script"]);let pt=null;const ft=["application/xhtml+xml","text/html"],dt="text/html";let ht=null,gt=null;const Tt=r.createElement("form"),yt=function(e){return e instanceof RegExp||e instanceof Function},Et=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!gt||gt!==e){if(e&&"object"==typeof e||(e={}),e=D(e),pt=-1===ft.indexOf(e.PARSER_MEDIA_TYPE)?dt:e.PARSER_MEDIA_TYPE,ht="application/xhtml+xml"===pt?g:h,we=_(e,"ALLOWED_TAGS")?R({},e.ALLOWED_TAGS,ht):Re,Oe=_(e,"ALLOWED_ATTR")?R({},e.ALLOWED_ATTR,ht):De,lt=_(e,"ALLOWED_NAMESPACES")?R({},e.ALLOWED_NAMESPACES,g):ct,et=_(e,"ADD_URI_SAFE_ATTR")?R(D(tt),e.ADD_URI_SAFE_ATTR,ht):tt,Ze=_(e,"ADD_DATA_URI_TAGS")?R(D(Qe),e.ADD_DATA_URI_TAGS,ht):Qe,Ve=_(e,"FORBID_CONTENTS")?R({},e.FORBID_CONTENTS,ht):Je,ve=_(e,"FORBID_TAGS")?R({},e.FORBID_TAGS,ht):{},Ce=_(e,"FORBID_ATTR")?R({},e.FORBID_ATTR,ht):{},Ke=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,xe=!1!==e.ALLOW_ARIA_ATTR,ke=!1!==e.ALLOW_DATA_ATTR,Ie=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Me=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Ue=e.SAFE_FOR_TEMPLATES||!1,ze=!1!==e.SAFE_FOR_XML,Pe=e.WHOLE_DOCUMENT||!1,Be=e.RETURN_DOM||!1,We=e.RETURN_DOM_FRAGMENT||!1,Ge=e.RETURN_TRUSTED_TYPE||!1,Fe=e.FORCE_BODY||!1,Ye=!1!==e.SANITIZE_DOM,je=e.SANITIZE_NAMED_PROPS||!1,qe=!1!==e.KEEP_CONTENT,$e=e.IN_PLACE||!1,be=e.ALLOWED_URI_REGEXP||X,it=e.NAMESPACE||rt,st=e.MATHML_TEXT_INTEGRATION_POINTS||st,ut=e.HTML_INTEGRATION_POINTS||ut,Le=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Le.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&yt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Le.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Le.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ue&&(ke=!1),We&&(Be=!0),Ke&&(we=R({},U),Oe=[],!0===Ke.html&&(R(we,v),R(Oe,z)),!0===Ke.svg&&(R(we,C),R(Oe,P),R(Oe,F)),!0===Ke.svgFilters&&(R(we,x),R(Oe,P),R(Oe,F)),!0===Ke.mathMl&&(R(we,I),R(Oe,H),R(Oe,F))),e.ADD_TAGS&&(we===Re&&(we=D(we)),R(we,e.ADD_TAGS,ht)),e.ADD_ATTR&&(Oe===De&&(Oe=D(Oe)),R(Oe,e.ADD_ATTR,ht)),e.ADD_URI_SAFE_ATTR&&R(et,e.ADD_URI_SAFE_ATTR,ht),e.FORBID_CONTENTS&&(Ve===Je&&(Ve=D(Ve)),R(Ve,e.FORBID_CONTENTS,ht)),qe&&(we["#text"]=!0),Pe&&R(we,["html","head","body"]),we.table&&(R(we,["tbody"]),delete ve.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw N('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ce=e.TRUSTED_TYPES_POLICY,se=ce.createHTML("")}else void 0===ce&&(ce=re(j,c)),null!==ce&&"string"==typeof se&&(se=ce.createHTML(""));i&&i(e),gt=e}},At=R({},[...C,...x,...k]),_t=R({},[...I,...M]),St=function(e){let t=le(e);t&&t.tagName||(t={namespaceURI:it,tagName:"template"});const n=h(e.tagName),o=h(t.tagName);return!!lt[e.namespaceURI]&&(e.namespaceURI===ot?t.namespaceURI===rt?"svg"===n:t.namespaceURI===nt?"svg"===n&&("annotation-xml"===o||st[o]):Boolean(At[n]):e.namespaceURI===nt?t.namespaceURI===rt?"math"===n:t.namespaceURI===ot?"math"===n&&ut[o]:Boolean(_t[n]):e.namespaceURI===rt?!(t.namespaceURI===ot&&!ut[o])&&!(t.namespaceURI===nt&&!st[o])&&!_t[n]&&(mt[n]||!At[n]):!("application/xhtml+xml"!==pt||!lt[e.namespaceURI]))},Nt=function(e){f(o.removed,{element:e});try{le(e).removeChild(e)}catch(t){V(e)}},bt=function(e,t){try{f(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Be||We)try{Nt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},wt=function(e){let t=null,n=null;if(Fe)e="<remove></remove>"+e;else{const t=T(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===pt&&it===rt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=ce?ce.createHTML(e):e;if(it===rt)try{t=(new Y).parseFromString(o,pt)}catch(e){}if(!t||!t.documentElement){t=ue.createDocument(it,"template",null);try{t.documentElement.innerHTML=at?se:o}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),it===rt?fe.call(t,Pe?"html":"body")[0]:Pe?t.documentElement:i},Rt=function(e){return me.call(e.ownerDocument||e,e,B.SHOW_ELEMENT|B.SHOW_COMMENT|B.SHOW_TEXT|B.SHOW_PROCESSING_INSTRUCTION|B.SHOW_CDATA_SECTION,null)},Ot=function(e){return e instanceof G&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof W)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Dt=function(e){return"function"==typeof w&&e instanceof w};function Lt(e,t,n){u(e,e=>{e.call(o,t,n,gt)})}const vt=function(e){let t=null;if(Lt(he.beforeSanitizeElements,e,null),Ot(e))return Nt(e),!0;const n=ht(e.nodeName);if(Lt(he.uponSanitizeElement,e,{tagName:n,allowedTags:we}),e.hasChildNodes()&&!Dt(e.firstElementChild)&&S(/<[/\w!]/g,e.innerHTML)&&S(/<[/\w!]/g,e.textContent))return Nt(e),!0;if(e.nodeType===ee)return Nt(e),!0;if(ze&&e.nodeType===te&&S(/<[/\w]/g,e.data))return Nt(e),!0;if(!we[n]||ve[n]){if(!ve[n]&&xt(n)){if(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,n))return!1;if(Le.tagNameCheck instanceof Function&&Le.tagNameCheck(n))return!1}if(qe&&!Ve[n]){const t=le(e)||e.parentNode,n=ae(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=$(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,ie(e))}}return Nt(e),!0}return e instanceof O&&!St(e)?(Nt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!S(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ue&&e.nodeType===Q&&(t=e.textContent,u([ge,Te,ye],e=>{t=y(t,e," ")}),e.textContent!==t&&(f(o.removed,{element:e.cloneNode()}),e.textContent=t)),Lt(he.afterSanitizeElements,e,null),!1):(Nt(e),!0)},Ct=function(e,t,n){if(Ye&&("id"===t||"name"===t)&&(n in r||n in Tt))return!1;if(ke&&!Ce[t]&&S(Ee,t));else if(xe&&S(Ae,t));else if(!Oe[t]||Ce[t]){if(!(xt(e)&&(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,e)||Le.tagNameCheck instanceof Function&&Le.tagNameCheck(e))&&(Le.attributeNameCheck instanceof RegExp&&S(Le.attributeNameCheck,t)||Le.attributeNameCheck instanceof Function&&Le.attributeNameCheck(t))||"is"===t&&Le.allowCustomizedBuiltInElements&&(Le.tagNameCheck instanceof RegExp&&S(Le.tagNameCheck,n)||Le.tagNameCheck instanceof Function&&Le.tagNameCheck(n))))return!1}else if(et[t]);else if(S(be,y(n,Se,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==E(n,"data:")||!Ze[e])if(Ie&&!S(_e,y(n,Se,"")));else if(n)return!1;return!0},xt=function(e){return"annotation-xml"!==e&&T(e,Ne)},kt=function(e){Lt(he.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Ot(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Oe,forceKeepAttr:void 0};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=ht(a);let m="value"===a?c:A(c);if(n.attrName=s,n.attrValue=m,n.keepAttr=!0,n.forceKeepAttr=void 0,Lt(he.uponSanitizeAttribute,e,n),m=n.attrValue,!je||"id"!==s&&"name"!==s||(bt(a,e),m=Xe+m),ze&&S(/((--!?|])>)|<\/(style|title)/i,m)){bt(a,e);continue}if(n.forceKeepAttr)continue;if(bt(a,e),!n.keepAttr)continue;if(!Me&&S(/\/>/i,m)){bt(a,e);continue}Ue&&u([ge,Te,ye],e=>{m=y(m,e," ")});const f=ht(e.nodeName);if(Ct(f,s,m)){if(ce&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(l);else switch(j.getAttributeType(f,s)){case"TrustedHTML":m=ce.createHTML(m);break;case"TrustedScriptURL":m=ce.createScriptURL(m)}try{l?e.setAttributeNS(l,a,m):e.setAttribute(a,m),Ot(e)?Nt(e):p(o.removed)}catch(e){}}}Lt(he.afterSanitizeAttributes,e,null)},It=function e(t){let n=null;const o=Rt(t);for(Lt(he.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)Lt(he.uponSanitizeShadowNode,n,null),vt(n),kt(n),n.content instanceof s&&e(n.content);Lt(he.afterSanitizeShadowDOM,t,null)};return o.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,r=null,i=null,l=null;if(at=!e,at&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Dt(e)){if("function"!=typeof e.toString)throw N("toString is not a function");if("string"!=typeof(e=e.toString()))throw N("dirty is not a string, aborting")}if(!o.isSupported)return e;if(He||Et(t),o.removed=[],"string"==typeof e&&($e=!1),$e){if(e.nodeName){const t=ht(e.nodeName);if(!we[t]||ve[t])throw N("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof w)n=wt("\x3c!----\x3e"),r=n.ownerDocument.importNode(e,!0),r.nodeType===Z&&"BODY"===r.nodeName||"HTML"===r.nodeName?n=r:n.appendChild(r);else{if(!Be&&!Ue&&!Pe&&-1===e.indexOf("<"))return ce&&Ge?ce.createHTML(e):e;if(n=wt(e),!n)return Be?null:Ge?se:""}n&&Fe&&Nt(n.firstChild);const c=Rt($e?e:n);for(;i=c.nextNode();)vt(i),kt(i),i.content instanceof s&&It(i.content);if($e)return e;if(Be){if(We)for(l=pe.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(Oe.shadowroot||Oe.shadowrootmode)&&(l=de.call(a,l,!0)),l}let m=Pe?n.outerHTML:n.innerHTML;return Pe&&we["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&S(K,n.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+m),Ue&&u([ge,Te,ye],e=>{m=y(m,e," ")}),ce&&Ge?ce.createHTML(m):m},o.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Et(e),He=!0},o.clearConfig=function(){gt=null,He=!1},o.isValidAttribute=function(e,t,n){gt||Et({});const o=ht(e),r=ht(t);return Ct(o,r,n)},o.addHook=function(e,t){"function"==typeof t&&f(he[e],t)},o.removeHook=function(e,t){if(void 0!==t){const n=m(he[e],t);return-1===n?void 0:d(he[e],n,1)[0]}return p(he[e])},o.removeHooks=function(e){he[e]=[]},o.removeAllHooks=function(){he={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},o}()}()}}]);