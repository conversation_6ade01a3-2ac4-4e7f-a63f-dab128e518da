<template>
  <div class="kivi-booking-step" id="step-services">
    <h2 class="kivi-step-title"></h2>
    <p class="kivi-step-subtitle"></p>

    <div v-if="isLoading" class="kivi-loader-container">
      <div class="kivi-loader-circle"></div>
      <p class="kivi-loader-text">Loading services...</p>
    </div>

    <template v-else>
      <div class="kivi-form-group selected-category-info">
        <div class="kivi-form-label">Selected Category: <span id="selected-category-name">{{ bookingData.category ? bookingData.category.name : 'Please select a category' }}</span></div>
        <button type="button" class="kivi-btn kivi-btn-secondary" @click="changeCategory">Change</button>
      </div>

      <div class="kivi-form-group kivi-search-input">
        <div class="kivi-search-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input
          type="text"
          class="kivi-form-input"
          id="service-search"
          placeholder="Search services..."
          v-model="searchTerm"
        >
      </div>

      <div class="kivi-form-group">
        <div class="kivi-form-label">Filter By:</div>
        <div class="kivi-filter-buttons">
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'all' }"
            @click="filterServices('all')"
          >
            All
          </button>
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'virtual' }"
            @click="filterServices('virtual')"
          >
            Virtual
          </button>
          <button
            type="button"
            class="kivi-btn kivi-btn-secondary"
            :class="{ 'active': activeFilter === 'clinic' }"
            @click="filterServices('clinic')"
          >
            In-Clinic
          </button>
        </div>
      </div>

      <div class="kivi-grid" id="service-list">
        <div
          v-for="service in filteredServices"
          :key="service.id"
          class="kivi-card service-card"
          :class="{
            'selected': isServiceSelected(service.id),
            'disabled': !canSelectService(service),
            'virtual-service': service.telemed_service === 'yes',
            'clinic-service': service.telemed_service !== 'yes'
          }"
          :data-service-id="service.id"
          :data-service-type="service.telemed_service === 'yes' ? 'virtual' : 'clinic'"
          @click="toggleService(service)"
        >
          <!-- Service type indicator icon at top right -->
          <div class="kivi-service-type-indicator">
            <div v-if="service.telemed_service === 'yes'" class="kivi-service-type-icon virtual" title="Virtual Appointment">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <line x1="8" y1="21" x2="16" y2="21"></line>
                <line x1="12" y1="17" x2="12" y2="21"></line>
              </svg>
            </div>
            <div v-else class="kivi-service-type-icon clinic" title="In-Clinic Appointment">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
            </div>
          </div>

          <div class="kivi-card-header">
            <div>
              <h3 class="kivi-card-title">{{ service.name }}</h3>
              <div class="kivi-card-subtitle" v-if="service.doctor_name">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="doctor-icon">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                Dr. {{ service.doctor_name }}
              </div>
            </div>
            <div class="kivi-card-price pt-7">{{ service.charges }}</div>
          </div>

          <div class="kivi-card-body" v-if="service.description">
            <p>{{ service.description }}</p>
          </div>

          <div class="kivi-card-badges">
            <div class="kivi-badge kivi-badge-blue" v-if="service.duration">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="duration-icon">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              {{ formatDuration(service.duration) }}
            </div>
            <div class="kivi-badge kivi-badge-green" v-if="service.telemed_service === 'yes'">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="virtual-icon">
                <path d="M15.6 11.6L22 7v10l-6.4-4.5v-1zM4 5h9a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V7c0-1.1.9-2 2-2z"></path>
              </svg>
              Virtual
            </div>
            <div class="kivi-badge kivi-badge-purple" v-else>
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="clinic-icon">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="3" y1="9" x2="21" y2="9"></line>
                <line x1="9" y1="21" x2="9" y2="9"></line>
              </svg>
              In-Clinic
            </div>
          </div>

          <!-- Next available appointment with enhanced visual presentation -->
          <div class="kivi-next-available" v-if="service.next_available">
            <div class="kivi-next-available-label">
              <div class="kivi-next-available-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              </div>
              <span v-if="!service.next_available.includes('Next available')">Next available:</span>
            </div>
            <div class="kivi-next-available-date">
              <strong>{{ service.next_available.includes('Next available') ? service.next_available : service.next_available }}</strong>
              <span class="kivi-available-indicator"></span>
            </div>
          </div>

          <!-- Warning message for non-multiservice items -->
          <div class="kivi-service-warning" v-if="!canSelectService(service)">
            <p>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
              This service cannot be combined with other services.
            </p>
          </div>
        </div>
      </div>

      <div v-if="filteredServices.length === 0" class="kivi-empty-state">
        <div class="kivi-empty-state-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
        </div>
        <p class="kivi-empty-state-title">No services found</p>
        <p class="kivi-empty-state-subtitle">Try changing your search terms or selecting a different category.</p>
        <button type="button" class="kivi-btn kivi-btn-secondary kivi-empty-state-btn" @click="changeCategory">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          Return to categories
        </button>
      </div>
    </template>
  </div>
</template>

<script>
import { apiCall } from '../../../config/request';

export default {
  name: 'ServiceStep',
  props: {
    bookingData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      services: [],
      searchTerm: '',
      selectedServices: [],
      activeFilter: 'all',
      isLoading: false,
      fetchInProgress: false, // Flag to prevent multiple simultaneous API calls
      serviceCache: {} // Cache for service data by category ID
    };
  },
  computed: {
    filteredServices() {
      if (!this.services.length) return [];

      let result = this.services;

      // Apply service type filter
      if (this.activeFilter !== 'all') {
        const isVirtual = this.activeFilter === 'virtual';
        result = result.filter(service =>
          isVirtual ? service.telemed_service === 'yes' : service.telemed_service !== 'yes'
        );
      }

      // Apply search term filter
      if (this.searchTerm) {
        const term = this.searchTerm.toLowerCase();
        result = result.filter(service =>
          service.name.toLowerCase().includes(term) ||
          (service.description && service.description.toLowerCase().includes(term))
        );
      }

      return result;
    },

    // Check if any selected service has multiple=no
    hasSingleServiceSelected() {
      return this.selectedServices.some(service => service.multiple === 'no');
    },

    // Get ID of the single service if one is selected
    singleServiceId() {
      const singleService = this.selectedServices.find(service => service.multiple === 'no');
      return singleService ? singleService.id : null;
    }
  },
  watch: {
    'bookingData.category': {
      immediate: true,
      handler(newCategory) {
        if (newCategory && newCategory.id) {
          // Debounce the fetch call to prevent multiple rapid calls
          if (this.fetchDebounceTimer) {
            clearTimeout(this.fetchDebounceTimer);
          }

          this.fetchDebounceTimer = setTimeout(() => {
            this.fetchServicesWithCache(newCategory.id);
          }, 100); // 100ms debounce
        }
      }
    },

    'bookingData.services': {
      immediate: true,
      handler(newServices) {
        if (newServices && newServices.length) {
          this.selectedServices = [...newServices];
        } else {
          this.selectedServices = [];
        }
      }
    }
  },
  methods: {
    // New method that uses caching to prevent redundant API calls
    async fetchServicesWithCache(categoryId) {
      // If a fetch is already in progress, don't start another one
      if (this.fetchInProgress) {
        console.log('Fetch already in progress, skipping redundant call');
        return;
      }

      // Make sure we have a clinic ID
      if (!this.bookingData.clinic || !this.bookingData.clinic.id) {
        return;
      }

      this.fetchInProgress = true;
      this.isLoading = true;

      try {
        // Create a cache key that combines clinic ID and category ID
        const cacheKey = `${this.bookingData.clinic.id}_${categoryId}`;

        // Check if we have cached data for this clinic and category
        if (this.serviceCache[cacheKey]) {
          console.log('Using cached service data for category ID:', categoryId);
          this.services = [...this.serviceCache[cacheKey]];
        } else {
          // No cached data, fetch from API
          await this.fetchServices(categoryId);
        }
      } catch (error) {
        console.error('Error in fetchServicesWithCache:', error);
      } finally {
        this.isLoading = false;
        this.fetchInProgress = false;
      }
    },

    async fetchServices(categoryId) {
      if (!this.bookingData.clinic || !this.bookingData.clinic.id) {
        return;
      }

      try {
        this.services = [];

        console.log(`Fetching services for clinic ${this.bookingData.clinic.id} and category ${categoryId}`);

        // Use the get_clinic_service endpoint with correct parameters
        const params = {
          clinic_id: this.bookingData.clinic.id,
          service_category: categoryId, // This is the correct parameter name for the category
          format: 'json',
          widgetType: 'phpWidget' // Add this to identify this is coming from the widget
        };

        console.log('Service request params:', params);

        const response = await apiCall.get('get_clinic_service', { params });

        // Log the full response for debugging
        console.log('Raw service response:', response);
        console.log('Service response data:', response.data);

        if (response.data.status && response.data.data) {
          // Process services based on the response format
          if (Array.isArray(response.data.data)) {
            this.services = response.data.data.map(service => {
              // Normalize the service data to handle different field names
              // Get next available date using the previous method
              // Initialize next available date
              let nextAvailable = 'Tomorrow';

              // If we have HTML response, extract the next available date from it
              // This is the most reliable source as shown in the example
              if (response.data.html) {
                try {
                  // Create a temporary div to parse the HTML
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = response.data.html;

                  // Find all service items
                  const serviceItems = tempDiv.querySelectorAll('.service-item');

                  // Loop through service items to find the matching one
                  for (const item of serviceItems) {
                    const checkbox = item.querySelector(`input[id="service_${service.id}"]`);
                    if (checkbox) {
                      // Found the matching service, extract the next available date
                      const nextAvailableElement = item.querySelector('.mt-3.text-sm.text-gray-500');
                      if (nextAvailableElement && nextAvailableElement.textContent) {
                        nextAvailable = nextAvailableElement.textContent.trim();
                        break;
                      }
                    }
                  }
                } catch (error) {
                  console.error('Error extracting next available date from HTML:', error);
                }
              }
              // If we couldn't extract from HTML, use the API data
              else if (service.next_available_date) {
                // Make sure it has the "Next available:" prefix for consistency
                if (!service.next_available_date.includes('Next available:')) {
                  nextAvailable = `Next available: ${service.next_available_date}`;
                } else {
                  nextAvailable = service.next_available_date;
                }
              }
              // Fallback to a default value
              else {
                // Calculate based on current date
                const today = new Date();
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);

                // Format to readable date
                const options = { weekday: 'short', month: 'short', day: 'numeric' };
                const formattedDate = tomorrow.toLocaleDateString('en-US', options);
                nextAvailable = `Next available: ${formattedDate}`;
              }

              // If we have a share_link field, try to extract the next available date from it
              if (service.share_link && !service.next_available_date && nextAvailable === 'Tomorrow') {
                try {
                  // The share_link might contain a date parameter
                  const shareUrl = new URL(service.share_link);
                  const params = new URLSearchParams(shareUrl.search);

                  // Check if there's a date parameter
                  if (params.has('date')) {
                    const dateStr = params.get('date');
                    // Try to parse and format the date
                    const date = new Date(dateStr);
                    if (!isNaN(date.getTime())) {
                      const options = { month: 'long', day: 'numeric', year: 'numeric' };
                      nextAvailable = `Next available: ${date.toLocaleDateString('en-US', options)}`;
                    }
                  }
                } catch (error) {
                  console.error('Error parsing share_link:', error);
                }
              }

              return {
                ...service,
                id: service.id || service.mapping_id || service.service_id,
                name: service.name || service.service_name,
                charges: service.charges || service.service_base_price || service.price || '$0',
                duration: service.duration || '30',
                description: service.description || '',
                doctor_id: service.doctor_id || service.doctor_mapping_id || null,
                doctor_name: service.doctor_name || '',
                // Handle telemed_service field consistently
                telemed_service: service.telemed_service || (service.serviceType === 'virtual' ? 'yes' : 'no'),
                // Also store the service type for filtering
                serviceType: (service.telemed_service === 'yes' || service.serviceType === 'virtual' || service.service_type === 'virtual') ? 'virtual' : 'clinic',
                // Handle multiple service setting
                multiple: service.multiple || 'yes',
                // Add next available appointment time
                next_available: nextAvailable,
                // Store the original next_available_date for reference
                original_next_available_date: service.next_available_date || null
              };
            });

            // Cache the processed services
            const cacheKey = `${this.bookingData.clinic.id}_${categoryId}`;
            this.serviceCache[cacheKey] = [...this.services];

            console.log('Processed services:', this.services);
          } else if (typeof response.data.data === 'string' && response.data.data.includes('<')) {
            // Fall back to HTML response handling (for backward compatibility)
            console.log('Received HTML from get_clinic_service, trying service/clinic-services');

            const oldResponse = await apiCall.get('service/clinic-services', {
              params: {
                clinic_id: this.bookingData.clinic.id,
                service_category: categoryId,
                widgetType: 'phpWidget',
                module_type: 'vue'
              }
            });

            if (oldResponse.data.status && oldResponse.data.data) {
              this.services = oldResponse.data.data.map(service => ({
                ...service,
                id: service.id || service.service_id,
                name: service.name || service.service_name,
                charges: service.charges || service.price || '$0',
                duration: service.duration || '30',
                telemed_service: service.telemed_service || 'no',
                serviceType: service.telemed_service === 'yes' ? 'virtual' : 'clinic',
                multiple: service.multiple || 'yes'
              }));

              // Cache the processed services
              const cacheKey = `${this.bookingData.clinic.id}_${categoryId}`;
              this.serviceCache[cacheKey] = [...this.services];
            }
          } else if (response.data.html) {
            // If we got HTML in the 'html' field, we need to extract services from it
            console.log('Received HTML in html field, extracting services from HTML');

            try {
              // Create a temporary div to parse the HTML
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = response.data.html;

              // Find all service items
              const serviceItems = tempDiv.querySelectorAll('.service-item');
              const extractedServices = [];

              // Extract service data from each item
              serviceItems.forEach(item => {
                const checkbox = item.querySelector('input.card-checkbox');
                if (checkbox) {
                  // Extract service data from checkbox attributes
                  const serviceId = checkbox.getAttribute('id')?.replace('service_', '') || '';
                  const serviceName = checkbox.getAttribute('service_name') || '';
                  const servicePrice = checkbox.getAttribute('service_price') || '';
                  const doctorId = checkbox.getAttribute('doctor_id') || '';
                  const clinicId = checkbox.getAttribute('clinic_id') || '';
                  const doctorName = checkbox.getAttribute('doctor_name') || '';
                  const serviceType = checkbox.getAttribute('service_type') || '';
                  const multiple = checkbox.getAttribute('multipleService') || 'yes';

                  // Extract next available date
                  const nextAvailableElement = item.querySelector('.mt-3.text-sm.text-gray-500');
                  let nextAvailable = nextAvailableElement ? nextAvailableElement.textContent.trim() : 'Check availability';

                  // Make sure we don't have duplicate "Next available:" text
                  if (!nextAvailable.includes('Next available:') && nextAvailable !== 'Check availability') {
                    nextAvailable = `Next available: ${nextAvailable}`;
                  }

                  // Extract duration
                  const durationElement = item.querySelector('.inline-flex.items-center.px-2.py-0.5.rounded.text-xs.bg-gray-100');
                  const durationText = durationElement ? durationElement.textContent.trim() : '';
                  const duration = durationText.match(/(\d+)\s*min/) ? durationText.match(/(\d+)\s*min/)[1] : '30';

                  // Create service object
                  extractedServices.push({
                    id: serviceId,
                    service_id: checkbox.getAttribute('service_id') || serviceId,
                    name: serviceName,
                    charges: servicePrice,
                    duration: duration,
                    doctor_id: doctorId,
                    clinic_id: clinicId,
                    doctor_name: doctorName,
                    telemed_service: serviceType === 'virtual' ? 'yes' : 'no',
                    serviceType: serviceType,
                    multiple: multiple,
                    next_available: nextAvailable
                  });
                }
              });

              if (extractedServices.length > 0) {
                console.log('Successfully extracted services from HTML:', extractedServices);
                this.services = extractedServices;

                // Cache the processed services
                const cacheKey = `${this.bookingData.clinic.id}_${categoryId}`;
                this.serviceCache[cacheKey] = [...this.services];
              } else {
                console.warn('No services found in HTML');
                this.services = [];
              }
            } catch (error) {
              console.error('Error extracting services from HTML:', error);
              this.services = [];
            }
          }

          console.log('Processed services:', this.services);
        }
      } catch (error) {
        console.error('Error fetching services:', error);
      }
    },

    formatDuration(duration) {
      if (!duration) return '';

      // Convert duration to minutes if it's not already
      const minutes = parseInt(duration);
      if (isNaN(minutes)) return '';

      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;

      if (hours > 0) {
        return `${hours}h ${mins > 0 ? mins + 'min' : ''}`;
      }

      return `${mins} min`;
    },

    filterServices(filterType) {
      this.activeFilter = filterType;
    },

    isServiceSelected(serviceId) {
      return this.selectedServices.some(service => service.id === serviceId);
    },

    canSelectService(service) {
      // If this service has multiple=no
      if (service.multiple === 'no') {
        // Only allow if no other services are selected
        return this.selectedServices.length === 0 ||
               (this.selectedServices.length === 1 && this.selectedServices[0].id === service.id);
      }

      // If a single service is already selected, don't allow adding more
      if (this.hasSingleServiceSelected) {
        return this.singleServiceId === service.id;
      }

      return true;
    },

    toggleService(service) {
      // Check if we can select this service
      if (!this.canSelectService(service)) {
        return;
      }

      console.log('Toggling service:', service);

      // Check if service is already selected
      const index = this.selectedServices.findIndex(s => s.id === service.id);

      if (index === -1) {
        // Add service to selection with normalized data - ensuring the format matches the expected API parameters
        const serviceToAdd = {
          id: service.id,
          service_id: service.service_id || service.id, // Ensure we have the correct service_id
          name: service.name,
          price: service.charges,
          type: service.telemed_service === 'yes' ? 'virtual' : 'clinic',
          duration: service.duration || '30',
          multiple: service.multiple || 'yes',
          doctor_id: service.doctor_id || null,
          clinic_id: this.bookingData.clinic.id,
          doctor_name: service.doctor_name || null,
          category: this.bookingData.category ? this.bookingData.category.name : null,
          telemed_service: service.telemed_service || 'no',
          charges: service.charges // Make sure we include the original charges field
        };

        // Log the doctor_id for debugging
        console.log('Service doctor_id:', service.doctor_id);
        if (!service.doctor_id) {
          console.warn('No doctor_id found for service:', service.id, service.name);
        }

        console.log('Adding service to selection:', serviceToAdd);
        this.selectedServices.push(serviceToAdd);

        // Demonstrate how to format this service for the get_time_slots endpoint
        // This format matches the example endpoint parameters
        // We'll log it for now, but this would be used in the date/time selection step
        const timeSlotParams = {
          doctor_id: service.doctor_id,
          clinic_id: this.bookingData.clinic.id,
          date: new Date().toISOString().split('T')[0], // Format: YYYY-MM-DD
          widgetType: 'phpWidget'
        };

        // Add service parameters in array format with index [0], [1], etc.
        timeSlotParams[`service[0][id]`] = service.id;
        timeSlotParams[`service[0][service_id]`] = service.service_id || service.id;
        timeSlotParams[`service[0][name]`] = service.name;
        timeSlotParams[`service[0][charges]`] = service.charges;

        console.log('Time slot params format for this service:', timeSlotParams);
      } else {
        // Remove service from selection
        console.log('Removing service from selection:', this.selectedServices[index]);
        this.selectedServices.splice(index, 1);
      }

      // Update parent component with full service details
      // This ensures we have all necessary information for the next steps

      // Get the doctor information from the selected service
      let doctorInfo = null;
      if (this.selectedServices.length > 0) {
        const service = this.selectedServices[0];
        if (service.doctor_id) {
          doctorInfo = {
            id: service.doctor_id,
            name: service.doctor_name || 'Doctor'
          };
          console.log('Setting doctor info from service:', doctorInfo);
        }
      }

      this.$emit('update:booking-data', {
        ...this.bookingData,
        services: [...this.selectedServices],
        doctor: doctorInfo || this.bookingData.doctor
      });

      // Removed auto-advance - user must click Next button
    },

    changeCategory() {
      this.$emit('go-to-step', 1); // Go back to category step
    }
  }
};
</script>

<style scoped>
.kivi-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--black);
}

.kivi-step-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.kivi-form-group {
  margin-bottom: 1rem;
}

.selected-category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--light-gray);
  border-radius: var(--radius);
}

.kivi-form-label {
  font-weight: 500;
  color: var(--dark-gray);
}

.kivi-search-input {
  position: relative;
}

.kivi-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray);
}

.kivi-search-input input {
  padding-left: 2.5rem;
}

.kivi-form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.kivi-form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.kivi-filter-buttons {
  display: flex;
  gap: 0.5rem;
}

.kivi-filter-buttons button {
  padding: 0.375rem 0.75rem;
}

.kivi-filter-buttons button.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.kivi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.kivi-card {
  border: 2px solid rgba(229, 231, 235, 1);
  border-radius: var(--radius);
  padding: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.kivi-card:hover:not(.disabled) {
  border-color: rgba(79, 70, 229, 0.4);
  background-color: rgba(79, 70, 229, 0.02);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kivi-card.selected {
  border-color: var(--primary-color);
  background-color: rgba(79, 70, 229, 0.05);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.kivi-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Next available styling */
.kivi-next-available {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px dashed rgba(229, 231, 235, 0.8);
  color: var(--gray);
  font-size: 0.8125rem;
}

.kivi-next-available-label {
  display: flex;
  align-items: center;
}

.kivi-next-available-icon {
  display: flex;
  margin-right: 0.375rem;
  color: var(--primary-color);
}

.kivi-next-available-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kivi-next-available-date strong {
  color: var(--dark-gray);
  font-weight: 600;
}

.kivi-available-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10B981;
  position: relative;
}

.kivi-available-indicator:after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background-color: rgba(16, 185, 129, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  70% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.7;
  }
}

.kivi-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.kivi-card-title {
  font-weight: 600;
  color: var(--black);
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.kivi-card-subtitle {
  font-size: 0.75rem;
  color: var(--gray);
}

.kivi-card-price {
  font-weight: 600;
  color: var(--primary-color);
}

.kivi-card-body {
  font-size: 0.875rem;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-card-footer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.kivi-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgba(29, 78, 216, 1);
}

.kivi-badge-green {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgba(5, 150, 105, 1);
}

.kivi-badge-purple {
  background-color: rgba(124, 58, 237, 0.1);
  color: rgba(109, 40, 217, 1);
}

.kivi-card-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.kivi-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.5;
}

.kivi-service-warning {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--danger-color);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  backdrop-filter: blur(2px);
  border-radius: var(--radius);
}

.kivi-service-warning p {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.kivi-service-warning svg {
  color: var(--danger-color);
}

.kivi-card.disabled:hover .kivi-service-warning {
  opacity: 1;
}

.kivi-service-type-indicator {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  z-index: 5;
}

.kivi-service-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kivi-service-type-icon.virtual {
  color: rgba(16, 185, 129, 0.9);
  background-color: rgba(16, 185, 129, 0.1);
}

.kivi-service-type-icon.clinic {
  color: rgba(124, 58, 237, 0.9);
  background-color: rgba(124, 58, 237, 0.1);
}

.doctor-icon, .virtual-icon, .clinic-icon, .duration-icon {
  margin-right: 0.25rem;
}

.virtual-service {
  border-left: 4px solid rgba(16, 185, 129, 0.7);
}

.clinic-service {
  border-left: 4px solid rgba(124, 58, 237, 0.7);
}

.kivi-card-subtitle {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--gray);
  margin-top: 0.25rem;
}

.kivi-loader-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem 0;
  gap: 1rem;
}

.kivi-loader-circle {
  border: 3px solid rgba(229, 231, 235, 1);
  border-radius: 50%;
  border-top: 3px solid var(--primary-color);
  width: 2.5rem;
  height: 2.5rem;
  animation: kivi-spin 1s linear infinite;
}

.kivi-loader-text {
  color: var(--gray);
  font-size: 0.875rem;
  font-weight: 500;
}

.kivi-empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem 1rem;
  color: var(--gray);
  background-color: rgba(243, 244, 246, 0.5);
  border-radius: var(--radius);
  min-height: 16rem;
  text-align: center;
}

.kivi-empty-state-icon {
  color: var(--gray);
  margin-bottom: 1rem;
  opacity: 0.4;
}

.kivi-empty-state-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.kivi-empty-state-subtitle {
  font-size: 0.875rem;
  color: var(--gray);
  margin-bottom: 1.5rem;
  max-width: 20rem;
}

.kivi-empty-state-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius);
  border: 1px solid rgba(229, 231, 235, 1);
  background-color: white;
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.kivi-empty-state-btn:hover {
  background-color: rgba(243, 244, 246, 1);
  border-color: rgba(209, 213, 219, 1);
}

@keyframes kivi-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .kivi-grid {
    grid-template-columns: 1fr;
  }

  .kivi-filter-buttons {
    flex-wrap: wrap;
  }
}
</style>