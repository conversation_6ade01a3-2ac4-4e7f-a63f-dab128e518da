<?php
/**
 * Migration: Merge Services and Service Doctor Mapping Tables
 * File: merge_services_tables.php
 */

if (!defined('ABSPATH')) {
    exit;
}

class MergeServicesTables {
    /**
     * Run the migration - merges services and mapping tables into single table
     */
    public function up() {
        global $wpdb;

        error_log("[Migration] Starting services tables merge");

        $charset_collate = $wpdb->get_charset_collate();

        $services_table = $wpdb->prefix . 'kc_services';
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $services_backup = $wpdb->prefix . 'kc_services_backup';
        $mapping_backup = $wpdb->prefix . 'kc_service_doctor_mapping_backup';

        try {
            // Check if migration is already done
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM `{$services_table}` LIKE 'doctor_id'");
            if (!empty($column_exists)) {
                error_log("[Migration] Services tables already merged, skipping");
                return true;
            }

            // Check if mapping table exists
            $mapping_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$mapping_table}'") === $mapping_table;
            if (!$mapping_table_exists) {
                error_log("[Migration] Mapping table doesn't exist, skipping migration");
                return true;
            }

            // Start transaction
            $wpdb->query('START TRANSACTION');

            // Step 1: Create backup tables
            $wpdb->query("CREATE TABLE IF NOT EXISTS `{$services_backup}` AS SELECT * FROM `{$services_table}`");
            $wpdb->query("CREATE TABLE IF NOT EXISTS `{$mapping_backup}` AS SELECT * FROM `{$mapping_table}`");

            // Step 2: Add new columns to services table
            $new_columns = [
                'charges' => 'varchar(50) DEFAULT "0"',
                'doctor_id' => 'bigint(20) unsigned DEFAULT 0',
                'clinic_id' => 'int(11) DEFAULT 0',
                'duration' => 'int(11) DEFAULT 0',
                'image' => 'text NULL',
                'description' => 'varchar(255) NULL',
                'telemed_service' => 'varchar(10) DEFAULT "no"',
                'service_name_alias' => 'varchar(191) NULL',
                'multiple' => 'varchar(10) DEFAULT "yes"',
                'extra' => 'longtext NULL',
                'mapping_id' => 'bigint(20) DEFAULT 0'
            ];

            foreach ($new_columns as $column => $definition) {
                $wpdb->query("ALTER TABLE `{$services_table}` ADD COLUMN `{$column}` {$definition}");
            }

            // Step 3: Migrate data from mapping table to services table
            $migration_query = "
                INSERT INTO `{$services_table}` (
                    name, type, price, status, created_at, 
                    charges, doctor_id, clinic_id, duration, image, 
                    description, telemed_service, service_name_alias, 
                    multiple, extra, mapping_id
                )
                SELECT 
                    s.name, s.type, s.price, m.status, s.created_at,
                    m.charges, m.doctor_id, m.clinic_id, 
                    COALESCE(m.duration, 0) as duration,
                    m.image, m.description, 
                    COALESCE(m.telemed_service, 'no') as telemed_service,
                    m.service_name_alias,
                    COALESCE(m.multiple, 'yes') as multiple,
                    m.extra, m.id as mapping_id
                FROM `{$mapping_table}` m
                JOIN `{$services_table}` s ON m.service_id = s.id
                WHERE NOT EXISTS (
                    SELECT 1 FROM `{$services_table}` existing 
                    WHERE existing.mapping_id = m.id
                )
            ";

            $result = $wpdb->query($migration_query);

            if ($result === false) {
                throw new Exception('Failed to migrate data from mapping table to services table');
            }

            // Step 4: Update appointment service mappings to use new service IDs
            // NOTE: The service_id in appointment mappings refers to the OLD mapping table ID
            $appointment_service_mapping_table = $wpdb->prefix . 'kc_appointment_service_mapping';

            if ($wpdb->get_var("SHOW TABLES LIKE '{$appointment_service_mapping_table}'")) {
                $update_appointments_query = "
                    UPDATE `{$appointment_service_mapping_table}` asm
                    JOIN `{$services_table}` s ON s.mapping_id = asm.service_id
                    SET asm.service_id = s.id
                    WHERE s.mapping_id > 0
                ";
                $wpdb->query($update_appointments_query);
            }

            // Step 5: Update bill items to use new service IDs
            $bill_items_table = $wpdb->prefix . 'kc_bill_items';

            if ($wpdb->get_var("SHOW TABLES LIKE '{$bill_items_table}'")) {
                $update_bills_query = "
                    UPDATE `{$bill_items_table}` bi
                    JOIN `{$services_table}` s ON s.mapping_id = bi.item_id
                    SET bi.item_id = s.id
                    WHERE bi.type = 'service' AND s.mapping_id > 0
                ";
                $wpdb->query($update_bills_query);
            }

            // Step 6: Add indexes for better performance
            $wpdb->query("ALTER TABLE `{$services_table}` ADD INDEX `idx_doctor_clinic` (`doctor_id`, `clinic_id`)");
            $wpdb->query("ALTER TABLE `{$services_table}` ADD INDEX `idx_type_status` (`type`, `status`)");
            $wpdb->query("ALTER TABLE `{$services_table}` ADD INDEX `idx_mapping_id` (`mapping_id`)");

            // Step 7: Remove old service records that don't have mapping data
            $wpdb->query("DELETE FROM `{$services_table}` WHERE mapping_id = 0 AND doctor_id = 0");

            // Commit transaction
            $wpdb->query('COMMIT');

            // Log successful migration
            if (function_exists('kcLogActivity')) {
                kcLogActivity(
                    'services_migration',
                    'Services tables merged successfully',
                    [
                        'migrated_records' => $result,
                        'timestamp' => current_time('Y-m-d H:i:s')
                    ]
                );
            }

            error_log("[Migration] Services tables merged successfully. Migrated {$result} records");
            return true;

        } catch (Exception $e) {
            // Rollback transaction
            $wpdb->query('ROLLBACK');

            // Log error
            error_log('[Migration] Services migration failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Reverse the migration - restores original table structure
     */
    public function down() {
        global $wpdb;

        error_log("[Migration] Rolling back services tables merge");

        $services_table = $wpdb->prefix . 'kc_services';
        $mapping_table = $wpdb->prefix . 'kc_service_doctor_mapping';
        $services_backup = $wpdb->prefix . 'kc_services_backup';
        $mapping_backup = $wpdb->prefix . 'kc_service_doctor_mapping_backup';

        try {
            $wpdb->query('START TRANSACTION');

            // Restore original tables from backups
            if ($wpdb->get_var("SHOW TABLES LIKE '{$services_backup}'")) {
                $wpdb->query("DROP TABLE IF EXISTS `{$services_table}`");
                $wpdb->query("CREATE TABLE `{$services_table}` AS SELECT * FROM `{$services_backup}`");
            }

            if ($wpdb->get_var("SHOW TABLES LIKE '{$mapping_backup}'")) {
                $wpdb->query("DROP TABLE IF EXISTS `{$mapping_table}`");
                $wpdb->query("CREATE TABLE `{$mapping_table}` AS SELECT * FROM `{$mapping_backup}`");
            }

            $wpdb->query('COMMIT');

            error_log("[Migration] Services tables rollback completed successfully");
            return true;

        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            error_log('[Migration] Services tables rollback failed: ' . $e->getMessage());
            return false;
        }
    }
}