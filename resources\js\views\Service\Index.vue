<template>
  <div class="min-h-screen p-6 bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header -->
    <div class="mb-8 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <button
          class="flex items-center gap-2 px-4 py-2 bg-black text-sm text-white rounded-lg shadow-sm hover:bg-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="m12 19-7-7 7-7"></path>
            <path d="M19 12H5"></path>
          </svg>
          <span>Back</span>
        </button>
        <h1 class="text-2xl font-semibold text-gray-800">
          {{ formTranslation.service.service_list }}
        </h1>
      </div>
      <div class="flex gap-3">

        <module-data-import v-if="
          userData.addOns.kiviPro &&
          kcCheckPermission('service_add') &&
          kivicareCompareVersion(requireProVersion, userData.pro_version)" ref="module_data_import"
          @reloadList="getServiceData" :required-data="[
            {
              label: formTranslation.service.category,
              value: 'category',
              required: true,
              type: 'select'
            },
            {
              label: formTranslation.service.name,
              value: 'name',
              required: true
            },
            {
              label: formTranslation.service.charges,
              value: 'charges',
              required: true,
              type: 'number',
              min: 0
            },
            {
              label: formTranslation.service.doctor + ' ' + formTranslation.service.id,
              value: 'doctor_id',
              required: false,
              type: 'select',
              multiple: true
            },
            {
              label: formTranslation.common.description,
              value: 'description',
              required: false,
              type: 'textarea'
            },
            {
              label: formTranslation.patient_encounter.duration,
              value: 'duration',
              required: false,
              type: 'number',
              min: 0
            },
            {
              label: formTranslation.service.status,
              value: 'status',
              required: false,
              type: 'select',
              options: [
                { label: formTranslation.common.active, value: 1 },
                { label: formTranslation.common.inactive, value: 0 }
              ]
            },
            {
              label: formTranslation.service.duration,
              value: 'duration',
              required: false,
              type: 'number',
              min: 0
            },
            {
              label: formTranslation.service.description,
              value: 'description',
              required: false,
              type: 'textarea'
            }
          ]" :module-name="formTranslation.common.service" module-type="service" :validate-before-import="true"
          :show-progress="true" />

        <module-data-export v-if="kcCheckPermission('service_export')" :module-data="serviceList.data"
          :module-name="formTranslation.service.service_list" module-type="services">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" x2="12" y1="15" y2="3"></line>
          </svg>
          {{ formTranslation.common.import }}
        </module-data-export>

        <button v-if="kcCheckPermission('service_add')"
          class="flex items-center gap-2 px-4 py-2 text-sm text-white bg-black rounded-lg hover:bg-gray-800 transition"
          @click="handleServiceDataForm({})">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
            <path d="M5 12h14"></path>
            <path d="M12 5v14"></path>
          </svg>
          {{
            visible
              ? formTranslation.service.close_form_btn
              : formTranslation.service.add_service_btn
          }}
        </button>
      </div>
    </div>

    <!-- Global Search -->
    <div class="relative mb-6">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input v-model="searchQuery" @input="globalFilter" :placeholder="formTranslation.common.search_service_field_data_global_placeholder
        "
        class="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" />
    </div>

    <!-- Detailed Filters -->
    <div class="grid grid-cols-6 gap-4 mb-6">
      <input placeholder="ID"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" v-model="filters.id" @input="handleFilterChange" />
      <input placeholder="Filter by name"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" v-model="filters.name" @input="handleFilterChange" />
      <input placeholder="Filter by clinic"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" v-model="filters.clinic" @input="handleFilterChange" />
      <input placeholder="Filter by doctor"
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        type="text" v-model="filters.doctor" @input="handleFilterChange" />
      <select
        class="px-4 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400"
        v-model="filters.status" @change="handleFilterChange">
        <option value="">Filter by status</option>
        <option value="1">Active</option>
        <option value="0">Inactive</option>
      </select>
    </div>

    <!-- checkbox feature -->
    <div v-if="selectedRows.length > 0"
      class="flex items-center justify-between px-6 py-3 bg-white border-b border-gray-200">
      <div class="flex items-center gap-4">
        <span class="text-sm text-gray-600">
          {{ selectedRows.length }}
          {{ selectedRows.length === 1 ? "Row" : "Rows" }} selected
        </span>
        <button @click="clearSelection" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
          Clear
        </button>
      </div>
      <div class="flex items-center gap-3">
        <select v-model="globalCheckboxApplyData.action_perform"
          class="px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400">
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="delete">Delete</option>
        </select>
        <button @click="handleBulkAction"
          class="px-4 py-1.5 text-sm text-white bg-purple-600 rounded-lg hover:bg-purple-700 transition">
          Apply
        </button>
      </div>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto">
      <!-- Loader -->
      <div v-if="pageLoader" class="page-loader-section">
        <loader-component-2></loader-component-2>
      </div>

      <table v-else class="w-full">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300" :checked="allSelected" @change="selectAllRows" />
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              #
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Clinic Name
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Doctor
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Charges
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Duration
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Category
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Action
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr v-for="(service, index) in serviceList.data" :key="service.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <input type="checkbox" class="rounded border-gray-300" :checked="selectedRows.includes(service.id)"
                @change="handleRowSelection(service.id)" />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ index + 1 }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <img v-if="service.image" :src="service.image" :alt="service.name" class="w-8 h-8 rounded-full mr-3" />
                <div v-else class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  {{ getInitials(service.clinic_name) }}
                </div>
                {{ service.name }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ service.clinic_name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ service.doctor_name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ service.charges }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ formatDuration(service.duration) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              {{ service.service_type }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <div v-if="kcCheckPermission('service_edit')"
                  class="relative w-10 h-5 flex items-center rounded-full p-1 cursor-pointer transition-colors ease-in-out duration-200"
                  :class="service.status === '1' ? 'bg-black' : 'bg-gray-300'" @click="confirmStatusChange(service)">
                  <div class="w-4 h-4 rounded-full bg-white transform transition-transform duration-200" :class="service.status === '1' ? 'translate-x-4' : 'translate-x-0'
                    "></div>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium" :class="service.status === '1'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
                  ">
                  {{
                    service.status === "1"
                      ? formTranslation.common.active
                      : formTranslation.common.inactive
                  }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex gap-2">
                <button v-if="kcCheckPermission('service_edit')" class="p-1 hover:bg-gray-100 rounded"
                  @click="editServiceData(service)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-4 h-4 text-gray-600">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                  </svg>
                </button>
                <button type="button" v-b-tooltip.hover :title="copyToolTipText"
                  v-if="kcCheckPermission('service_edit')" @click="CopyLink(service.share_link)"
                  class="p-1 hover:bg-gray-100 rounded "
                  @mouseout="copyToolTipText = formTranslation.settings.click_to_copy">
                  <i class="far fa-lg fa-copy"></i>
                </button>
                <button v-if="
                  kcCheckPermission('service_delete') &&
                  !['telemed', 'Telemed'].includes(service.name)
                " class="p-1 hover:bg-gray-100 rounded" @click="confirmDelete(service)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="w-4 h-4 text-red-500">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" x2="10" y1="11" y2="17"></line>
                    <line x1="14" x2="14" y1="11" y2="17"></line>
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-700">Rows per page:</span>
          <select v-model.number="perPage" @change="handlePerPageChange" class="border border-gray-300 rounded-md text-sm p-1">
            <option :value="10">10</option>
            <option :value="25">25</option>
            <option :value="50">50</option>
            <option :value="-1">ALL</option>
          </select>
        </div>
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-700">
            <template v-if="perPage === -1">
              Showing all {{ totalRows }} records
            </template>
            <template v-else>
              Page {{ currentPage }} of {{ totalPages }}
            </template>
          </span>
          <div class="flex gap-2" v-if="perPage !== -1">
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="currentPage === 1"
              @click="handlePageChange(currentPage - 1)">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-5 h-5 text-gray-600">
                <path d="m15 18-6-6 6-6"></path>
              </svg>
            </button>
            <button class="p-1 rounded hover:bg-gray-100 disabled:opacity-50" :disabled="currentPage === totalPages"
              @click="handlePageChange(currentPage + 1)">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="w-5 h-5 text-gray-600">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Service Modal -->
    <div v-if="isServiceModalOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title"
      role="dialog" aria-modal="true">
      <!-- Modal Backdrop -->
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>

      <!-- Modal Container -->
      <div class="flex min-h-screen items-center justify-center p-4 text-center sm:p-0">
        <div
          class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-3xl">
          <!-- Modal Header -->
          <div class="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">
              {{
                serviceId === -1
                  ? formTranslation.service.add_service_btn
                  : formTranslation.common.edit_service
              }}
            </h3>
            <button @click="closeServiceModal" class="text-gray-400 hover:text-gray-500">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div class="px-6 py-4">
            <Create :selectedServiceData="selectedServiceData" :serviceId="serviceId" @getServiceData="getServiceData"
              @serviceSaved="handleServiceSave" @closeForm="closeServiceModal" @getServicesListData="getServiceData" />
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import VueTimepicker from "vue2-timepicker";
import "vue2-timepicker/dist/VueTimepicker.css";
import { post, get } from "../../config/request";
import Create from "../Service/Create";

export default {
  components: { Create, VueTimepicker },
  data: () => ({
    visible: false,
    pageLoader: true,
    currentPage: 1,
    perPage: 10,
    totalRows: 0,
    serviceList: {
      data: [],
      column: [],
    },
    sessionEdit: false,
    serviceId: null,
    editSessionDataIndex: "",
    searchQuery: "",
    selectedRows: [],
    showImportModal: false,
    allSelected: false,
    selectedServiceData: {},
    isServiceModalOpen: false,
    copyToolTipText: "",
    oldServerParams: {
      searchTerm: "",
      perPage: 10,
      columnFilters: {},
    },
    filters: {
      id: "",
      doctor: "",
      clinic: "",
      name: "",
      date: "",
      status: "",
    },
    serverParams: {
      columnFilters: {
        service_type: "",
        duration: "",
      },
      sort: [
        {
          field: "",
          type: "",
        },
      ],
      page: 1,
      perPage: 10,
      searchTerm: "",
      type: "list",
    },
    serviceCategory: [],
    globalCheckboxApplyData: {
      action_perform: "inactive",
      module: "doctor_service",
      data: [],
      route_name: "module_wise_multiple_data_update",
      ajax_nonce: "cf7b342506",
    },
    globalCheckboxApplyDataActions: [
      { value: "active", label: "Active" },
      { value: "inactive", label: "Inactive" },
      { value: "delete", label: "Delete" },
    ],
  }),
  mounted() {
    this.getServiceCategoryType();
    this.init();
  },
  methods: {
    // changeDurationHandler(event) {
    //   const duration = parseInt(event.data.HH) * 60 + parseInt(event.data.mm);
    //   this.serverParams.columnFilters.duration = duration.toString();
    // },
    init: function () {
      this.serviceList = this.defaultServiceList();
      this.getServiceData();
      this.globalCheckboxApplyData = this.defaultGlobalCheckboxApplyData();
      this.globalCheckboxApplyDataActions =
        this.defaultGlobalCheckboxApplyDataActions();
    },

    clearSelection() {
      this.selectedRows = [];
      this.allSelected = false;
      this.globalCheckboxApplyData.data = [];
    },

    handleFilterChange() {
      this.pageLoader = true;

      const columnFilters = {
        id: this.filters.id || "",
        service_id: "", // Add if you have this filter
        name: this.filters.name || "",
        clinic_name: this.filters.clinic || "",
        doctor_name: this.filters.doctor || "",
        charges: "", // Add if you have this filter
        duration: "", // Add if you have this filter
        service_type: "", // Add if you have this filter
        status: this.filters.status || "",
      };

      const filters = {};
      Object.keys(this.filters).forEach((key) => {
        if (this.filters[key]) {
          filters[key] = this.filters[key];
        }
      });

      // No filters and no search term - early return
      if (Object.keys(filters).length === 0 && !this.searchQuery) {
        this.pageLoader = false;
        return;
      }

      // Update params with both filters and current search term
      this.updateParams({
        columnFilters,
        sort: [{ field: "", type: "" }], // Match exact structure
        page: 1,
        perPage: this.perPage,
        searchTerm: this.searchQuery || "",
        type: "list",
      });
    },

    // Checkbox handling
    selectAllRows() {
      this.allSelected = !this.allSelected;
      this.selectedRows = this.allSelected
        ? this.serviceList.data.map((row) => row.id)
        : [];
      this.globalCheckboxApplyData.data = this.selectedRows;
    },

    handleRowSelection(id) {
      const index = this.selectedRows.indexOf(id);
      if (index === -1) {
        this.selectedRows.push(id);
      } else {
        this.selectedRows.splice(index, 1);
      }
      this.allSelected =
        this.selectedRows.length === this.serviceList.data.length;
      this.globalCheckboxApplyData.data = this.selectedRows;
    },

    // Bulk actions
    async handleBulkAction() {
      if (!this.selectedRows.length) return;

      try {
        const result = await this.$swal.fire({
          title: "Are you sure?",
          text: "This action cannot be undone!",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, proceed!",
        });

        if (result.isConfirmed) {
          // this.pageLoader = true;

          // Get the full data for selected rows
          const selectedRowsData = this.serviceList.data.filter((row) =>
            this.selectedRows.includes(row.id)
          );

          const payload = {
            ...this.globalCheckboxApplyData,
            data: {
              selectedRows: selectedRowsData,
            },
          };

          const response = await post(
            "module_wise_multiple_data_update",
            payload
          );

          if (response.data.status) {
            await this.$swal.fire("Success!", response.data.message, "success");
            this.getServiceData();
            this.clearSelection();
          } else {
            this.$swal.fire("Error!", response.data.message, "error");
          }
        }
      } catch (error) {
        console.error(error);
        this.$swal.fire("Error!", "Operation failed", "error");
      } finally {
        this.pageLoader = false;
      }
    },
    defaultServiceList: function () {
      return {
        column: [
          {
            field: "id",
            label: this.formTranslation.common.id,
            width: "100px",
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            field: "service_id",
            label:
              this.formTranslation.widget_setting.service_setting +
              " " +
              this.formTranslation.common.id,
            width: "120px",
            filterOptions: {
              enabled: true,
              placeholder:
                this.formTranslation.widget_setting.service_setting +
                " " +
                this.formTranslation.common.id,
              filterValue: "",
            },
          },
          {
            field: "name",
            width: "150px",
            label: this.formTranslation.service.dt_lbl_name,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_name_fltr,
              filterValue: "",
            },
          },
          {
            field: "clinic_name",
            width: "150px",
            label: this.formTranslation.patient_encounter.dt_lbl_clinic,
            filterOptions: {
              enabled: !(
                window.request_data.current_user_role ===
                "kiviCare_clinic_admin" ||
                window.request_data.current_user_role ===
                "kiviCare_receptionist"
              ),
              placeholder:
                this.formTranslation.patient_encounter.dt_plh_fltr_by_clinic,
              filterValue: "",
            },
          },
          {
            field: "doctor_name",
            width: "150px",
            label: this.formTranslation.service.dt_lbl_doctor,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_fltr_by_doc,
              filterValue: "",
            },
          },
          {
            field: "charges",
            width: "150px",
            label: this.formTranslation.service.dt_lbl_charges,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_fltr_by_price,
              filterValue: "",
            },
          },
          {
            field: "duration",
            width: "150px",
            label: this.formTranslation.patient_encounter.duration,
            hidden: this.userData.addOns.kiviPro !== true,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.patient_encounter.duration,
              filterValue: "",
              // filterDropdownItems:[
              //   {value:5,text:'5min'},
              //   {value:10,text:'10min'},
              //   {value:15,text:'15min'},
              //   {value:20,text:'20min'},
              //   {value:25,text:'25min'},
              //   {value:30,text:'30min'},
              //   {value:35,text:'35min'},
              //   {value:40,text:'40min'},
              //   {value:45,text:'45min'},
              //   {value:50,text:'50min'},
              //   {value:55,text:'55min'},
              //   {value:60,text:'1hr'},
              //   {value:75,text:'1hr 15min'},
              //   {value:90,text:'1hr 30min'},
              //   {value:105,text:'1hr 45min'},
              //   {value:120,text:'2hr'},
              //   {value:150,text:'2hr 30min'}
              // ]
            },
          },
          {
            field: "service_type",
            label: this.formTranslation.service.dt_lbl_category,
            filterOptions: {
              enabled: true,
              placeholder: this.formTranslation.service.dt_plh_name_fltr,
              filterDropdownItems: this.serviceCategory,
              filterValue: "",
            },
          },
          {
            field: "status",
            label: this.formTranslation.service.dt_lbl_status,
            filterOptions: {
              enabled: true,
              placeholder:
                this.formTranslation.static_data.dt_lbl_plh_sr_fltr_status,
              filterValue: "",
              filterDropdownItems: [
                { value: "1", text: this.formTranslation.common.active },
                { value: "0", text: this.formTranslation.common.inactive },
              ],
            },
          },
          {
            field: "actions",
            sortable: false,
            label: this.formTranslation.service.dt_lbl_action,
          },
        ],
        data: [],
      };
    },
    getServiceData() {
      this.pageLoader = true;
      const params = {
        columnFilters: this.serverParams.columnFilters,
        sort: this.serverParams.sort,
        page: this.serverParams.page,
        perPage: this.serverParams.perPage,
        searchTerm: this.serverParams.searchTerm,
        type: "list",
      };

      get("service_list", params)
        .then((response) => {
          this.pageLoader = false;
          if (response.data.status) {
            this.serviceList.data = response.data.data;
            this.totalRows = response.data.total_rows;
          } else {
            this.serviceList.data = [];
            this.totalRows = 0;
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          console.error("Error fetching service data:", error);
          this.$swal("Error!", "Failed to fetch services", "error");
        });
    },

    resetSearch() {
      this.searchQuery = "";
      this.oldServerParams.searchTerm = "";
      this.updateParams({
        searchTerm: "",
        page: 1,
      });
    },

    defaultGlobalCheckboxApplyData() {
      return {
        action_perform: "delete",
        module: "doctor_service",
        data: [],
      };
    },

    defaultGlobalCheckboxApplyDataActions: function () {
      return [
        {
          value: "active",
          label: this.formTranslation.service.dt_active,
        },
        {
          value: "inactive",
          label: this.formTranslation.service.dt_inactive,
        },
        {
          value: "delete",
          label: this.formTranslation.clinic_schedule.dt_lbl_dlt,
        },
      ];
    },

    // Updated delete confirmation and execution
    confirmDelete(service) {
      this.$swal
        .fire({
          title: "Are you sure?",
          text: "This action cannot be undone!",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#d33",
          cancelButtonColor: "#3085d6",
          confirmButtonText: "Yes, delete it!",
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.deleteService(service);
          }
        });
    },

    deleteService(service) {
      this.pageLoader = true;
      get("service_delete", { id: service.id })
        .then((response) => {
          this.pageLoader = false;
          if (response.data.status) {
            this.$swal.fire("Deleted!", response.data.message, "success");
            this.getServiceData();
          } else {
            this.$swal.fire("Error!", response.data.message, "error");
          }
        })
        .catch((error) => {
          this.pageLoader = false;
          this.$swal.fire("Error!", "Failed to delete service", "error");
          console.error(error);
        });
    },

    // Updated status change confirmation
    confirmStatusChange(service) {
      const newStatus = service.status === "1" ? "0" : "1";
      const statusText = newStatus === "1" ? "activate" : "deactivate";

      this.$swal
        .fire({
          title: "Confirm Status Change",
          text: `Are you sure you want to ${statusText} this service?`,
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, change it!",
        })
        .then((result) => {
          if (result.isConfirmed) {
            this.changeModuleValueStatus({
              module_type: "doctor_service",
              id: service.id,
              value: newStatus,
            });
          }
        });
    },

    // Updated pagination handlers
    handlePageChange(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        this.serverParams.page = page;
        this.getServiceData();
      }
    },

    handlePerPageChange() {
      // Handle the "ALL" option
      if (this.perPage === -1) {
        // Update params with current filters and search
        this.updateParams({
          page: 1,
          perPage: 0, // Send 0 to backend to indicate ALL records
          searchTerm: this.searchQuery,
          columnFilters: this.serverParams.columnFilters,
        });
      } else {
        // Update params with current filters and search
        this.updateParams({
          page: 1,
          perPage: this.perPage,
          searchTerm: this.searchQuery,
          columnFilters: this.serverParams.columnFilters,
        });
      }
    },

    globalCheckboxApply() {
      this.pageLoader = true;
      post("module_wise_multiple_data_update", this.globalCheckboxApplyData)
        .then((data) => {
          this.pageLoader = false;
          if (data.data.status !== undefined && data.data.status === true) {
            displayMessage(data.data.message);
            this.getServiceData();
          } else {
            displayErrorMessage(data.data.message);
          }
        })
        .catch((error) => {
          this.pageLoader = true;
          console.log(error);
        });
    },

    onPageChange(params) {
      this.updateParams({ page: params.currentPage });
    },

    onPerPageChange(params) {
      if (this.oldServerParams.perPage === params.currentPerPage) {
        return;
      }
      this.oldServerParams.perPage = params.currentPerPage;
      this.updateParams({
        perPage: params.currentPerPage,
        page: params.currentPage,
      });
    },

    onSortChange(params) {
      this.updateParams({
        sort: params,
      });
    },
    globalFilter: _.debounce(function () {
      this.pageLoader = true;
      // Don't make API call if search term hasn't changed
      if (this.oldServerParams.searchTerm === this.searchQuery) {
        this.pageLoader = false;
        return;
      }

      // Update old params to track changes
      this.oldServerParams = {
        ...this.oldServerParams,
        searchTerm: this.searchQuery,
      };

      // Update server params
      this.updateParams({
        searchTerm: this.searchQuery,
        perPage: this.serverParams.perPage,
        page: 1, // Reset to first page on new search
      });
    }, 300),
    // Helper method to update params and fetch data
    updateParams(newProps) {
      this.serverParams = {
        ...this.serverParams,
        ...newProps,
        type: "list", // Ensure this is always set
      };

      // Update serverParams to match exact API structure
      const params = {
        columnFilters: this.serverParams.columnFilters,
        sort: this.serverParams.sort,
        page: this.serverParams.page,
        perPage: this.serverParams.perPage,
        searchTerm: this.serverParams.searchTerm,
        type: "list",
      };

      // Fetch new data with updated params
      this.getServiceData(params);
    },

    getServiceCategoryType() {
      let _this = this;
      get("get_static_data", {
        data_type: "static_data_with_label",
        static_data_type: "service_type",
      })
        .then((response) => {
          if (
            response.data.status !== undefined &&
            response.data.status === true
          ) {
            this.serviceCategory = [];
            if (response.data.data.length > 0) {
              let checkExists = false;
              response.data.data.map(function (value, key) {
                if (value.id == "system_service") {
                  checkExists = true;
                }
                _this.serviceCategory.push({
                  value: value.id,
                  text: value.label,
                });
              });
              if (!checkExists) {
                this.serviceCategory.push({
                  value: "system_service",
                  text: "System Service",
                });
              }
            }
          } else {
            displayErrorMessage(response.data.message);
          }
        })
        .catch((error) => {
          console.log(error);
          displayErrorMessage(
            this.formTranslation.common.internal_server_error
          );
        });
    },

    handleServiceDataForm(service = {}) {
      this.serviceId = service.id || -1;
      this.selectedServiceData = {};
      this.isServiceModalOpen = true;
    },

    editServiceData(service) {
      this.serviceId = service.id;
      this.selectedServiceData = { ...service }; // Clone the service data
      this.isServiceModalOpen = true;
    },

    closeServiceModal() {
      this.isServiceModalOpen = false;
      this.selectedServiceData = {};
      this.serviceId = -1;
    },
    closeForm() {
      this.isServiceModalOpen = false;
      this.visible = false;
      this.serviceId = -1;
      this.selectedServiceData = {};
    },
    // Handle successful save
    handleServiceSave(savedService) {
      this.getServiceData();
      this.closeServiceModal();
    },
    formatDuration(duration) {
      if (!duration) {
        return "";
      }
      if (duration === 0 || duration === "0") {
        return "0min (No block)";
      }
      if (parseInt(duration) > 0) {
        var hours = Math.floor(duration / 60);
        var minutes = duration % 60;
        minutes += "min";
        if (hours > 0) {
          return hours + "hr " + minutes;
        }
        return minutes;
      }

      return duration;
    },
    getInitials(name) {
      if (name !== undefined && name !== "" && name !== null) {
        const patient_name = name.split(" ");
        const initials = patient_name.map((patient_name) =>
          patient_name.charAt(0).toUpperCase()
        );
        return initials.join("");
      } else {
        return " - ";
      }
    },
    CopyLink(share_link) {
      const elem = document.createElement("input");
      document.querySelector("body").appendChild(elem);
      elem.value = share_link;
      elem.select();
      document.execCommand("copy");
      elem.remove();
      this.copyToolTipText = this.formTranslation.common.copied;
    },
  },
  computed: {
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage);
    },
    servicesListExport() {
      return "Services List - " + moment().format("YYYY-MM-DD");
    },
    userData() {
      return this.$store.state.userDataModule.user;
    },
  },
  watch: {
    filters: {
      deep: true,
      handler: _.debounce(function () {
        this.handleFilterChange();
      }, 300),
    },
  },
};
</script>
