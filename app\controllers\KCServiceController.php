<?php

namespace App\Controllers;

use App\baseClasses\KCBase;
use App\baseClasses\KCRequest;
use App\models\KCAppointmentServiceMapping;
use App\models\KCService;
use App\models\KCUser;
use App\models\KCReceptionistClinicMapping;
use App\models\KCClinic;
use App\models\KCDoctorClinicMapping;
use Exception;
use WP_User;

class KCServiceController extends KCBase
{

    public $db;
    /**
     * @var KCRequest
     */
    private $request;

    public $exclude_service;
    public function __construct()
    {

        global $wpdb;

        $this->db = $wpdb;

        $this->request = new KCRequest();

        parent::__construct();
    }

/**
 * Extracts the minutes value from the duration data
 *
 * @param mixed $duration Duration data from request (could be object, array, string or integer)
 * @return int Total minutes
 *
 * Note: A duration of 0 minutes means the service won't block doctor availability
 * when booked. This is useful for services that don't require dedicated time slots.
 */
private function extractDurationMinutes($duration) {
    // Handle empty values or explicit 0
    if (empty($duration) || $duration === 0 || $duration === '0') {
        return 0;
    }

    // If it's already a numeric value, just return it
    if (is_numeric($duration)) {
        return (int) $duration;
    }

    // If duration is a value object from the frontend dropdown
    if (is_array($duration) && isset($duration['value'])) {
        return (int) $duration['value'];
    }

    // If it's a JSON string, try to decode it
    if (is_string($duration) && (strpos($duration, '{') === 0 || strpos($duration, '[') === 0)) {
        $decoded = json_decode($duration, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($decoded['value'])) {
                return (int) $decoded['value'];
            }
        }
    }

    // Handle time string format (HH:MM)
    if (is_string($duration) && strpos($duration, ':') !== false) {
        $parts = explode(':', $duration);
        if (count($parts) === 2) {
            $hours = (int) $parts[0];
            $minutes = (int) $parts[1];
            return ($hours * 60) + $minutes;
        }
    }

    // Last resort: try to cast to integer
    return (int) $duration;
}

    public function index()
    {

        if (!kcCheckPermission('service_list') && is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }
        
        $request_data = $this->request->getInputs();
        $service_table = $this->db->prefix . 'kc_services';
        $users_table = $this->db->base_prefix . 'users';
        $clinic_doctor_mapping = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $clinic_table = $this->db->prefix . 'kc_clinics';
        
        //current login user role
        $current_login_user_role = $this->getLoginUserRole();

        //current login user id
        $current_login_user_id = get_current_user_id();

        //default query condition value
        $search_condition = $doctor_condition = $clinic_condition = $paginationCondition = $clinic_service_condition = " ";
        $orderByCondition = " ORDER BY s.id DESC ";

        //check request is from new appointment book shortcode/widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';

        //check request is from new appointment book shortcode/widget and check doctor id empty or not valid id
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget && (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));

        //check request from service module (listing)
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        //check request is from new appointment book shortcode/widget
        if ($request_from_new_appointment_widget) {
            if (!empty($request_data['searchKey'])) {
                $request_data['searchKey'] = esc_sql($request_data['searchKey']);
                $searchKey = $request_data['searchKey'];
                //search query condition
                $search_condition = " AND (s.name LIKE '%{$searchKey}%' OR s.type LIKE '%{$searchKey}%' OR s.charges LIKE '%{$searchKey}%')";
            }
        } else if ($request_from_service_module) {
            // If perPage is set and greater than 0, apply pagination
            // If perPage is 0, return all records (no pagination)
            if (isset($request_data['perPage']) && (int) $request_data['perPage'] > 0) {
                $perPage = (int) $request_data['perPage'];
                $offset = ((int) $request_data['page'] - 1) * $perPage;
                $paginationCondition = " LIMIT {$perPage} OFFSET {$offset} ";
            }
            
            if (!empty($request_data['sort'])) {
                $request_data['sort'] = kcRecursiveSanitizeTextField(json_decode(stripslashes($request_data['sort'][0]), true));
                if (!empty($request_data['sort']['field']) && !empty($request_data['sort']['type']) && $request_data['sort']['type'] !== 'none') {
                    $sortField = esc_sql($request_data['sort']['field']);
                    $sortByValue = esc_sql(strtoupper($request_data['sort']['type']));
                    switch ($request_data['sort']['field']) {
                        case 'charges':
                        case 'status':
                        case 'id':
                        case 'duration':
                            $orderByCondition = " ORDER BY s.{$sortField} {$sortByValue} ";
                            break;
                        case 'name':
                        case 'type':
                            $orderByCondition = " ORDER BY s.{$sortField} {$sortByValue} ";
                            break;
                        case 'doctor_name':
                            $orderByCondition = " ORDER BY u.display_name {$sortByValue} ";
                            break;
                        case 'service_type':
                            $orderByCondition = " ORDER BY s.type {$sortByValue} ";
                            break;
                    }
                }
            }

            if (isset($request_data['searchTerm']) && trim($request_data['searchTerm']) !== '') {
                $request_data['searchTerm'] = esc_sql(strtolower(trim($request_data['searchTerm'])));
                $search_condition .= " AND (
                           s.id LIKE '%{$request_data['searchTerm']}%'
                           OR s.name LIKE '%{$request_data['searchTerm']}%'
                           OR u.display_name LIKE '%{$request_data['searchTerm']}%'
                           OR s.charges LIKE '%{$request_data['searchTerm']}%'
                           OR s.type LIKE '%{$request_data['searchTerm']}%'
                           OR s.status LIKE '%{$request_data['searchTerm']}%'
                           ) ";
            } else {
                if (!empty($request_data['columnFilters'])) {
                    $request_data['columnFilters'] = json_decode(stripslashes($request_data['columnFilters']), true);
                    foreach ($request_data['columnFilters'] as $column => $searchValue) {
                        $searchValue = !empty($searchValue) ? $searchValue : '';
                        $searchValue = esc_sql(strtolower(trim($searchValue)));
                        $column = esc_sql($column);
                        if ($searchValue === '') {
                            continue;
                        }
                        switch ($column) {
                            case 'charges':
                            case 'status':
                            case 'id':
                            case 'duration':
                                if ($column === 'duration') {
                                    list($hours, $minutes) = explode(":", $searchValue);
                                    $searchValue = ((int) $hours * 60) + (int) $minutes;
                                }
                                $search_condition .= " AND s.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'name':
                            case 'type':
                                $search_condition .= " AND s.{$column} LIKE '%{$searchValue}%' ";
                                break;
                            case 'doctor_name':
                                $search_condition .= " AND u.display_name LIKE '%{$searchValue}%' ";
                                break;
                            case 'service_type':
                                $search_condition .= " AND s.type LIKE '%{$searchValue}%'";
                                break;
                            case 'clinic_name':
                                $search_condition .= " AND c.name LIKE '%{$searchValue}%'";
                                break;
                        }
                    }
                }
            }
        }

        //check if login user is doctor or request data have valid doctor id
        if (($this->getDoctorRole() === $current_login_user_role) || (isset($request_data['doctor_id']) && !in_array($request_data['doctor_id'], [0, '0']))) {

            //doctor id
            $doctor_id = $this->getDoctorRole() === $current_login_user_role ? $current_login_user_id : $request_data['doctor_id'];
            //doctor query condition
            if (str_contains($doctor_id, ',')) {
                $doctor_id = implode(',', array_map('absint', explode(',', $doctor_id)));
                $doctor_condition = " AND s.doctor_id IN ({$doctor_id}) ";
            } else {
                $doctor_id = (int) $doctor_id;
                $doctor_condition = " AND s.doctor_id = {$doctor_id} ";
            }

        }

        $telemed_condition = " AND (s.telemed_service != 'yes' OR s.telemed_service IS NULL )  ";
        if (isKiviCareTelemedActive() || isKiviCareGoogleMeetActive()) {
            $telemed_condition = "  ";
        }

        // get only active service list in appointment book
        $active_services = $request_from_service_module ? " " : " AND s.status = '1' ";
        $full_service_name = " s.name,s.type,s.doctor_id,s.clinic_id ";
        if ($request_from_new_appointment_widget) {
            if (
                $request_from_new_appointment_widget_and_service_first ||
                (isset($request_data['doctor_id']) && !in_array($request_data['doctor_id'], [0, '0'])
                    && (empty($request_data['doctor_id']) || in_array($request_data['clinic_id'], [0, '0'])))
            ) {
                $full_service_name = " s.name,s.type ";
            }
        }

        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            //get clinic id wise service list
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
                $clinic_service_condition = " AND s.clinic_id = {$request_data['clinic_id']} ";
            }
        } else {
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
            if (!empty($request_data['clinic_id'])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
                $clinic_service_condition = " AND s.clinic_id = {$request_data['clinic_id']} ";
            }
        }

        $preselected_service_condition = ' ';
        if (!empty($request_data['preselected_service'])) {
            $request_data['preselected_service'] = implode(',', array_filter(array_map('absint', explode(',', $request_data['preselected_service']))));
            if (!empty($request_data['preselected_service'])) {
                $preselected_service_condition = " AND s.id IN ({$request_data['preselected_service']}) ";
            }
        }

        //query for service list - simplified to use single table
        $query = "SELECT s.*,s.charges as service_base_price,
                  CONCAT({$full_service_name}) AS full_service_name,
                  s.name AS name, s.type AS service_type, s.created_at AS created_at,
                  u.display_name AS doctor_name, c.name AS clinic_name
                   FROM {$service_table} s
                  LEFT JOIN {$clinic_table} c ON s.clinic_id = c.id
                  LEFT JOIN {$users_table} u ON u.ID = s.doctor_id
                  LEFT JOIN {$clinic_doctor_mapping} dcm ON dcm.doctor_id = s.doctor_id AND dcm.clinic_id = s.clinic_id
                  WHERE 1=1 {$doctor_condition} {$clinic_condition} {$clinic_service_condition} {$active_services} {$telemed_condition} {$search_condition} {$preselected_service_condition}
                 {$orderByCondition}";

        $total = 0;
        if ($request_from_service_module) {
            $total = $this->db->get_var("SELECT count(*)  FROM {$service_table} s
                  LEFT JOIN {$users_table} u ON u.ID = s.doctor_id
                  LEFT JOIN {$clinic_table} c ON s.clinic_id = c.id
                  LEFT JOIN {$clinic_doctor_mapping} dcm ON dcm.doctor_id = s.doctor_id AND dcm.clinic_id = s.clinic_id
                  WHERE 1=1 {$doctor_condition} {$clinic_condition} {$clinic_service_condition} {$active_services} {$telemed_condition} {$search_condition} ");

            $query .= $paginationCondition;
        }

        $clinicCurrenySetting = kcGetClinicCurrenyPrefixAndPostfix();
        $clinic_prefix = !empty($clinicCurrenySetting['prefix']) ? $clinicCurrenySetting['prefix'] : '';
        $clinic_postfix = !empty($clinicCurrenySetting['postfix']) ? $clinicCurrenySetting['postfix'] : '';
        
        //get unique service (full_service_name = service_name + service_category_name + service_doctor_id)
        $services = collect($this->db->get_results($query))->unique('full_service_name')->map(function ($service) use ($clinic_prefix, $clinic_postfix, $request_data, $request_from_new_appointment_widget_and_service_first) {
            $service->charges = round((float) $service->charges, 3);
            $service->clinic_name = decodeSpecificSymbols($service->clinic_name);
            $service->service_base_price = round((float) $service->charges, 3); // Using charges as base price since they're now the same
            
            //service image
            $service->image = !empty($service->image) ? wp_get_attachment_url($service->image) : '';
            //service category name format
            $service->service_type = !empty($service->service_type) ? str_replace('_', ' ', $service->service_type) : "";
            //check if service name is telemed
            if ($service->telemed_service === 'yes') {
                //get category name of telemed service (updated category name of telemed service)
                $service->service_type = !empty($service->service_name_alias) ? str_replace("_", " ", $service->service_name_alias) : $service->service_type;
            }

            if ($request_from_new_appointment_widget_and_service_first) {
                //change service charges as base service price
                $service->charges = $clinic_prefix . $service->service_base_price . $clinic_postfix;
            } else {
                if (empty($request_data['without_currency']) || (!empty($request_data['without_currency']) && $request_data['without_currency'] !== 'yes')) {
                    $service->charges = $clinic_prefix . $service->charges . $clinic_postfix;
                }
            }

            $service->share_link = add_query_arg(
                array(
                    'service_id' => $service->id, // Changed from service_id to id
                    'doctor_id' => $service->doctor_id,
                    'clinic_id' => $service->clinic_id,
                    'step' => 'datetime',
                ),
                site_url('/appointment')
            );
            return $service;
        })->values();

        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No services found', 'kc-lang'),
                'data' => []
            ]);
        } else {
            $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;
            wp_send_json([
                'status' => true,
                'message' => esc_html__('Service list', 'kc-lang'),
                'data' => $services,
                'total_rows' => $request_from_service_module ? $total : count($services),
                'html' => $request_from_new_appointment_widget ? $this->kcCreateServiceListHtml($services, $request_data) : ''
            ]);
        }
    }

    public function getClinicServices() {
        // Debug permission check
        $has_permission = kcCheckPermission('service_list');
        $is_logged_in = is_user_logged_in();

        // Check if this is a widget request (should allow public access)
        $request_data = $this->request->getInputs();
        $is_widget_request = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';

        // Only check permissions for logged-in users and non-widget requests
        if (!$has_permission && $is_logged_in && !$is_widget_request) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        // Handle specific route and module type parameters
        if (!empty($request_data['module_type']) && $request_data['module_type'] === 'appointment_form') {
            // Handle doctor_id if provided
            if (!empty($request_data['doctor_id'])) {
                $request_data['doctor_id'] = (int)$request_data['doctor_id'];
            }

            // Handle clinic_id array if provided
            if (!empty($request_data['clinic_id']) && is_array($request_data['clinic_id'])) {
                $request_data['clinic_id'] = (int)$request_data['clinic_id'][0];
            }
        }

        // Check if request is from appointment widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget &&
            (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        // Define table names - simplified to use single table
        $service_table = $this->db->prefix . 'kc_services';
        $users_table = $this->db->base_prefix . 'users';
        $clinic_doctor_mapping = $this->db->prefix . 'kc_doctor_clinic_mappings';
        $clinic_table = $this->db->prefix . 'kc_clinics';
        $category_table = $this->db->prefix . 'kc_categories';

        // Get clinic ID based on request or user role
        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            }
        } else {
            $current_login_user_role = $this->getLoginUserRole();
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
        }

        $clinic_service_condition = '';
        if (!empty($request_data['clinic_id'])) {
            $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            $clinic_service_condition = " AND s.clinic_id = {$request_data['clinic_id']} ";
        }

        // Add doctor_id condition if specified
        $doctor_condition = '';
        if (!empty($request_data['doctor_id'])) {
            $doctor_id = (int) $request_data['doctor_id'];
            $doctor_condition = " AND s.doctor_id = {$doctor_id} ";
        }

        $full_service_name = " s.name,s.type,s.doctor_id,s.clinic_id ";
        if ($request_from_new_appointment_widget) {
            if ($request_from_new_appointment_widget_and_service_first) {
                $full_service_name = " s.name,s.type ";
            }
        }
        
        $service_category_condition='';
        if (!empty($request_data['service_category'])) {
            $category = $request_data['service_category'];

            // Check if category is numeric (new category_id system) or string (old type system for backward compatibility)
            if (is_numeric($category)) {
                // New category system - filter by category_id
                $category_id = (int) $category;
                $service_category_condition = " AND s.category_id = {$category_id} ";
            } else {
                // Backward compatibility - filter by old type system
                $category = esc_sql($category);
                $service_category_condition = " AND (
                    s.type = '{$category}' OR
                    (s.service_name_alias IS NOT NULL AND s.service_name_alias = '{$category}')
                ) ";
            }

            if(isset($request_data['doctor_id']) && !empty($request_data['doctor_id'])){
                $request_data['doctor_id'] = (int) $request_data['doctor_id'];
                $service_category_condition .= " AND s.doctor_id = ".$request_data['doctor_id'];
            }
        }

        // Query for service list - updated to include category information
        $query = "SELECT s.*,
                  s.charges as service_base_price,
                  CONCAT({$full_service_name}) AS full_service_name,
                  s.name AS name,
                  s.type AS service_type,
                  s.created_at AS created_at,
                  u.display_name AS doctor_name,
                  c.name AS clinic_name,
                  cat.id AS category_id,
                  cat.name AS category_name,
                  cat.slug AS category_slug
                  FROM {$service_table} s
                  LEFT JOIN {$clinic_table} c ON s.clinic_id = c.id
                  LEFT JOIN {$users_table} u ON u.ID = s.doctor_id
                  LEFT JOIN {$clinic_doctor_mapping} dcm ON dcm.doctor_id = s.doctor_id AND dcm.clinic_id = s.clinic_id
                  LEFT JOIN {$category_table} cat ON s.category_id = cat.id
                  WHERE s.status = '1' {$clinic_service_condition} {$doctor_condition}
                  {$service_category_condition}
                  ORDER BY s.id DESC";

        $total = $request_from_service_module ?
            $this->db->get_var(preg_replace('/SELECT.*?FROM/', 'SELECT COUNT(*) FROM', $query)) : 0;

        // Get clinic currency settings
        $clinicCurrencySetting = kcGetClinicCurrenyPrefixAndPostfix();
        $clinic_prefix = !empty($clinicCurrencySetting['prefix']) ? $clinicCurrencySetting['prefix'] : '';
        $clinic_postfix = !empty($clinicCurrencySetting['postfix']) ? $clinicCurrencySetting['postfix'] : '';

        // Get and process services
        $services =  collect($this->db->get_results($query));
        if(empty($request_data['service_category'])){
            $services = $services->unique('full_service_name');
        }
        $services = $services->map(function ($service)
            use ($clinic_prefix, $clinic_postfix, $request_data, $request_from_new_appointment_widget_and_service_first) {

            $service->charges = round((float) $service->charges, 3);
            $service->clinic_name = decodeSpecificSymbols($service->clinic_name);
            $service->service_base_price = round((float) $service->charges, 3);
            $service->image = !empty($service->image) ? wp_get_attachment_url($service->image) : '';
            $service->service_type = !empty($service->service_type) ? str_replace('_', ' ', $service->service_type) : "";

            if ($service->telemed_service === 'yes') {
                $service->service_type = !empty($service->service_name_alias) ?
                    str_replace("_", " ", $service->service_name_alias) : $service->service_type;
            }

            if ($request_from_new_appointment_widget_and_service_first) {
                $service->charges = $clinic_prefix . $service->service_base_price . $clinic_postfix;
            } else {
                if (empty($request_data['without_currency']) ||
                    (!empty($request_data['without_currency']) && $request_data['without_currency'] !== 'yes')) {
                    $service->charges = $clinic_prefix . $service->charges . $clinic_postfix;
                }
            }

            $service->share_link = add_query_arg(
                array(
                    'service_id' => $service->id, // Changed from service_id to id
                    'doctor_id' => $service->doctor_id,
                    'clinic_id' => $service->clinic_id,
                    'step' => 'datetime',
                ),
                site_url('/appointment')
            );

            return $service;
        })->values();

        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No services found', 'kc-lang'),
                'data' => []
            ]);
        }

        $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;
        // Create debug info for troubleshooting if needed
        $debug_info = [];
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $debug_info = [
                'clinic_id' => $request_data['clinic_id'] ?? 'not_set',
                'clinic_condition' => $clinic_service_condition,
                'doctor_condition' => $doctor_condition,
                'category_condition' => $service_category_condition,
                'request_category' => isset($request_data['service_category']) ? $request_data['service_category'] : 'none',
                'query' => $query,
                'service_count' => count($services),
                'request_data' => $request_data
            ];
        }

        wp_send_json([
            'status' => true,
            'message' => esc_html__('Service list', 'kc-lang'),
            'data' => $services,
            'total_rows' => $request_from_service_module ? $total : count($services),
            'html' => $this->kcCreateServiceListHtml($services, $request_data),
            'debug' => $debug_info
        ]);
    }

    public function getClinicServicesCategory() {
        if (!kcCheckPermission('service_list') && is_user_logged_in()) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        global $wpdb;
        $request_data = $this->request->getInputs();

        // Check if request is from appointment widget
        $request_from_new_appointment_widget = !empty($request_data['widgetType']) && $request_data['widgetType'] === 'phpWidget';
        $request_from_new_appointment_widget_and_service_first = $request_from_new_appointment_widget &&
            (empty($request_data['doctor_id']) || in_array($request_data['doctor_id'], [0, '0']));
        $request_from_service_module = !empty($request_data['type']) && $request_data['type'] === 'list';

        // Define table names - simplified to use single table
        $service_table = $this->db->prefix . 'kc_services';

        // Get clinic ID based on request or user role
        if ($request_from_new_appointment_widget && isKiviCareProActive()) {
            if (!empty($request_data['clinic_id']) && !in_array($request_data['clinic_id'], ['0', 0])) {
                $request_data['clinic_id'] = (int) $request_data['clinic_id'];
            }
        } else {
            $current_login_user_role = $this->getLoginUserRole();
            switch ($current_login_user_role) {
                case $this->getDoctorRole():
                case 'administrator':
                    if ($request_from_service_module) {
                        $request_data['clinic_id'] = '';
                    } else {
                        $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    }
                    break;
                case $this->getClinicAdminRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfClinicAdmin();
                    break;
                case $this->getReceptionistRole():
                    $request_data['clinic_id'] = kcGetClinicIdOfReceptionist();
                    break;
                case $this->getPatientRole():
                    $request_data['clinic_id'] = !empty($request_data['clinic_id']) ? $request_data['clinic_id'] : kcGetDefaultClinicId();
                    break;
            }
        }

        $query_condition= '';
        if(isset($request_data['searchKey']) && !empty($request_data['searchKey'])){
            $searchKey= sanitize_title($request_data['searchKey']) ;
            $query_condition .=  " AND s.name LIKE '%$searchKey%' ";
        }

        // Get all valid service categories that actually have active services - updated to use category system
        $query = $wpdb->prepare("
            SELECT
                c.id as category_id,
                c.name as category_name,
                c.slug as category_slug,
                c.visibility as category_visibility,
                COUNT(s.id) as service_count,
                MIN(s.name) as service_name,
                MIN(s.clinic_id) as clinic_id,
                MIN(s.charges) as charges,
                c.name AS category_value
            FROM {$wpdb->prefix}kc_categories c
            INNER JOIN {$wpdb->prefix}kc_services s ON s.category_id = c.id
            WHERE s.status = 1
                AND s.clinic_id = %d
                AND c.module_type = 'service'
                AND c.status = 1
                AND c.visibility IN ('public', 'backend_only')
                {$query_condition}
            GROUP BY c.id, c.name, c.slug, c.visibility
            HAVING service_count > 0
            ORDER BY c.sort_order ASC, c.name ASC
        ", $request_data['clinic_id']);

        $services = $wpdb->get_results($query);

        $total = $request_from_service_module ?
            $this->db->get_var(preg_replace('/SELECT.*?FROM/', 'SELECT COUNT(*) FROM', $query)) : 0;


        if (empty($services) || count($services) < 1) {
            wp_send_json([
                'status' => false,
                'message' => esc_html__('No Category found', 'kc-lang'),
                'data' => []
            ]);
        }

        $request_data['request_from_new_appointment_widget_and_service_first'] = $request_from_new_appointment_widget_and_service_first;

        ob_start(); ?>
            <div class="space-y-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <?php foreach ($services as $key => $value) : ?>
                        <div class="category-item">
                            <?php
                            // Use category ID and name from the new category system
                            $category_id = $value->category_id;
                            $category_name = $value->category_name;
                            $category_slug = $value->category_slug;

                            // Only show category if it has a valid name and services
                            if (!empty($category_name) && !empty($value->service_count)) :
                            ?>
                            <input id="category_<?php echo esc_attr($category_id); ?>" type="radio"
                                   name="service_category"
                                   value="<?php echo esc_attr($category_id); ?>"
                                   class="hidden peer service-category"
                                   data-count="<?php echo intval($value->service_count); ?>"
                                   data-category-name="<?php echo esc_attr($category_name); ?>"
                                   data-category-slug="<?php echo esc_attr($category_slug); ?>" />
                            <label for="category_<?php echo esc_attr($category_id); ?>" class="bg-gray-50 rounded-lg shadow-sm p-4 max-w-sm border-2 border-transparent checked:border-blue-500 block cursor-pointer">
                                <div>
                                    <span class="font-medium text-gray-900">
                                        <?php echo esc_html($category_name); ?>
                                    </span>
                                    <?php if (WP_DEBUG && current_user_can('manage_options')) : ?>
                                    <span class="text-xs text-gray-400 block">(<?php echo intval($value->service_count); ?> services)</span>
                                    <?php endif; ?>
                                </div>
                            </label>
                            <?php endif; ?>
                        </div>
                <?php endforeach; ?>
                </div>
            </div>
            <?php $service_category_html = ob_get_clean();


        // Create debug info for troubleshooting if needed
        $debug_info = [];
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $debug_info = [
                'query' => $query,
                'services' => $services,
                'service_count' => count($services),
                'clinic_id' => $request_data['clinic_id']
            ];
        }

        wp_send_json([
            'status' => true,
            'message' => esc_html__('Service list', 'kc-lang'),
            'data' => $services,
            'total_rows' => $request_from_service_module ? $total : count($services),
            'html' => $service_category_html,
            'debug' => $debug_info
        ]);
    }

    public function save()
    {

        global $wpdb;

        if (!kcCheckPermission('service_add')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();
        $request_data['price'] = round((float) $request_data['price'], 3);
        $service_table = $this->db->prefix . 'kc_services';
        $pro_plugin_active = isKiviCareProActive();
        $service = new KCService();

        //current login user role
        $current_login_user_role = $this->getLoginUserRole();

        //current login user id
        $current_login_user_id = get_current_user_id();

        //service data - now includes all fields in single table
        $temp = [
            'name' => $request_data['name'],
            'price' => $request_data['price'],
            'charges' => $request_data['price'], // Set charges same as price initially
            'type' => str_replace(' ', '_', strtolower($request_data['type']['label'])),
            'category_id' => (int) $request_data['type']['id'], // Add category_id for new system
            'status' => $request_data['status']['id'],
            'telemed_service' => isset($request_data['telemed_service']) ? $request_data['telemed_service'] : 'no',
            'multiple' => isset($request_data['multiservice']['id']) ? $request_data['multiservice']['id'] : 'yes',
        ];

        //get clinic_id
        $clinic_id = kcGetDefaultClinicId();
        if (isKiviCareProActive()) {
            switch ($current_login_user_role) {
                case $this->getReceptionistRole():
                    $clinic_id = kcGetClinicIdOfReceptionist();
                    $request_data['clinic_id'] = [$clinic_id];
                    break;
                case $this->getClinicAdminRole():
                    $clinic_id = kcGetClinicIdOfClinicAdmin();
                    $request_data['clinic_id'] = [$clinic_id];
                    break;
                default:
                    if (is_array($request_data['clinic_id']) && !empty($request_data['clinic_id'][0]['id'])) {
                        $request_data['clinic_id'] = collect($request_data['clinic_id'])->pluck('id')->map(function ($v) {
                            return (int) $v;
                        })->toArray();
                    } else {
                        if (!empty($request_data['clinic_id']['id'])) {
                            $clinic_id = (int) $request_data['clinic_id']['id'];
                        }
                        $request_data['clinic_id'] = [$clinic_id];
                    }
                    break;
            }
        }

        //service image attachment id
        $attachment_id = 0;
        if (isset($request_data['profile_image']) && !empty((int) $request_data['profile_image'])) {
            $attachment_id = $request_data['profile_image'];
            $temp['image'] = $attachment_id;
        }

        // Add duration if provided
        if ($pro_plugin_active && !empty($request_data['duration'])) {
            $temp['duration'] = $this->extractDurationMinutes($request_data['duration']);
        }

        // Add service name alias for telemed services
        if ($temp['telemed_service'] === 'yes') {
            $temp['service_name_alias'] = $temp['type'];
        }

        if (empty($request_data['id'])) {
            //get doctor id from request data
            if ($current_login_user_role === $this->getDoctorRole()) {
                $doctor_id = [$current_login_user_id];
            } else {
                $doctor_id = collect($request_data['doctor_id'])->pluck('id')->map(function ($v) {
                    return (int) $v;
                })->toArray();
            }
            
            $implode_clinic_condition = '';
            if (!empty($request_data['clinic_id'])) {
                //implode clinic id for query
                $implode_clinic_id = implode(',', $request_data['clinic_id']);
                $implode_clinic_condition = " clinic_id IN ({$implode_clinic_id}) AND ";
            }
            //implode doctor id for query
            $implode_doctor_id = implode(',', $doctor_id);
            //clinic wise doctor array
            $clinic_doctors = collect($this->db->get_results("SELECT GROUP_CONCAT(doctor_id) AS doctor_id, clinic_id FROM
            {$this->db->prefix}kc_doctor_clinic_mappings WHERE $implode_clinic_condition doctor_id IN ({$implode_doctor_id}) GROUP BY clinic_id"))->keyBy('clinic_id')->map(function ($v) {
                return explode(',', $v->doctor_id);
            })->toArray();

            //check if same service exists with same doctor - simplified validation for single table
            foreach ($clinic_doctors as $clinic_doctor_key => $clinic_doctor_val) {
                $doctor_ids = implode(',', $clinic_doctor_val);
                $validationRow = $wpdb->get_var("SELECT id FROM {$service_table}
                    WHERE doctor_id IN ({$doctor_ids})
                    AND clinic_id = $clinic_doctor_key
                    AND (category_id = {$temp['category_id']} OR type ='{$temp['type']}')
                    AND name='{$request_data['name']}'");
                if (!empty($validationRow)) {
                    wp_send_json([
                        'status' => false,
                        'message' => esc_html__('Same Service Already Exists,Please select Different category or service name', 'kc-lang'),
                        'data' => []
                    ]);
                }
            }

            $temp['created_at'] = current_time('Y-m-d H:i:s');
            
            // Insert service records for each doctor/clinic combination
            $service_ids = [];
            foreach ($clinic_doctors as $clinic_id => $clinic_doctor_val) {
                foreach ($clinic_doctor_val as $doctor) {
                    $service_data = $temp;
                    $service_data['doctor_id'] = (int) $doctor;
                    $service_data['clinic_id'] = (int) $clinic_id;
                    
                    $service_data = apply_filters('kivicare_update_service_save_fields', $service_data, $request_data);
                    
                    $service_id = $service->insert($service_data);
                    if ($service_id) {
                        $service_ids[] = $service_id;
                        // hook for service add.
                        do_action('kc_service_add', $service_data);
                    }
                }
            }

            $message = esc_html__('Service saved successfully', 'kc-lang');

            kcLogActivity(
                'create_service',
                sprintf(esc_html__('Service has been created successfully', 'kc-lang')),
                [
                    'service_ids' => $service_ids,
                ]
            );

        } else {
            $request_data['id'] = (int) $request_data['id'];
            if (!(new KCService())->serviceUserPermission($request_data['id'])) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }

            $clinic_id = isset($request_data['clinic_id'][0]) && !empty($request_data['clinic_id'][0])
                ? (int) $request_data['clinic_id'][0]
                : (isset($request_data['clinic_id']['id']) && !empty($request_data['clinic_id']['id'])
                    ? (int) $request_data['clinic_id']['id']
                    : kcGetDefaultClinicId());

            $doctor_id = $current_login_user_role === $this->getDoctorRole() ? $current_login_user_id : (int) $request_data['doctor_id']['id'];

            //check if same service exists with same doctor - simplified validation
            $validationRow = $wpdb->get_var("SELECT id FROM {$service_table}
                WHERE doctor_id = {$doctor_id}
                AND clinic_id = {$clinic_id}
                AND (category_id = {$temp['category_id']} OR type ='{$temp['type']}')
                AND name='{$request_data['name']}'
                AND id != {$request_data['id']} ");
            if (!empty($validationRow)) {
                wp_send_json([
                    'status' => false,
                    'message' => esc_html__('Same Service Already Exists,Please select Different category or service name', 'kc-lang'),
                    'data' => []
                ]);
            }

            $service_update_data = $temp;
            $service_update_data['doctor_id'] = $doctor_id;
            $service_update_data['clinic_id'] = $clinic_id;
            
            $service_update_data = apply_filters('kivicare_update_service_save_fields', $service_update_data, $request_data);

            $service->update($service_update_data, array('id' => $request_data['id']));

            // Update WooCommerce product if exists
            $product_id = $this->getProductIdOfService($request_data['id']);
            if (!empty($product_id) && get_post_status($product_id)) {
                update_post_meta($product_id, '_price', $request_data['price']);
                update_post_meta($product_id, '_sale_price', $request_data['price']);
                if (!empty($attachment_id)) {
                    update_post_meta($product_id, '_thumbnail_id', $attachment_id);
                }

                $my_post = array(
                    'ID' => $product_id,
                    'post_title' => $request_data['name'],
                );
                wp_update_post($my_post);
            }

            $service_update_data['id'] = $request_data['id'];

            do_action('kc_service_update', $service_update_data);

            $message = esc_html__('Service updated successfully', 'kc-lang');

            kcLogActivity(
                'update_service',
                sprintf(esc_html__('Service has been updated successfully', 'kc-lang')),
                [
                    'service_id' => $request_data['id'],
                ]
            );
        }

        wp_send_json([
            'status' => true,
            'message' => $message
        ]);

    }

    public function edit()
    {


        if (!kcCheckPermission('service_edit') || !kcCheckPermission('service_view')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        try {

            if (!isset($request_data['id'])) {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }

            $edit_id = (int) $request_data['id'];

            if (!(new KCService())->serviceUserPermission($edit_id)) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            $service_table = $this->db->prefix . 'kc_services';
            $users_table = $this->db->base_prefix . 'users';
            $clinic_table = $this->db->prefix . 'kc_clinics';

            // Query with category information for new system
            $category_table = $this->db->prefix . 'kc_categories';
            $query = " SELECT s.*, u.display_name AS doctor_name, c.name AS clinic_name,
                              cat.id AS category_id, cat.name AS category_name, cat.slug AS category_slug
                       FROM  {$service_table} s
					   LEFT JOIN  {$users_table} u ON u.ID = s.doctor_id
                       LEFT JOIN  {$clinic_table} c ON c.id = s.clinic_id
                       LEFT JOIN  {$category_table} cat ON s.category_id = cat.id
					   WHERE s.id = {$edit_id} ";


            $service = $this->db->get_row($query);

            if (count((array) $service)) {

                $status = new \stdClass();
                $status->id = 0;
                $status->label = 'Inactive';

                if ((int) $service->status === 1) {
                    $status->id = 1;
                    $status->label = 'Active';
                }

                $temp = [
                    'id' => $service->id,
                    'name' => $service->name,
                    'price' => round((float) $service->charges, 3), // Use charges instead of price
                    'doctor_id' => [
                        'id' => $service->doctor_id,
                        'label' => $service->doctor_name
                    ],
                    'clinic_id' => [
                        'id' => $service->clinic_id,
                        'label' => decodeSpecificSymbols($service->clinic_name)
                    ],
                    'type' => [
                        'id' => $service->category_id ?: $service->type,
                        'label' => $service->category_name ?: str_replace('_', " ", $service->type),
                        'slug' => $service->category_slug ?: null
                    ],
                    'telemed_service' => $service->telemed_service,
                    'status' => $status,
                    'image' => !empty($service->image) ? wp_get_attachment_url($service->image) : ''
                ];

                if (!empty($service->multiple)) {
                    $temp['multiservice'] = [
                        'id' => strtolower($service->multiple),
                        'label' => $service->multiple
                    ];
                } else {
                    $temp['multiservice'] = [
                        'id' => 'yes',
                        'label' => __("yes", "kc-lang")
                    ];
                }
                if ($service->telemed_service === 'yes') {
                    if (!empty($service->service_name_alias)) {
                        $temp['type'] = [
                            'id' => str_replace(" ", "_", strtolower($service->service_name_alias)),
                            'label' => $service->service_name_alias
                        ];
                    } else {
                        $temp['type'] = [
                            'id' => $service->type,
                            'label' => str_replace('_', " ", $service->type)
                        ];
                    }
                }

                $hours = floor($service->duration / 60);
                $minutes = (int) $service->duration % 60;
                $temp['duration'] = sprintf("%02d:%02d", $hours, $minutes);
                $temp = apply_filters('kivicare_update_service_edit_fields', $temp, $service);
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Service data', 'kc-lang'),
                    'data' => $temp
                ]);

            } else {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }


        } catch (Exception $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            header("Status: $code $message");
            wp_send_json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function delete()
    {

        if (!kcCheckPermission('service_delete')) {
            wp_send_json(kcUnauthorizeAccessResponse(403));
        }

        $request_data = $this->request->getInputs();

        try {

            if (!isset($request_data['id'])) {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Data not found', 'kc-lang'), 400));
            }

            $service = new KCService();

            $request_data['id'] = (int) $request_data['id'];

            if (!(new KCService())->serviceUserPermission($request_data['id'])) {
                wp_send_json(kcUnauthorizeAccessResponse(403));
            }
            
            $product_id = $this->getProductIdOfService($request_data['id']);
            if (!empty($product_id) && get_post_status($product_id)) {
                do_action('kc_woocoomerce_service_delete', $product_id);
                wp_delete_post($product_id);
            }

            $id = $request_data['id'];
            $results = $service->delete(['id' => $id]);

            if ($results) {
                kcLogActivity(
                    'delete_service',
                    sprintf(esc_html__('Service has been deleted successfully', 'kc-lang'), $id),
                    [
                        'service_id' => $id,
                    ]
                );

                do_action('kc_service_delete', $id);
                wp_send_json([
                    'status' => true,
                    'message' => esc_html__('Service has been deleted successfully', 'kc-lang'),
                ]);
            } else {
                wp_send_json(kcThrowExceptionResponse(esc_html__('Failed to delete Service', 'kc-lang'), 400));
            }


        } catch (Exception $e) {

            $code = $e->getCode();
            $message = $e->getMessage();

            header("Status: $code $message");

            wp_send_json([
                'status' => false,
                'message' => $message
            ]);
        }
    }

    public function getProductIdOfService($id)
    {
        $id = (int) $id;
        $product_id = '';
        $service_table = $this->db->prefix . 'kc_services';
        $data = $this->db->get_var('select extra from ' . $service_table . ' where id=' . $id);
        if ($data != null) {
            $data = json_decode($data);
            $product_id = $data->product_id;
        }
        return $product_id;
    }
    
    public function kcCreateServiceListHtml($services, $request_data) {
        $services = $services->sortBy('service_type')->groupBy('service_type');
        $showServicetImageStatus = kcGetSingleWidgetSetting('showServiceImage');
        $showServicetypeStatus = kcGetSingleWidgetSetting('showServicetype');
        $showServicePriceStatus = kcGetSingleWidgetSetting('showServicePrice');
        $showServiceDurationStatus = kcGetSingleWidgetSetting('showServiceDuration');
        $total_service_category = count($services);

        ob_start();

        ?>
         <div class="grid grid-cols-1 sm:grid-cols-2  gap-4">
            <?php
        foreach ($services as $key => $main_services) {
            ?>

                <?php
                $total_service = count($main_services);
                foreach ($main_services as $service) {
                    $singleServiceClass = '';
                    if (!empty($service->multiple) && $service->multiple == 'no') {
                        $singleServiceClass = ' selected-service-single';
                    }
                    $prechecked = ' ';
                    $is_checked_class='';
                    if ($total_service == 1 && $total_service_category == 1) {
                        $singleServiceClass = ' selected-service-single ';
                        $is_checked_class = ' border-purple-500 bg-purple-50 ';
                        $prechecked = ' checked ';
                    }
                    if (!empty($request_data['request_from_new_appointment_widget_and_service_first'])) {
                        $singleServiceClass = ' selected-service-single';
                    }



                    //get doctor session days
                    $results = collect($this->db->get_results("SELECT DISTINCT day FROM {$this->db->prefix}kc_clinic_sessions where doctor_id={$service->doctor_id} AND clinic_id={$service->clinic_id}"))->pluck('day')->toArray();
                    $leaves = collect($this->db->get_results("SELECT * FROM {$this->db->prefix}kc_clinic_schedule where module_id ={$service->doctor_id} AND module_type = 'doctor'"))->toArray();

                    //week day for php vue appointment dashboard
                    $days = [1 => 'sun', 2 => 'mon', 3 =>'tue', 4 => 'wed', 5 => 'thu', 6 => 'fri', 7 => 'sat'];
                    //week day for php shortcode widget
                    if(!empty($request_data['type']) && $request_data['type'] === 'flatpicker'){
                        $days = [0 => 'sun', 1 => 'mon', 2 =>'tue', 3 => 'wed', 4 => 'thu', 5 => 'fri', 6 => 'sat'];
                    }


                    if(count($results) > 0){
                        // get unavilable  days
                    $results = array_diff(array_values($days),$results);
                    //get key of unavilable days
                    $results = array_map(function ($v) use ($days){
                        return array_search($v,$days);
                    },$results);
                    $results = array_values($results);
                    }
                    else{
                        //get all days keys
                        $results = array_keys($days);
                    }

                    $next_available_date=  kc_get_next_available_date([
                         'days' => $results,
                         'holiday' => $leaves
                    ]);

                    // Determine service type
                    $serviceType = ($service->telemed_service === 'yes') ? 'virtual' : 'clinic';
                    ?>
                    <!-- Parent container for each card -->
                    <div class="service-item <?php echo esc_attr($serviceType); ?>">
                        <!-- Hidden checkbox for selection -->
                        <input
                            type="checkbox"
                            class="hidden peer card-checkbox selected-service <?php echo esc_html($singleServiceClass); ?>"
                            name="card_main"
                            id="service_<?php echo esc_html($service->id); ?>"
                            value="<?php echo esc_html($service->id); ?>"
                            service_id="<?php echo esc_html($service->id); ?>"
                            service_name="<?php echo esc_html($service->name); ?>"
                            service_price="<?php echo esc_html($service->charges); ?>"
                            doctor_id="<?php echo esc_html($service->doctor_id); ?>"
                            clinic_id="<?php echo esc_html($service->clinic_id); ?>"
                            status="<?php echo esc_html($service->status); ?>"
                            created_at="<?php echo esc_html($service->created_at); ?>"
                            doctor_name="<?php echo esc_html($service->doctor_name); ?>"
                            service_type="<?php echo esc_html($serviceType); ?>"
                            multipleService="<?php echo esc_html(!empty($service->multiple) && $service->multiple == 'no' ? 'no' : 'yes'); ?>"
                            <?php echo esc_html($prechecked); ?>/>

                        <!-- Label acts as the card; clicking it checks the box -->
                        <label
                            for="service_<?php echo esc_html($service->id); ?>"
                            class="block w-full p-4 bg-gray-50 border border-gray-200 rounded-lg shadow-sm cursor-pointer hover:border-indigo-200 transition-colors peer-checked:border-indigo-500 <?php echo esc_attr($is_checked_class)?>" >
                            <!-- Top row: service name + price -->
                            <div class="flex justify-between items-start">
                                <div class="text-left">
                                    <h3 class="text-base font-medium text-gray-900">
                                        <?php echo esc_html($service->name); ?>
                                    </h3>
                                    <!-- Doctor's name (optional) -->
                                    <?php if (!empty($service->doctor_name)): ?>
                                        <p class="text-sm text-indigo-600 mt-1">
                                            Dr <?php echo esc_html($service->doctor_name); ?>
                                        </p>
                                    <?php endif; ?>
                                </div>

                                <!-- Price info -->
                                <?php if ($showServicePriceStatus): ?>
                                    <div class="ml-2 text-right">
                                        <span class="text-lg font-semibold text-gray-900">
                                            <?php echo esc_html($service->charges); ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Middle row: duration & virtual/in-clinic badges -->
                            <div class="mt-3 flex items-center space-x-2">
                                <?php if (!empty($service->duration) && $showServiceDurationStatus): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-600">
                                        <?php echo esc_html($service->duration); ?> min
                                    </span>
                                <?php endif; ?>

                                <?php if ($service->telemed_service === 'yes'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-green-50 text-green-600">
                                        Virtual
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-50 text-blue-600">
                                        In-Clinic
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Optional service description (Pro) -->
                            <?php if (isKiviCareProActive() && !empty($service->description)): ?>
                                <div class="mt-2 text-sm text-gray-500">
                                    <?php echo esc_html($service->description); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Next available date (hard-coded or dynamic) -->
                            <div class="mt-3 text-sm text-gray-500 text-left">
                                <?php echo $next_available_date;?>
                            </div>
                        </label>
                    </div>

                <?php } ?>

            <?php
        }
        ?> </div> <?php
        return ob_get_clean();
    }
}

