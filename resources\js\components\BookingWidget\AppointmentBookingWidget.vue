<template>
  <div class="kivi-booking-widget">
    <div class="kivi-booking-container">
      <!-- Booking Widget Header with Modern Step Indicator -->
      <div class="kivi-booking-header">
        <div class="border-b">
          <div class="flex px-6 py-4 overflow-x-auto justify-center">
            <!-- Clinic Step (Blue) - Hide if clinic is preselected from URL -->
            <div class="flex items-center" v-if="!hideClinicStep">
              <div class="flex items-center" :class="{'text-blue-600': currentStep >= 0, 'text-gray-400': currentStep < 0}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-blue-600 bg-blue-50': currentStep >= 0, 'text-gray-400 bg-gray-50': currentStep < 0}"
                     @click="goToStep(0)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.3-4.3"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-blue-600': currentStep >= 0, 'text-gray-400': currentStep < 0}">Clinic</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Category Step (Purple) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-indigo-600': currentStep >= 1, 'text-gray-400': currentStep < 1}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-indigo-600 bg-indigo-50': currentStep >= 1, 'text-gray-400 bg-gray-50': currentStep < 1}"
                     @click="goToStep(1)">
                  <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <rect x="4" y="4" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="14" y="4" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="4" y="14" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                    <rect x="14" y="14" width="6" height="6" rx="1" fill="none" stroke="currentColor" stroke-width="2"></rect>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-indigo-600': currentStep >= 1, 'text-gray-400': currentStep < 1}">Category</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Service Step (Purple) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-purple-600': currentStep >= 2, 'text-gray-400': currentStep < 2}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-purple-600 bg-purple-50': currentStep >= 2, 'text-gray-400 bg-gray-50': currentStep < 2}"
                     @click="goToStep(2)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-purple-600': currentStep >= 2, 'text-gray-400': currentStep < 2}">Service</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Time Step (Blue) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-blue-600': currentStep >= 3, 'text-gray-400': currentStep < 3}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-blue-600 bg-blue-50': currentStep >= 3, 'text-gray-400 bg-gray-50': currentStep < 3}"
                     @click="goToStep(3)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M8 2v4"></path>
                    <path d="M16 2v4"></path>
                    <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                    <path d="M3 10h18"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-blue-600': currentStep >= 3, 'text-gray-400': currentStep < 3}">Time</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Details Step (Pink) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-pink-600': currentStep >= 4, 'text-gray-400': currentStep < 4}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-pink-600 bg-pink-50': currentStep >= 4, 'text-gray-400 bg-gray-50': currentStep < 4}"
                     @click="goToStep(4)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path>
                    <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                    <path d="M10 9H8"></path>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-pink-600': currentStep >= 4, 'text-gray-400': currentStep < 4}">Details</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Login Step (Gray) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-gray-600': currentStep >= 5, 'text-gray-400': currentStep < 5}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-gray-600 bg-gray-100': currentStep >= 5, 'text-gray-400 bg-gray-50': currentStep < 5}"
                     @click="goToStep(5)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M2 18v3c0 .6.4 1 1 1h4v-3h3v-3h2l1.4-1.4a6.5 6.5 0 1 0-4-4Z"></path>
                    <circle cx="16.5" cy="7.5" r=".5"></circle>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-gray-600': currentStep >= 5, 'text-gray-400': currentStep < 5}">Login</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 mx-3 text-gray-300">
                <path d="m9 18 6-6-6-6"></path>
              </svg>
            </div>

            <!-- Confirm Step (Green) -->
            <div class="flex items-center">
              <div class="flex items-center" :class="{'text-green-600': currentStep >= 6, 'text-gray-400': currentStep < 6}">
                <div class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-300"
                     :class="{'text-green-600 bg-green-50': currentStep >= 6, 'text-gray-400 bg-gray-50': currentStep < 6}"
                     @click="goToStep(6)">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <path d="m9 11 3 3L22 4"></path>
                  </svg>
                </div>
                <span class="hidden md:inline-block ml-2 text-sm font-medium transition-colors duration-300"
                      :class="{'text-green-600': currentStep >= 6, 'text-gray-400': currentStep < 6}">Confirm</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Booking Widget Body -->
      <div class="kivi-booking-body">
        <div class="form-card">
          <keep-alive>
            <component
              :is="currentComponent"
              :booking-data="bookingData"
              @update:booking-data="updateBookingData"
              @next-step="nextStep"
              @prev-step="prevStep"
              @go-to-step="goToStep"
              @go-to-login-register="goToLoginStep"
              @time-selected="handleTimeSelected"
              @appointment-booked="handleAppointmentBooked"
              @user-authenticated="handleAuthentication"
              @payment-method-selected="setPaymentMethod"
              @payment-success="handlePaymentSuccess"
              @payment-error="handlePaymentError"
              ref="currentComponent"
            ></component>
          </keep-alive>
        </div>
      </div>

      <!-- Navigation Buttons -->
      <div class="flex justify-between mt-6 pt-6 mx-5 mb-5 border-t" v-if="!isPaymentProcessing">
        <button
          v-if="currentStep > 0"
          @click="prevStep"
          class="px-4 py-2 text-sm text-gray-600 hover:text-gray-900"
          data-step="prev"
        >
          Back
        </button>
        <div v-else class="px-4 py-2"></div> <!-- Empty div to maintain spacing when back button is hidden -->

        <button
          v-if="currentStep < steps.length - 1 || (currentStep === steps.length - 1 && !showSuccessOrErrorScreen)"
          @click="handleNextButtonClick"
          class="px-4 py-2 text-sm text-white rounded-md transition-colors duration-300"
          :class="{
            'bg-pink-600 hover:bg-pink-700': currentStep === 4 || currentStep === 5,
            'bg-green-600 hover:bg-green-700': currentStep === steps.length - 1,
            'bg-blue-600 hover:bg-blue-700': currentStep !== 4 && currentStep !== 5 && currentStep !== steps.length - 1
          }"
          :disabled="(!isCurrentStepValid && currentStep !== 5) || isLoading || processingDetailsStep"
          data-step="next"
        >
          {{ getButtonText() }}
        </button>
      </div>

      <!-- Trust badge -->
      <div class="trust-badge">
        <svg class="shield-icon" viewBox="0 0 24 24">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"></path>
        </svg>
        <span class="badge-text">Encrypted & HIPAA Compliant</span>
      </div>
    </div>
  </div>
</template>

<script>
import ClinicStep from './Steps/ClinicStep.vue';
import CategoryStep from './Steps/CategoryStep.vue';
import ServiceStep from './Steps/ServiceStep.vue';
import DateTimeStep from './Steps/DateTimeStep.vue';
import AppointmentDetailsStep from './Steps/AppointmentDetailsStep.vue';
import ConfirmationStep from './Steps/ConfirmationStep.vue';
import LoginRegisterStep from './Steps/LoginRegisterStep.vue';
import { get, post } from '../../config/request';

export default {
  name: 'AppointmentBookingWidget',
  components: {
    ClinicStep,
    CategoryStep,
    ServiceStep,
    DateTimeStep,
    AppointmentDetailsStep,
    LoginRegisterStep,
    ConfirmationStep
  },
  props: {
    presetClinicId: {
      type: [String, Number],
      default: 0
    },
    presetCategoryId: {
      type: String,
      default: ''
    },
    presetServiceId: {
      type: [String, Number],
      default: 0
    },
    presetDoctorId: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      currentStep: 0,
      steps: [
        { id: 'clinic', label: 'Clinic', component: 'ClinicStep', valid: false },
        { id: 'category', label: 'Category', component: 'CategoryStep', valid: false },
        { id: 'services', label: 'Service', component: 'ServiceStep', valid: false },
        { id: 'datetime', label: 'Time', component: 'DateTimeStep', valid: false },
        { id: 'details', label: 'Details', component: 'AppointmentDetailsStep', valid: false },
        { id: 'login', label: 'Login', component: 'LoginRegisterStep', valid: false },
        { id: 'confirm', label: 'Confirm', component: 'ConfirmationStep', valid: true }
      ],
      bookingData: {
        clinic: null,
        category: null,
        services: [],
        doctor: null,
        date: null,
        time: null,
        patient: {
          name: '',
          email: '',
          phone: '',
          notes: ''
        }
      },
      isLoading: false,
      isPaymentProcessing: false,
      isAuthenticated: false,
      appointmentId: null,
      paymentMethod: null,
      processingDetailsStep: false,
      hideClinicStep: false // Flag to hide clinic step when preselected from URL
    };
  },
  computed: {
    currentComponent() {
      return this.steps[this.currentStep].component;
    },
    isCurrentStepValid() {
      const currentStepData = this.steps[this.currentStep];

      if (!currentStepData) return false;

      switch (currentStepData.id) {
        case 'clinic':
          return this.bookingData.clinic !== null && this.bookingData.clinic.id;
        case 'category':
          return this.bookingData.category !== null && this.bookingData.category.id;
        case 'services':
          return this.bookingData.services &&
                 this.bookingData.services.length > 0 &&
                 this.bookingData.services.every(service => service.id || service.service_id);
        case 'datetime':
          return this.bookingData.date !== null &&
                 this.bookingData.time !== null &&
                 this.bookingData.date.trim() !== '' &&
                 this.bookingData.time.trim() !== '';
        case 'details':
          // Check if required fields are filled
          return this.bookingData.description !== undefined; // Allow empty description
        case 'login':
          return true; // Login step is always valid for navigation
        case 'confirm':
          // Confirmation step requires all previous steps to be valid
          return this.bookingData.clinic &&
                 this.bookingData.services &&
                 this.bookingData.services.length > 0 &&
                 this.bookingData.date &&
                 this.bookingData.time;
        default:
          return false;
      }
    },
    isConfirmationPage() {
      return this.currentStep === this.steps.length - 1;
    },
    showSuccessOrErrorScreen() {
      // Check if we're on the confirmation step and if the current component has success or error screens showing
      if (this.currentStep === this.steps.length - 1 && this.$refs.currentComponent) {
        return this.$refs.currentComponent.showSuccessMessage || this.$refs.currentComponent.showErrorMessage;
      }
      return false;
    }
  },
  created() {
    // Handle URL parameters for direct navigation
    this.handleUrlParameters();

    // Initialize with preset values if provided
    this.initializePresetValues();

    // Check if user is already authenticated
    this.checkUserAuthentication();
  },

  mounted() {
    // Remove hash from URL if present
    this.removeHashFromUrl();

    // Check if we should hide the clinic step
    this.checkIfShouldHideClinicStep();

    // If we have a preset clinic ID and startFromCategory is true,
    // preload the clinic and go to category step
    if (this.presetClinicId && this.startFromCategory) {
      this.preloadClinic(this.presetClinicId);
    }

    // Also check URL path for clinic name
    this.checkUrlForClinicName();

    // Listen for the clinic-selected-from-url event
    this.$root.$on('clinic-selected-from-url', this.handleClinicSelectedFromUrl);
  },

  beforeDestroy() {
    // Clean up event listener
    this.$root.$off('clinic-selected-from-url', this.handleClinicSelectedFromUrl);
  },
  methods: {
    getButtonText() {
      // Return appropriate button text based on current step
      if (this.currentStep === this.steps.length - 1) {
        return 'Confirm Appointment';
      } else if (this.currentStep === this.steps.length - 2) {
        return 'Next';
      } else {
        return 'Next';
      }
    },
    handleUrlParameters() {
      const urlParams = new URLSearchParams(window.location.search);
      const step = urlParams.get('step');
      const clinicId = urlParams.get('clinic_id');
      const doctorId = urlParams.get('doctor_id');
      const serviceId = urlParams.get('service_id');

      // Check for hash fragment that might contain additional parameters
      let hashStep = null;
      if (window.location.hash) {
        // Remove the # and / characters
        const hashValue = window.location.hash.replace(/^#\/?/, '');
        if (hashValue) {
          // If the hash is a valid step name, use it
          if (['clinic', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(hashValue)) {
            hashStep = hashValue;
          }
        }
      }

      // Check for clinic name in URL path
      let clinicSlug = null;

      // Get the full URL (including hash)
      const fullUrl = window.location.href;

      // Extract the path part (before any hash or query string)
      const urlWithoutHash = fullUrl.split('#')[0].split('?')[0];

      // Extract path segments
      const pathSegments = urlWithoutHash.replace(/https?:\/\/[^\/]+/, '').split('/').filter(segment => segment);

      // Check the last segment first
      if (pathSegments.length > 0) {
        const lastSegment = pathSegments[pathSegments.length - 1];

        // If the last segment is not a known endpoint, it might be a clinic name
        if (lastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(lastSegment)) {
          clinicSlug = lastSegment;
        }
        // If we didn't find a clinic name in the last segment and there are at least 2 segments,
        // check the second-to-last segment
        else if (pathSegments.length > 1) {
          const secondToLastSegment = pathSegments[pathSegments.length - 2];
          if (secondToLastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(secondToLastSegment)) {
            clinicSlug = secondToLastSegment;
          }
        }
      }

      // Store any available parameters for later use
      this.bookingData.urlParams = {};

      // Use step from query parameter or hash fragment
      if (step) this.bookingData.urlParams.step = step;
      else if (hashStep) this.bookingData.urlParams.step = hashStep;
      // If we found a clinic slug, set the step to 'category'
      else if (clinicSlug) this.bookingData.urlParams.step = 'category';

      // Validate and store IDs as numbers
      if (clinicId && !isNaN(clinicId)) {
        this.bookingData.urlParams.clinicId = parseInt(clinicId);
      }
      if (doctorId && !isNaN(doctorId)) {
        this.bookingData.urlParams.doctorId = parseInt(doctorId);
      }
      if (serviceId && !isNaN(serviceId)) {
        this.bookingData.urlParams.serviceId = parseInt(serviceId);
      }

      // Store clinic slug from URL if found
      if (clinicSlug) {
        this.bookingData.urlParams.clinicSlug = clinicSlug;
        console.log('Clinic slug from URL:', clinicSlug);
      }

      // Log the parameters we found
      console.log('URL parameters detected:', this.bookingData.urlParams);
    },

    initializePresetValues() {
      // Process URL parameters for direct navigation if any are available
      if (this.bookingData.urlParams && Object.keys(this.bookingData.urlParams).length > 0) {
        console.log('Initializing with URL parameters:', this.bookingData.urlParams);

        // Create a promise array to track all data fetching
        const fetchPromises = [];

        // If we have a clinic slug from the URL, we'll handle it in the ClinicStep component
        // Just set a flag to hide the clinic step
        if (this.bookingData.urlParams.clinicSlug) {
          console.log('Hiding clinic step because we have a clinic slug in URL params');
          this.hideClinicStep = true;
        }
        // Fetch clinic details if clinic ID is provided
        else if (this.bookingData.urlParams.clinicId) {
          fetchPromises.push(
            new Promise(resolve => {
              this.fetchClinicDetails(this.bookingData.urlParams.clinicId, resolve);
            })
          );
        }

        // Fetch doctor details if doctor ID is provided
        if (this.bookingData.urlParams.doctorId) {
          fetchPromises.push(
            new Promise(resolve => {
              this.fetchDoctorDetails(this.bookingData.urlParams.doctorId, resolve);
            })
          );
        }

        // Fetch service details if service ID is provided
        if (this.bookingData.urlParams.serviceId) {
          fetchPromises.push(
            new Promise(resolve => {
              this.fetchServiceDetails(this.bookingData.urlParams.serviceId, resolve);
            })
          );
        }

        // Wait for all data to be fetched before navigating
        Promise.all(fetchPromises).then(() => {
          console.log('All data fetched, preparing to navigate');
          this.debugBookingData();

          // Map step name to step index
          const stepMap = {
            'clinic': 0,
            'category': 1,
            'services': 2,
            'datetime': 3,
            'details': 4,
            'login': 5,
            'confirm': 6
          };

          // Navigate to the specified step if provided
          if (this.bookingData.urlParams.step) {
            if (stepMap[this.bookingData.urlParams.step] !== undefined) {
              console.log(`Navigating to step: ${this.bookingData.urlParams.step} (index: ${stepMap[this.bookingData.urlParams.step]})`);
              this.goToStep(stepMap[this.bookingData.urlParams.step]);
            }
          }
          // Handle different parameter combinations
          else if (this.bookingData.urlParams.clinicId && this.bookingData.clinic) {
            // Make sure the clinic step is valid
            this.steps[0].valid = true;

            // Case: All three parameters (clinic_id, doctor_id, service_id)
            if (this.bookingData.urlParams.clinicId && this.bookingData.urlParams.doctorId &&
                this.bookingData.urlParams.serviceId && this.bookingData.services &&
                this.bookingData.services.length > 0 && this.bookingData.doctor) {

              console.log('All parameters provided, automatically advancing to datetime step');
              // Make sure all previous steps are valid
              this.steps[0].valid = true; // clinic
              this.steps[1].valid = true; // category
              this.steps[2].valid = true; // services

              // Go to the datetime step
              this.goToStep(3);
            }
            // Case: only clinic_id (go to category step)
            else {
              console.log('Only clinic_id provided, automatically advancing to category step');
              // Go to the category step
              this.goToStep(1);
            }
          }
        });
      }
    },

    // Debug method to log the current booking data
    debugBookingData() {
      console.log('Current booking data:');
      console.log('- Clinic:', this.bookingData.clinic ? `ID: ${this.bookingData.clinic.id}, Name: ${this.bookingData.clinic.name}` : 'Not set');
      console.log('- Doctor:', this.bookingData.doctor ? `ID: ${this.bookingData.doctor.id}, Name: ${this.bookingData.doctor.name}` : 'Not set');
      console.log('- Services:', this.bookingData.services && this.bookingData.services.length > 0
        ? this.bookingData.services.map(s => `ID: ${s.id}, Service ID: ${s.service_id}, Name: ${s.name}, Doctor ID: ${s.doctor_id}`).join(', ')
        : 'Not set');
      console.log('- Category:', this.bookingData.category ? `ID: ${this.bookingData.category.id}, Name: ${this.bookingData.category.name}` : 'Not set');
      console.log('- Date:', this.bookingData.date || 'Not set');
      console.log('- Time:', this.bookingData.time || 'Not set');

      // Check if steps are valid
      console.log('Step validation status:');
      this.steps.forEach(step => {
        console.log(`- ${step.id}: ${step.valid ? 'Valid' : 'Invalid'}`);
      });

      // Log the current step
      console.log(`Current step: ${this.currentStep} (${this.steps[this.currentStep].id})`);
    },

    async preloadClinic(clinicId) {
      try {
        this.isLoading = true;

        // Fetch clinic details
        const response = await get('get_clinic_detail', { id: clinicId });

        if (response.data.status) {
          // Handle different response formats
          let clinic;
          if (Array.isArray(response.data.data)) {
            // If it's an array, find the clinic with the matching ID
            clinic = response.data.data.find(c => c.id == clinicId);
          } else if (response.data.data && response.data.data.id) {
            // If it's a single object
            clinic = response.data.data;
          }

          if (clinic) {
            // Set the clinic in booking data
            this.bookingData.clinic = {
              id: clinic.id,
              name: clinic.name,
              address: clinic.address || ''
            };

            // Mark the clinic step as valid
            this.steps[0].valid = true;

            // Skip to category step
            this.goToStep(1);
          }
        }
      } catch (error) {
        console.error('Error preloading clinic:', error);
      } finally {
        this.isLoading = false;
      }
    },

    fetchClinicDetails(clinicId, callback) {
      console.log('Fetching clinic details for ID:', clinicId);
      get('get_clinic_detail', { id: clinicId })
        .then(response => {
          if (response.data.status) {
            console.log('Clinic details received:', response.data.data);

            // Handle different response formats
            if (Array.isArray(response.data.data)) {
              // If it's an array, find the clinic with the matching ID
              const clinic = response.data.data.find(c => c.id == clinicId);
              if (clinic) {
                this.bookingData.clinic = {
                  id: clinic.id,
                  name: clinic.name,
                  address: clinic.address || ''
                };
              } else {
                // If not found in the array, create a minimal clinic object
                this.bookingData.clinic = {
                  id: clinicId,
                  name: 'Clinic',
                  address: ''
                };
              }
            } else if (response.data.data && response.data.data.id) {
              // If it's a single object
              this.bookingData.clinic = {
                id: response.data.data.id,
                name: response.data.data.name,
                address: response.data.data.address || ''
              };
            } else {
              // Fallback to minimal clinic object
              this.bookingData.clinic = {
                id: clinicId,
                name: 'Clinic',
                address: ''
              };
            }

            // Mark the clinic step as valid
            this.steps[0].valid = true;

            console.log('Updated booking data with clinic:', this.bookingData);
          } else {
            // If API call failed, still create a minimal clinic object
            console.log('Clinic API call failed, creating minimal clinic object');
            this.bookingData.clinic = {
              id: clinicId,
              name: 'Clinic',
              address: ''
            };

            // Mark the clinic step as valid
            this.steps[0].valid = true;
          }
        })
        .catch(error => {
          console.error('Error fetching clinic details:', error);
          // On error, still create a minimal clinic object
          this.bookingData.clinic = {
            id: clinicId,
            name: 'Clinic',
            address: ''
          };

          // Mark the clinic step as valid
          this.steps[0].valid = true;
        })
        .finally(() => {
          // Call the callback if provided
          if (typeof callback === 'function') {
            callback();
          }
        });
    },

    fetchDoctorDetails(doctorId, callback) {
      console.log('Fetching doctor details for ID:', doctorId);
      get('get_doctor_detail', { id: doctorId })
        .then(response => {
          if (response.data.status) {
            console.log('Doctor details received:', response.data.data);

            // If we got empty data, create a minimal doctor object with the ID
            if (!response.data.data || (Array.isArray(response.data.data) && response.data.data.length === 0)) {
              console.log('No doctor details found, creating minimal doctor object');
              this.bookingData.doctor = {
                id: doctorId,
                name: 'Doctor'
              };
            } else {
              this.bookingData.doctor = {
                id: response.data.data.id || doctorId,
                name: response.data.data.display_name || response.data.data.name || 'Doctor'
              };
            }

            // Update doctor_id in services if we have services
            if (this.bookingData.services && this.bookingData.services.length > 0) {
              this.bookingData.services.forEach(service => {
                if (!service.doctor_id) {
                  service.doctor_id = doctorId;
                  service.doctor_name = this.bookingData.doctor.name;
                }
              });
            }

            console.log('Updated booking data with doctor:', this.bookingData);
          } else {
            // If API call failed, still create a minimal doctor object
            console.log('Doctor API call failed, creating minimal doctor object');
            this.bookingData.doctor = {
              id: doctorId,
              name: 'Doctor'
            };
          }
        })
        .catch(error => {
          console.error('Error fetching doctor details:', error);
          // On error, still create a minimal doctor object
          this.bookingData.doctor = {
            id: doctorId,
            name: 'Doctor'
          };
        })
        .finally(() => {
          // Call the callback if provided
          if (typeof callback === 'function') {
            callback();
          }
        });
    },

    fetchServiceDetails(serviceId, callback) {
      console.log('Fetching service details for ID:', serviceId);
      get('get_service_detail', { id: serviceId })
        .then(response => {
          if (response.data.status) {
            const service = response.data.data;
            console.log('Service details received:', service);

            // If we got empty data, create a minimal service object with the ID
            if (!service || (Array.isArray(service) && service.length === 0)) {
              console.log('No service details found, creating minimal service object');
              this.bookingData.services = [{
                id: serviceId,
                service_id: serviceId,
                name: 'Service',
                price: '0',
                duration: '30',
                telemed_service: 'no',
                doctor_id: this.bookingData.urlParams.doctorId || null,
                doctor_name: ''
              }];
            } else {
              // Format the service data for the booking widget
              this.bookingData.services = [{
                id: service.id || serviceId,
                service_id: service.id || serviceId, // Add service_id for API compatibility
                name: service.name || 'Service',
                price: service.charges || '0',
                duration: service.duration || '30',
                telemed_service: service.telemed_service || 'no',
                doctor_id: service.doctor_id || this.bookingData.urlParams.doctorId || null,
                doctor_name: service.doctor_name || ''
              }];

              // If service has a doctor_id and we don't have a doctor from URL params, use it
              if (service.doctor_id && !this.bookingData.urlParams.doctorId) {
                console.log('Using doctor_id from service:', service.doctor_id);
                this.fetchDoctorDetails(service.doctor_id);
              }
            }

            // Mark the services step as valid
            this.steps[2].valid = true;

            // If we have a category from the service, set it
            if (service && service.category) {
              this.bookingData.category = {
                id: service.category.id,
                name: service.category.name
              };

              // Mark the category step as valid
              this.steps[1].valid = true;
            } else {
              // Create a default category if none exists
              this.bookingData.category = {
                id: 1,
                name: 'General'
              };

              // Mark the category step as valid
              this.steps[1].valid = true;
            }

            // If we have a doctor ID from the service or URL, set it
            if ((service && service.doctor_id) || this.bookingData.urlParams.doctorId) {
              const doctorId = (service && service.doctor_id) || this.bookingData.urlParams.doctorId;

              // If we don't already have doctor details, fetch them
              if (!this.bookingData.doctor || this.bookingData.doctor.id !== doctorId) {
                // We'll handle this in the Promise.all in initializePresetValues
                // Don't call fetchDoctorDetails here to avoid duplicate calls
              }
            }
            // If we don't have a doctor ID but we have a service, create a default doctor
            else if (!this.bookingData.doctor && this.bookingData.urlParams.serviceId && !this.bookingData.urlParams.doctorId) {
              console.log('No doctor ID provided with service, creating default doctor');
              // Create a default doctor for the service
              this.bookingData.doctor = {
                id: 0, // Use 0 as a default doctor ID
                name: 'Any Doctor'
              };

              // Update the doctor_id in the service
              if (this.bookingData.services && this.bookingData.services.length > 0) {
                this.bookingData.services.forEach(service => {
                  service.doctor_id = 0;
                  service.doctor_name = 'Any Doctor';
                });
              }
            }

            console.log('Updated booking data with service:', this.bookingData);
          } else {
            // If API call failed, still create a minimal service object
            console.log('Service API call failed, creating minimal service object');
            this.bookingData.services = [{
              id: serviceId,
              service_id: serviceId,
              name: 'Service',
              price: '0',
              duration: '30',
              telemed_service: 'no',
              doctor_id: this.bookingData.urlParams.doctorId || null,
              doctor_name: ''
            }];

            // Mark the services step as valid
            this.steps[2].valid = true;

            // Create a default category
            this.bookingData.category = {
              id: 1,
              name: 'General'
            };

            // Mark the category step as valid
            this.steps[1].valid = true;
          }
        })
        .catch(error => {
          console.error('Error fetching service details:', error);
          // On error, still create a minimal service object
          this.bookingData.services = [{
            id: serviceId,
            service_id: serviceId,
            name: 'Service',
            price: '0',
            duration: '30',
            telemed_service: 'no',
            doctor_id: this.bookingData.urlParams.doctorId || null,
            doctor_name: ''
          }];

          // Mark the services step as valid
          this.steps[2].valid = true;

          // Create a default category
          this.bookingData.category = {
            id: 1,
            name: 'General'
          };

          // Mark the category step as valid
          this.steps[1].valid = true;

          // If we don't have a doctor ID but we have a service, create a default doctor
          if (!this.bookingData.doctor && this.bookingData.urlParams.serviceId && !this.bookingData.urlParams.doctorId) {
            console.log('No doctor ID provided with service (error case), creating default doctor');
            // Create a default doctor for the service
            this.bookingData.doctor = {
              id: 0, // Use 0 as a default doctor ID
              name: 'Any Doctor'
            };

            // Update the doctor_id in the service
            if (this.bookingData.services && this.bookingData.services.length > 0) {
              this.bookingData.services.forEach(service => {
                service.doctor_id = 0;
                service.doctor_name = 'Any Doctor';
              });
            }
          }
        })
        .finally(() => {
          // Call the callback if provided
          if (typeof callback === 'function') {
            callback();
          }
        });
    },

    updateBookingData(newData) {
      this.bookingData = { ...newData };
    },

    handleClinicSelectedFromUrl(data) {
      // Hide the clinic step
      this.hideClinicStep = true;

      // Make sure the clinic step is marked as valid
      this.steps[0].valid = true;

      // Go to the category step
      this.goToStep(1);

      // If we have a clinic ID, update the booking data
      if (data && data.clinicId) {
        // Update the booking data with the selected clinic
        if (!this.bookingData.clinic || this.bookingData.clinic.id !== data.clinicId) {
          this.bookingData.clinic = {
            id: data.clinicId,
            name: data.clinicName || 'Selected Clinic',
            address: ''
          };
        }
      }
    },

    removeHashFromUrl() {
      // Check if the URL has a hash
      if (window.location.hash) {
        // Get the current URL without the hash
        const urlWithoutHash = window.location.href.split('#')[0];

        // Replace the current URL without the hash
        if (window.history && window.history.replaceState) {
          window.history.replaceState('', document.title, urlWithoutHash);
        }
      }
    },

    checkIfShouldHideClinicStep() {
      // Hide clinic step if we have a preset clinic ID and startFromCategory is true
      if (this.presetClinicId && this.startFromCategory) {
        console.log('Hiding clinic step because we have a preset clinic ID and startFromCategory is true');
        this.hideClinicStep = true;
      }
    },

    checkUrlForClinicName() {
      // Get the full URL (including hash)
      const fullUrl = window.location.href;

      // Extract the path part (before any hash or query string)
      const urlWithoutHash = fullUrl.split('#')[0].split('?')[0];

      // Extract path segments
      const pathSegments = urlWithoutHash.replace(/https?:\/\/[^\/]+/, '').split('/').filter(segment => segment);

      let clinicSlug = null;

      // Check the last segment first
      if (pathSegments.length > 0) {
        const lastSegment = pathSegments[pathSegments.length - 1];

        // If the last segment is not a known endpoint, it might be a clinic name
        if (lastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(lastSegment)) {
          clinicSlug = lastSegment;
        }
        // If we didn't find a clinic name in the last segment and there are at least 2 segments,
        // check the second-to-last segment
        else if (pathSegments.length > 1) {
          const secondToLastSegment = pathSegments[pathSegments.length - 2];
          if (secondToLastSegment && !['appointment', 'category', 'services', 'datetime', 'details', 'login', 'confirm'].includes(secondToLastSegment)) {
            clinicSlug = secondToLastSegment;
          }
        }
      }

      // If we found a potential clinic slug, hide the clinic step
      if (clinicSlug) {
        this.hideClinicStep = true;
      }
    },

    async nextStep() {
      console.log('nextStep called, currentStep:', this.currentStep);
      console.log('Current step ID:', this.steps[this.currentStep].id);

      if (this.currentStep < this.steps.length - 1) {
        // Check if we're on the AppointmentDetailsStep
        if (this.steps[this.currentStep].id === 'details') {
          // Get the current component instance
          const currentComponent = this.$refs.currentComponent;

          // Call the submitForm method on the AppointmentDetailsStep component
          if (currentComponent && typeof currentComponent.submitForm === 'function') {
            try {
              console.log('Processing details step...');

              // Prevent multiple simultaneous submissions
              if (this.processingDetailsStep) {
                console.log('Details step already being processed, skipping...');
                return;
              }

              // Set a flag to prevent infinite recursion
              this.processingDetailsStep = true;

              // Call submitForm to validate and update the booking data
              const isValid = await currentComponent.submitForm();

              if (!isValid) {
                console.log('Form validation failed');
                return; // Stop if validation fails
              }

              console.log('Form validation passed, checking authentication...');
              // Check if user is authenticated
              await this.checkUserAuthentication();

              if (this.isAuthenticated) {
                console.log('User is authenticated, making API call...');
                // User is authenticated, make the API call to book appointment
                const success = await this.submitAppointmentData();

                if (success) {
                  console.log('API call successful, moving to confirmation step');
                  // Skip login step and go directly to confirmation
                  const confirmStepIndex = this.steps.findIndex(step => step.id === 'confirm');
                  if (confirmStepIndex !== -1) {
                    this.currentStep = confirmStepIndex;
                    this.scrollToTop();
                    return;
                  }
                } else {
                  console.error('API call failed');
                  // Stay on the current step if API call fails
                  return;
                }
              } else {
                console.log('User is not authenticated, going to login step');
                // User is not authenticated, go to login step
                const loginStepIndex = this.steps.findIndex(step => step.id === 'login');
                if (loginStepIndex !== -1) {
                  this.currentStep = loginStepIndex;
                  this.scrollToTop();
                  return;
                }
              }
            } catch (error) {
              console.error('Error processing details step:', error);
              // Show user-friendly error message
              alert('An error occurred while processing your appointment details. Please try again.');
              return;
            } finally {
              this.processingDetailsStep = false;
            }
          } else {
            console.error('submitForm method not found on AppointmentDetailsStep component');
          }
        }
        // If we're on the login/register step, we need to handle form submission
        else if (this.steps[this.currentStep].id === 'login') {
          // Get the current component reference
          const loginRegisterComponent = this.$refs.currentComponent;

          // Check which form is active and submit it
          if (loginRegisterComponent) {
            if (loginRegisterComponent.isLogin) {
              // Submit login form
              loginRegisterComponent.handleLogin();
            } else {
              // Submit register form
              loginRegisterComponent.handleRegister();
            }
            // The component will emit events to handle navigation after successful login/register
            return;
          }
        }
        // If we're on the confirmation step, we need to handle appointment confirmation
        else if (this.steps[this.currentStep].id === 'confirm') {
          console.log("Processing confirmation step");
          console.log(this.steps[this.currentStep].id);

          // Get the current component reference
          const confirmationComponent = this.$refs.currentComponent;

          // Submit the appointment if component exists and not showing success/error screens
          if (confirmationComponent) {
            // Check if already showing success/error screens
            if (confirmationComponent.showSuccessMessage || confirmationComponent.showErrorMessage) {
              console.log('Already showing success/error message, skipping submission');
              return;
            }

            // Prevent multiple simultaneous submissions
            if (this.isLoading) {
              console.log('Already processing appointment submission, skipping...');
              return;
            }

            // Check if we should show payment page or submit directly
            if (typeof confirmationComponent.showPaymentPage === 'function') {
              console.log('Showing payment page from confirmation step');
              try {
                confirmationComponent.showPaymentPage();
              } catch (error) {
                console.error('Error showing payment page:', error);
                alert('An error occurred while processing payment. Please try again.');
              }
              return;
            } else if (typeof confirmationComponent.submitAppointment === 'function') {
              console.log('Submitting appointment from confirmation step');
              try {
                confirmationComponent.submitAppointment();
              } catch (error) {
                console.error('Error submitting appointment:', error);
                alert('An error occurred while submitting your appointment. Please try again.');
              }
              return;
            } else {
              console.error('Neither showPaymentPage nor submitAppointment methods found on ConfirmationStep component');
              alert('Unable to process appointment. Please refresh the page and try again.');
            }
          } else {
            console.error('ConfirmationStep component reference not found');
            alert('Unable to access confirmation component. Please refresh the page and try again.');
          }
        }
        // If we're about to move to the login step but the user is already authenticated,
        // skip directly to the confirmation step
        else if (this.steps[this.currentStep + 1].id === 'login' && this.isAuthenticated) {
          const confirmStepIndex = this.steps.findIndex(step => step.id === 'confirm');
          if (confirmStepIndex !== -1) {
            this.currentStep = confirmStepIndex;
          } else {
            // If we can't find the confirmation step, just move to the next step
            this.currentStep++;
          }
        } else {
          // Normal case - move to the next step
          this.currentStep++;
        }
        this.scrollToTop();
      }
    },

    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
        this.scrollToTop();
      }
    },

    goToStep(stepIndex) {
      if (stepIndex >= 0 && stepIndex < this.steps.length) {
        this.currentStep = stepIndex;
        this.scrollToTop();
      }
    },

    goToLoginStep() {
      // Find the index of the login step
      const loginStepIndex = this.steps.findIndex(step => step.id === 'login');
      if (loginStepIndex !== -1) {
        this.goToStep(loginStepIndex);
      } else {
        console.error('Login step not found');
      }
    },

    scrollToTop() {
      // Scroll to the top of the booking widget
      this.$nextTick(() => {
        const element = document.querySelector('.kivi-booking-widget');
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    },

    handleTimeSelected(timeData) {
      // Update booking data with selected date and time
      this.bookingData.date = timeData.date;
      this.bookingData.time = timeData.time;

      console.log('Time selected:', timeData);
    },

    handleAppointmentBooked(appointmentData) {
      // Handle successful appointment booking
      console.log('Appointment booked:', appointmentData);

      // Log the full response for debugging
      console.log('Full appointment booked response:', JSON.stringify(appointmentData, null, 2));

      // Store the appointment ID
      this.appointmentId = appointmentData.id || (appointmentData.data && appointmentData.data.appointment_id) || appointmentData.appointment_id;

      // If we don't have an appointment ID, try to extract it from the response
      if (!this.appointmentId && appointmentData.data) {
        // Try to find appointment_id in the data object
        if (typeof appointmentData.data === 'object') {
          this.appointmentId = appointmentData.data.id || appointmentData.data.appointment_id;
        }
      }

      console.log('Extracted appointment ID:', this.appointmentId);

      // Check if we have checkout details for payment
      const checkoutDetail = appointmentData.checkout_detail ||
                            (appointmentData.data && appointmentData.data.checkout_detail);

      // If payment is required, process payment
      if (this.paymentMethod === 'paymentStripepay' && checkoutDetail) {
        // Store appointment ID in session storage for retrieval after payment
        sessionStorage.setItem('kivicare_appointment_id', this.appointmentId);

        // Set payment processing flag
        this.isPaymentProcessing = true;

        // Redirect to Stripe checkout
        window.location.href = checkoutDetail.stripe_redirect_url;
      } else {
        // No payment needed or payment at clinic, move to confirmation
        this.goToStep(this.steps.length - 1);
      }
    },

    async handleAuthentication(userData) {
      // Handle successful authentication
      this.isAuthenticated = true;
      console.log('User authenticated:', userData);

      // If the userData contains new tokens, update them globally
      if (userData.token) {
        console.log('Received new tokens from authentication:', userData.token);

        // Update nonces in window objects
        if (window.ajaxData) {
          if (userData.token.get) window.ajaxData.get_nonce = userData.token.get;
          if (userData.token.post) window.ajaxData.post_nonce = userData.token.post;
          console.log('Updated nonces in window.ajaxData', window.ajaxData);
        }

        if (window.request_data) {
          if (userData.token.get) window.request_data.get_nonce = userData.token.get;
          if (userData.token.post) window.request_data.nonce = userData.token.post;
          console.log('Updated nonces in window.request_data', window.request_data);
        }
      }

      // If we have an appointment ID in session storage, it means we came from payment
      const appointmentId = sessionStorage.getItem('kivicare_appointment_id');
      if (appointmentId) {
        // Update appointment payment status
        this.updateAppointmentPaymentStatus(appointmentId);
      }

      // Submit appointment data to get confirmation HTML before moving to confirmation step
      const success = await this.submitAppointmentData();
      console.log('Appointment data submitted after authentication:', success);

      // Skip login step and move to confirmation step
      // Find the index of the confirmation step
      const confirmStepIndex = this.steps.findIndex(step => step.id === 'confirm');
      if (confirmStepIndex !== -1) {
        this.goToStep(confirmStepIndex);
      } else {
        // Fallback to just moving to the next step
        this.nextStep();
      }
    },

    async updateAppointmentPaymentStatus(appointmentId) {
      try {
        // Update the appointment payment status to approved
        const response = await post('save_appointment_payment_status', {
          appointment_id: appointmentId,
          payment_status: 'approved'
        });

        if (response.data.status) {
          console.log('Appointment payment status updated successfully');
          // Clear the appointment ID from session storage
          sessionStorage.removeItem('kivicare_appointment_id');
        } else {
          console.error('Failed to update appointment payment status:', response.data.message);
        }
      } catch (error) {
        console.error('Error updating appointment payment status:', error);
      }
    },

    setPaymentMethod(method) {
      this.paymentMethod = method;
    },

    async submitAppointmentData() {
      try {
        console.log('Submitting appointment data...');
        this.isLoading = true;

        // Get the ajaxurl and nonce with improved fallback
        const ajaxurl = window.ajaxurl || 'https://medroid.ai/ehr/wp-admin/admin-ajax.php';
        let nonce = '';

        // Try to get nonce from different possible sources with better error handling
        if (window.ajaxData && window.ajaxData.post_nonce) {
          nonce = window.ajaxData.post_nonce;
          console.log('Using nonce from window.ajaxData.post_nonce:', nonce);
        } else if (window.ajaxData && window.ajaxData.nonce) {
          nonce = window.ajaxData.nonce;
          console.log('Using nonce from window.ajaxData.nonce:', nonce);
        } else if (window.request_data && window.request_data.nonce) {
          nonce = window.request_data.nonce;
          console.log('Using nonce from window.request_data.nonce:', nonce);
        }

        if (!nonce) {
          console.error('No nonce available for appointment submission');
          throw new Error('Authentication nonce not available');
        }

        console.log('Current nonce values:', {
          'window.ajaxData.post_nonce': window.ajaxData?.post_nonce,
          'window.ajaxData.get_nonce': window.ajaxData?.get_nonce,
          'window.request_data.nonce': window.request_data?.nonce
        });

        // Validate required booking data
        if (!this.bookingData.clinic?.id) {
          throw new Error('Clinic selection is required');
        }
        if (!this.bookingData.services || this.bookingData.services.length === 0) {
          throw new Error('Service selection is required');
        }
        if (!this.bookingData.date || !this.bookingData.time) {
          throw new Error('Date and time selection is required');
        }

        // Get the selected clinic, doctor, service, time and date from the booking data
        const clinicId = this.bookingData.clinic.id;
        const doctorId = this.bookingData.doctor?.id || '';
        const serviceList = this.bookingData.services.map(s => s.service_id || s.id);
        const time = this.bookingData.time;
        const date = this.bookingData.date;
        const description = this.bookingData.description || '';

        // Prepare custom field data
        const customField = this.bookingData.customField || {};

        // Prepare the data for the API call in the exact format required by the API
        const params = {
          action: 'ajax_post',
          route_name: 'appointment_confirm_page',
          clinic_id: clinicId,
          doctor_id: doctorId,
          service_list: serviceList,
          time: this.formatTimeToAMPM(time),
          date: date,
          description: description,
          file: [],
          custom_field: customField,
          _ajax_nonce: nonce
        };

        console.log('Making API call with params:', params);

        // Make the API request with improved error handling
        const response = await fetch(ajaxurl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams(this.prepareFormData(params))
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();

        if (responseData.status) {
          console.log('Appointment data submitted successfully:', responseData);

          // Store the appointment data for the confirmation step
          this.appointmentId = responseData.data?.appointment_id || null;

          // Store the confirmation HTML in the booking data
          this.bookingData.confirmationHtml = responseData.data;
          this.bookingData.taxDetails = responseData.tax_details || [];

          // Emit the confirmation page loaded event
          this.$emit('confirmation-page-loaded', {
            html: responseData.data,
            taxDetails: responseData.tax_details || []
          });

          // Also emit to the root event bus for components that might be listening
          this.$root.$emit('confirmation-page-loaded', {
            html: responseData.data,
            taxDetails: responseData.tax_details || []
          });

          return true;
        } else {
          console.error('Error in API response:', responseData);
          return false;
        }
      } catch (error) {
        console.error('Error submitting appointment data:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // Helper method to prepare form data for submission
    prepareFormData(params) {
      const formData = {};

      // Handle simple key-value pairs
      Object.keys(params).forEach(key => {
        if (key !== 'service_list' && key !== 'custom_field' && key !== 'file' && key !== 'visit_type') {
          formData[key] = params[key];
        }
      });

      // Handle service_list array
      if (params.service_list && Array.isArray(params.service_list)) {
        params.service_list.forEach((service, index) => {
          formData[`service_list[${index}]`] = service;
        });
      }

      // Handle visit_type array
      if (params.visit_type && Array.isArray(params.visit_type)) {
        params.visit_type.forEach((service, index) => {
          formData[`visit_type[${index}]`] = service;
        });
      }

      // Handle custom_field object
      if (params.custom_field) {
        Object.keys(params.custom_field).forEach(key => {
          formData[`custom_field[${key}]`] = params.custom_field[key];
        });
      }

      return formData;
    },

    async checkUserAuthentication() {
      try {
        console.log('Checking user authentication status...');

        // Method 1: Check if WordPress has a logged-in user via ajaxData
        if (window.ajaxData && window.ajaxData.is_user_logged_in === 'yes') {
          console.log('User is already logged in according to WordPress ajaxData');
          this.isAuthenticated = true;
          return;
        }

        // Method 2: Try to get the login user details directly with improved error handling
        try {
          const ajaxurl = window.ajaxurl || 'https://medroid.ai/ehr/wp-admin/admin-ajax.php';
          let nonce = '';

          // Get the most current GET nonce with fallback
          if (window.ajaxData && window.ajaxData.get_nonce) {
            nonce = window.ajaxData.get_nonce;
          } else if (window.request_data && window.request_data.get_nonce) {
            nonce = window.request_data.get_nonce;
          }

          if (!nonce) {
            console.log('No nonce available for authentication check');
            this.isAuthenticated = false;
            return;
          }

          console.log('Using GET nonce for login_user_detail:', nonce);

          const params = {
            action: 'ajax_get',
            route_name: 'login_user_detail',
            _ajax_nonce: nonce
          };

          const response = await fetch(`${ajaxurl}?${new URLSearchParams(params).toString()}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const responseData = await response.json();

          if (responseData.status && responseData.data) {
            // Check if data is an empty object
            if (Object.keys(responseData.data).length === 0) {
              console.log('User is not logged in (empty data object)');
              this.isAuthenticated = false;
              return;
            }

            // If data contains user information, user is logged in
            console.log('User is already logged in:', responseData.data);
            this.isAuthenticated = true;

            // Update booking data with user information if available
            if (responseData.data.user_email) {
              this.bookingData.patient = {
                ...this.bookingData.patient,
                name: responseData.data.display_name || responseData.data.user_login || '',
                email: responseData.data.user_email || '',
                phone: responseData.data.mobile_number || '',
                user_id: responseData.data.ID || responseData.data.id || null
              };
            }
            return;
          }
        } catch (error) {
          console.log('Error checking login status:', error);
        }

        // Fallback: Try the user/profile endpoint with improved error handling
        try {
          const getNonce = window.ajaxData?.get_nonce || window.request_data?.get_nonce || '';

          if (!getNonce) {
            console.log('No GET nonce available for user/profile check');
            this.isAuthenticated = false;
            return;
          }

          console.log('Using GET nonce for user/profile:', getNonce);

          // Update the nonce in the global request object if needed
          if (window.request_data) {
            window.request_data.get_nonce = getNonce;
          }

          const response = await get('user/profile');

          if (response.data.status && response.data.data && Object.keys(response.data.data).length > 0) {
            console.log('User is already logged in via user/profile:', response.data.data);
            this.isAuthenticated = true;
            return;
          }
        } catch (error) {
          console.log('Error checking user/profile:', error);
        }

        console.log('User is not authenticated');
        this.isAuthenticated = false;
      } catch (error) {
        console.error('Error checking user authentication:', error);
        this.isAuthenticated = false;
      }
    },

    handleConfirmationPageLoaded(data) {
      console.log('Confirmation page loaded:', data);

      // Store the confirmation HTML in the booking data
      if (data && data.html) {
        this.bookingData.confirmationHtml = data.html;
        this.bookingData.taxDetails = data.taxDetails || [];
      }
    },

    handlePaymentSuccess() {
      console.log('Payment successful');

      try {
        // Show success message
        alert('Payment successful! Your appointment has been confirmed.');

        // Clear any stored appointment ID
        sessionStorage.removeItem('kivicare_appointment_id');
        sessionStorage.removeItem('kivicare_appointment_in_progress');

        // Update the confirmation component to show success
        const confirmationComponent = this.$refs.currentComponent;
        if (confirmationComponent && typeof confirmationComponent.showSuccessMessage !== 'undefined') {
          confirmationComponent.showSuccessMessage = true;
          confirmationComponent.successMessage = 'Payment successful! Your appointment has been confirmed.';
        }

        // Redirect to the home page or a success page after a delay
        setTimeout(() => {
          window.location.href = window.location.origin;
        }, 3000);
      } catch (error) {
        console.error('Error handling payment success:', error);
      }
    },

    handlePaymentError(errorMessage) {
      console.error('Payment error:', errorMessage);

      try {
        // Show error message
        const message = errorMessage || 'Payment failed. Please try again.';
        alert('Payment failed: ' + message);

        // Update the confirmation component to show error
        const confirmationComponent = this.$refs.currentComponent;
        if (confirmationComponent && typeof confirmationComponent.showErrorMessage !== 'undefined') {
          confirmationComponent.showErrorMessage = true;
          confirmationComponent.errorMessage = message;
        }

        // Clear any stored appointment ID on error
        sessionStorage.removeItem('kivicare_appointment_id');
        sessionStorage.removeItem('kivicare_appointment_in_progress');
      } catch (error) {
        console.error('Error handling payment error:', error);
      }
    },

    handleNextButtonClick() {
      console.log('Next button clicked, currentStep:', this.currentStep);
      console.log('Current step ID:', this.steps[this.currentStep].id);

      // If we're on the confirmation step, handle it specially
      if (this.steps[this.currentStep].id === 'confirm') {
        console.log('On confirmation step, getting component reference');
        const confirmationComponent = this.$refs.currentComponent;

        if (confirmationComponent) {
          console.log('Confirmation component found:', confirmationComponent);
          console.log('Has showPaymentPage method:', typeof confirmationComponent.showPaymentPage === 'function');
          console.log('Has submitAppointment method:', typeof confirmationComponent.submitAppointment === 'function');

          // Check if already showing success/error screens
          if (confirmationComponent.showSuccessMessage || confirmationComponent.showErrorMessage) {
            console.log('Already showing success/error message, skipping submission');
            return;
          }

          // Call showPaymentPage directly
          if (typeof confirmationComponent.showPaymentPage === 'function') {
            console.log('Calling showPaymentPage directly');
            confirmationComponent.showPaymentPage();
            return;
          } else if (typeof confirmationComponent.submitAppointment === 'function') {
            console.log('Calling submitAppointment directly');
            confirmationComponent.submitAppointment();
            return;
          } else {
            console.error('Neither showPaymentPage nor submitAppointment methods found on ConfirmationStep component');
          }
        } else {
          console.error('ConfirmationStep component reference not found');
        }
      } else {
        // For other steps, just call nextStep
        this.nextStep();
      }
    },

    // Format time to AM/PM format
    formatTimeToAMPM(timeStr) {
      if (!timeStr) return '';

      try {
        // Check if the time is already in AM/PM format
        if (timeStr.toLowerCase().includes('am') || timeStr.toLowerCase().includes('pm')) {
          return timeStr; // Return as is if already in AM/PM format
        }

        const timeParts = timeStr.split(':');
        if (timeParts.length < 2) {
          console.warn('Invalid time format:', timeStr);
          return timeStr; // Return original if format is invalid
        }

        const hours = parseInt(timeParts[0]);
        const minutes = timeParts[1] || '00';

        if (isNaN(hours) || hours < 0 || hours > 23) {
          console.warn('Invalid hour value:', hours);
          return timeStr; // Return original if hour is invalid
        }

        const ampm = hours >= 12 ? 'pm' : 'am';
        const hour12 = hours % 12 || 12;

        return `${hour12}:${minutes} ${ampm}`;
      } catch (error) {
        console.error('Error formatting time:', error);
        return timeStr; // Return original time on error
      }
    },

    // Validate booking data before submission
    validateBookingData() {
      const errors = [];

      if (!this.bookingData.clinic || !this.bookingData.clinic.id) {
        errors.push('Please select a clinic');
      }

      if (!this.bookingData.services || this.bookingData.services.length === 0) {
        errors.push('Please select at least one service');
      }

      if (!this.bookingData.date) {
        errors.push('Please select an appointment date');
      }

      if (!this.bookingData.time) {
        errors.push('Please select an appointment time');
      }

      if (errors.length > 0) {
        console.error('Booking validation errors:', errors);
        alert('Please complete the following:\n• ' + errors.join('\n• '));
        return false;
      }

      return true;
    },

    // Reset component state
    resetComponentState() {
      this.isLoading = false;
      this.processingDetailsStep = false;
      this.isPaymentProcessing = false;
      this.appointmentId = null;
      this.paymentMethod = null;
    }
  }
};
</script>

<style scoped>
.kivi-booking-widget {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  max-width: 1000px;
  margin: 0 auto;
  color: #333;
  --primary-color: #7093e5;
  --primary-color-dark: #4367b9;
  --secondary-color: #f68685;
  --secondary-color-dark: #df504e;
  --black: #333;
  --dark-gray: #4b5563;
  --gray: #6b7280;
  --light-gray: #f9fafb;
  --white: #fff;
  --radius: 8px;
}

.kivi-booking-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
}

.kivi-booking-header {
  background-color: #fff;
}

/* Tailwind-like utility classes for the new header */
.border-b {
  border-bottom: 1px solid #e5e7eb;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.overflow-x-auto {
  overflow-x: auto;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.text-blue-600 {
  color: #2563eb;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-purple-600 {
  color: #9333ea;
}

.text-pink-600 {
  color: #db2777;
}

.text-green-600 {
  color: #16a34a;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-300 {
  color: #d1d5db;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-indigo-50 {
  background-color: #eef2ff;
}

.bg-purple-50 {
  background-color: #faf5ff;
}

.bg-pink-50 {
  background-color: #fdf2f8;
}

.bg-green-50 {
  background-color: #f0fdf4;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.text-sm {
  font-size: 0.875rem;
}

.font-medium {
  font-weight: 500;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.hidden {
  display: none;
}

@media (min-width: 768px) {
  .md\:inline-block {
    display: inline-block;
  }
}

.progress-container {
  position: relative;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  margin-top: 0.5rem;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color, #7093e5);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-indicator {
  position: absolute;
  top: -20px;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  transition: left 0.3s ease;
}

.kivi-booking-body {
  padding: 1.5rem;
}

.form-card {
  min-height: 300px;
}

.trust-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Additional Tailwind-like utility classes for the footer */
.justify-between {
  justify-content: space-between;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.border-t {
  border-top: 1px solid #e5e7eb;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.text-gray-600 {
  color: #4b5563;
}

.hover\:text-gray-900:hover {
  color: #111827;
}

.text-white {
  color: white;
}

.rounded-md {
  border-radius: 0.375rem;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.bg-pink-600 {
  background-color: #db2777;
}

.hover\:bg-pink-700:hover {
  background-color: #be185d;
}

.shield-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  color: #6b7280;
}

.badge-text {
  font-weight: 500;
}

@media (max-width: 768px) {
  .kivi-booking-body {
    padding: 1rem;
  }

  /* Responsive adjustments for the new header */
  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 640px) {
  /* Responsive adjustments for the new footer */
  .flex.justify-between.mt-6.pt-6.mx-5.mb-5.border-t {
    flex-direction: column;
    gap: 1rem;
  }

  .flex.justify-between.mt-6.pt-6.mx-5.mb-5.border-t button {
    width: 100%;
  }

  /* Responsive adjustments for the new header */
  .px-6 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .w-8 {
    width: 1.75rem;
  }

  .h-8 {
    height: 1.75rem;
  }

  .mx-3 {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
}
</style>
